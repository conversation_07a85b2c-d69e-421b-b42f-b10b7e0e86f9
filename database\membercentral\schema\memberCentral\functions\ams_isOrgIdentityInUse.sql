ALTER FUNCTION dbo.ams_isOrgIdentityInUse (@orgID INT, @orgIdentityID INT)
RETURNS bit
AS
BEGIN

	DECLARE @inUse bit = 0, @count int;

	set @count = 0;
	select @count = count(listID) FROM dbo.lists_lists WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(siteID) FROM dbo.sites WHERE orgID = @orgID and loginOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END
	
	set @count = 0;
	select @count = count(siteID) FROM dbo.sites WHERE orgID = @orgID and defaultOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(publicationID) FROM dbo.pub_publications WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(blastID) FROM dbo.email_emailBlasts WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(orgID) FROM dbo.organizations WHERE orgID = @orgID AND defaultOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(participantID) FROM seminarWeb.dbo.tblParticipants WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(consentListID) FROM platformMail.dbo.email_consentLists WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END
	
	set @count = 0;
	select @count = count(messageID) FROM platformMail.dbo.email_messages WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(itemID) FROM platformQueue.dbo.queue_completeSeminarReminder WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(storeID) FROM dbo.store WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	on_done:
	RETURN @inUse;

END
GO
