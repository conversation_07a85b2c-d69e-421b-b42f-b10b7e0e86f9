<cfcomponent output="no">

	<cffunction name="getGroup" access="public" output="false" returntype="struct">
		<cfargument name="groupID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.returnStruct.qryGroup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @groupID int = <cfqueryparam value="#arguments.groupID#" cfsqltype="CF_SQL_INTEGER">;
			DECLARE @siteAdminGroupID int, @isInSiteAdminGroupPath int;
			SELECT @siteAdminGroupID = groupID FROM dbo.ams_groups WHERE orgID = @orgID AND groupCode = 'SiteAdmins';
			SELECT @isInSiteAdminGroupPath = count(*) FROM dbo.cache_ams_recursiveGroups WHERE startGroupID = @groupID AND groupID = @siteAdminGroupID;

			select g.orgid, g.groupID, g.groupCode, g.groupName, g.groupDesc, g.parentGroupID, g.isSystemGroup, 
				g.allowManualAssignment, g.isProtected, g.alertIfPopulated, g.imageExt, g.groupPathExpanded as groupPath, 
				g.uid, g.groupBadgeBgColor, g.groupBadgeTxtColor,
				isNull((select isProtected from dbo.ams_groups where orgID = @orgID AND groupID = g.parentGroupID),0) as isParentGroupProtected,
				CASE WHEN fg.groupID IS NOT NULL THEN 1 ELSE 0 END AS isFeatured,
				(
					select count(rgr.ruleGroupID) 
				 	from dbo.ams_virtualGroupRuleGroups as rgr 
				 	inner join dbo.cache_ams_recursiveGroups as rg on rg.startGroupID = rgr.groupID
				 	where rg.groupID = g.groupID
				 ) as numRuleGroupsDeep,
				CASE WHEN @isInSiteAdminGroupPath > 0 THEN 1 ELSE 0 END as isInSiteAdminGroupPath
			from dbo.ams_groups as g
			LEFT OUTER JOIN dbo.ams_featuredGroups AS fg ON fg.orgID = @orgID 
				AND fg.groupID = g.groupID
			where g.orgID = @orgID
			and g.groupID = @groupID
			and g.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.returnStruct.qryGroup.recordcount>
			<cfset local.returnStruct.groupID = local.returnStruct.qryGroup.groupID>
			<cfset local.returnStruct.orgID = local.returnStruct.qryGroup.orgID>
		<cfelse>
			<cfset local.returnStruct.groupID = 0>
			<cfset local.returnStruct.orgID = arguments.orgID>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createGroup" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="groupCode" type="string" required="true">
		<cfargument name="groupName" type="string" required="true">
		<cfargument name="groupDesc" type="string" required="true">
		<cfargument name="allowManualAssignment" type="boolean" required="true">
		<cfargument name="parentGroupID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "groupID":0, "errmsg":"" }>

		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				<cfif len(arguments.groupCode)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.groupCode#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.groupName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.groupDesc#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowManualAssignment#">
				<cfif arguments.parentGroupID>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.parentGroupID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="" null="yes">
				</cfif>				
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.returnStruct.groupID">
			</cfstoredproc>				
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfif findNoCase('Group Name "#arguments.groupName#" already exists',cfcatch.detail)>
				<cfset local.returnStruct.errmsg = "Group Name already exists.">
			<cfelseif findNoCase('Group Code "#arguments.groupCode#" already exists',cfcatch.detail)>
				<cfset local.returnStruct.errmsg = "Group Code already exists.">
			<cfelse>
				<cfset local.returnStruct.errmsg = "There was a problem saving the data.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="saveGroup" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":true, "errmsg":"" }>
		<cfset local.objMemberSettingsAdmin = createObject("component","model.admin.membersettings.membersettingsadmin")>

		
		<cfif arguments.event.getValue('groupid') gt 0>
			<cftry>
				<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_updateGroup">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('groupID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">
					<cfif len(arguments.event.getValue('groupCode',''))>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('groupCode')#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="yes">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('groupName')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('groupDesc')#">
					<cfif val(arguments.event.getValue('parentGroupID',0)) gt 0>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('parentGroupID')#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('allowManualAssignment',1)#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('groupBadgeBgColor','')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('groupBadgeTxtColor','')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('grpBadgeColorToChildren',0)#">
				</cfstoredproc>

				<!--- Featured Group --->
				<cfif arguments.event.getValue('featuredGroup',0) NEQ arguments.event.getValue('oldFeaturedGroup',0)>
					<cfif arguments.event.getValue('featuredGroup',0) is 1>
						<cfset local.objMemberSettingsAdmin.addFeaturedGroup(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), groupID=arguments.event.getValue('groupID'))>
					<cfelse>
						<cfset local.objMemberSettingsAdmin.removeFeaturedGroup(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), groupID=arguments.event.getValue('groupID'))>
					</cfif>
				</cfif>

				<!--- alpha if desired --->
				<cfif arguments.event.getValue('chkAlphaSubGroups',0) is 1>
					<cfset alphabetizeSubGroups(groupID=arguments.event.getValue('groupID'), orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
				</cfif>

				<cfquery name="local.qryUpdateAlert" datasource="#application.dsn.membercentral.dsn#">
					set nocount on;

					declare @groupID int, @uid uniqueidentifier, @newuid uniqueidentifier;
					set @groupID = <cfqueryparam value="#arguments.event.getValue('groupID')#" cfsqltype="CF_SQL_INTEGER">;
					select @uid = [uid] from dbo.ams_groups where groupID = @groupID;
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.event.getTrimValue('grpUID',''))>
						set @newuid = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#trim(arguments.event.getTrimValue('grpUID'))#">;
						IF EXISTS (select groupID from dbo.ams_groups where uid = @newuid and groupID <> @groupID)
							set @newuid = @uid;
					<cfelse>
						set @newuid = @uid;
					</cfif>

					update dbo.ams_groups
					set alertIfPopulated = <cfqueryparam value="#arguments.event.getValue('alertIfPopulated',0)#" cfsqltype="CF_SQL_BIT">,
						[uid] = @newuid
					where groupID = @groupID

					set nocount off;
				</cfquery>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfif findNoCase("Group Name already exists",cfcatch.detail)>
					<cfset local.returnStruct.errmsg = "Group Name already exists.">
				<cfelseif findNoCase("Group Code already exists",cfcatch.detail)>
					<cfset local.returnStruct.errmsg = "Group Code already exists.">
				<cfelse>
					<cfset local.returnStruct.errmsg = "There was a problem saving the data.">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
				</cfif>
				<cfreturn local.returnStruct>
			</cfcatch>
			</cftry>

			<!--- group image settings --->
			<cfif arguments.event.getValue('groupid') gt 0 and Len(arguments.event.getValue('deleteImage',''))>
				<!--- get extension --->
				<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
					select imageExt 
					from dbo.ams_groups
					where groupID = <cfqueryparam value="#arguments.event.getValue('groupID')#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>

				<cffile action="delete" file="#application.paths.RAIDUserAssetRoot.path#common/groupImages/#arguments.event.getValue('groupID')#.#local.qryImageExt.imageExt#">

				<!--- clear extension --->
				<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
					update dbo.ams_groups
					set imageExt = null
					where groupID = <cfqueryparam value="#arguments.event.getValue('groupID')#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
			</cfif>

			<cfif arguments.event.getValue('groupid') gt 0 and len(arguments.event.getValue('groupImage',""))>
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
				<cfset local.destination = "#application.paths.RAIDUserAssetRoot.path#common/groupImages/">
				<cffile action="upload" filefield="groupImage" destination="#local.strFolder.folderPath#" nameConflict="makeUnique" result="local.upload">
				
				<cfif listFindNoCase("png,jpg,jpeg,gif",local.upload.ServerFileExt)>
					<cffile action="move" destination="#local.destination##arguments.event.getValue('groupID')#.#local.upload.ServerFileExt#" source="#local.upload.ServerDirectory#/#local.upload.ServerFile#">

					<!--- record extension --->
					<cfquery name="local.qryImageExt" datasource="#application.dsn.membercentral.dsn#">
						update dbo.ams_groups
						set imageExt = <cfqueryparam value="#local.upload.ServerFileExt#" cfsqltype="CF_SQL_VARCHAR">
						where groupID = <cfqueryparam value="#arguments.event.getValue('groupID')#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
				<cfelse>
					<cffile action="delete" file="#local.destination##arguments.event.getValue('groupID')#.#local.upload.ServerFileExt#">
				</cfif>
			</cfif>

			<!--- handle protected group setting --->
			<cfif arguments.event.getValue('mc_admintoolInfo.myRightsSite.manageProtectedGroups') is 1>
				<!--- if group or subgroups have assignment rules, dont allow it to be protected. --->
				<cfif arguments.event.getValue('isProtected',0) is 1>
					<cfquery name="local.qryCheckRuleGroups" datasource="#application.dsn.membercentral.dsn#">
						select count(rgr.ruleGroupID) as rgCount
						from dbo.ams_virtualGroupRuleGroups as rgr
						inner join dbo.cache_ams_recursiveGroups as rg on rg.startGroupID = rgr.groupID
						where rg.groupID = <cfqueryparam value="#arguments.event.getValue('groupID')#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
					<cfif local.qryCheckRuleGroups.rgCount gt 0>
						<cfset arguments.event.setValue('isProtected',0)>
					</cfif>
				</cfif>

				<!--- if group is now NOT protected, only change is to mark group as such. --->
				<cfif arguments.event.getValue('isProtected',0) is not 1>
					<cfquery name="local.qryProtected" datasource="#application.dsn.membercentral.dsn#">
						update dbo.ams_groups
						set isProtected = 0, 
							allowManualAssignment = <cfqueryparam value="#arguments.event.getValue('allowManualAssignment',1)#" cfsqltype="CF_SQL_BIT">
						where groupID = <cfqueryparam value="#arguments.event.getValue('groupID')#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>

				<!--- if group is now protected, need to mark this group and all subgroups as protected. --->
				<cfelse>
					<cfquery name="local.qryProtected" datasource="#application.dsn.membercentral.dsn#">
						update g
						set g.isProtected = 1,
							g.allowManualAssignment = 1
						from dbo.ams_groups as g
						inner join dbo.cache_ams_recursiveGroups as rg on g.groupID = rg.startGroupID
						where rg.groupID = <cfqueryparam value="#arguments.event.getValue('groupID')#" cfsqltype="CF_SQL_INTEGER">
					</cfquery>
				</cfif>
			</cfif>								
		</cfif>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="addGroupMember" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="mcproxy_memberID" type="numeric" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasRightsForGroupAction(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, action="addGroupMember", groupID=arguments.groupID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_createMemberGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
	
		<cfreturn local.data>
	</cffunction>
		
	<cffunction name="removeGroupMember" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasRightsForGroupAction(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, action="removeGroupMember", groupID=arguments.groupID, memberID=arguments.memberID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_deleteMemberGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.GroupID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="removeGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasRightsForGroupAction(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, action="removeGroup", groupID=arguments.groupID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_deleteGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.groupID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="moveGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">
		<cfargument name="parentGroupID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasRightsForGroupAction(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, action="moveGroup", groupID=arguments.groupID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_moveGroup">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">
				<cfif arguments.parentGroupID gt 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.parentGroupID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="yes">
				</cfif>
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="moveGroupUsage" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="fgid" type="numeric" required="yes">
		<cfargument name="tgid" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif arguments.fgid is 0 or arguments.tgid is 0>
				<cfthrow message="Invalid group specified">
			</cfif>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_addToMoveUsageGroupQueue">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fgid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.tgid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasRightsForGroupAction" access="private" output="false" returntype="boolean">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="siteCode" type="string" required="yes">
		<cfargument name="action" type="string" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>
		<cfset local.siteSRID = application.objSiteInfo.mc_siteInfo[arguments.siteCode].siteSiteResourceID>
		<cfset local.tmpSiteRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfquery name="local.qryGroupInfo" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT g.allowManualAssignment, g.isProtected, g.isSystemGroup, isnull(mg.isManualDirect,0) AS isManualDirect
			FROM dbo.ams_groups as g
			LEFT OUTER JOIN dbo.cache_members_groups as mg on mg.groupID = g.groupID
				AND mg.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
			WHERE g.orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">
			AND g.groupID = <cfqueryparam value="#arguments.groupID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfswitch expression="#arguments.action#">
			<cfcase value="addGroupMember">
				<cfif local.qryGroupInfo.allowManualAssignment EQ 1 AND (local.qryGroupInfo.isProtected eq 0 OR local.tmpSiteRights.manageProtectedGroups is 1)>
					<cfset local.hasRights = true>
				</cfif>
			</cfcase>
			<cfcase value="removeGroupMember">
				<cfif local.qryGroupInfo.isManualDirect EQ 1 AND (local.qryGroupInfo.isProtected eq 0 OR local.tmpSiteRights.manageProtectedGroups is 1)>
					<cfset local.hasRights = true>
				</cfif>
			</cfcase>
			<cfcase value="moveGroup">
				<cfif local.qryGroupInfo.isSystemGroup EQ 0 AND (local.qryGroupInfo.isProtected eq 0 OR local.tmpSiteRights.manageProtectedGroups is 1)>
					<cfset local.hasRights = true>
				</cfif>
			</cfcase>
			<cfcase value="removeGroup">
				<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='GroupAdmin',siteID=arguments.siteID)>
				<cfset local.tmpGroupAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

				<cfif local.qryGroupInfo.isSystemGroup EQ 0 AND local.tmpGroupAdminRights.DeleteGroup is 1>
					<cfset local.hasRights = true>
				</cfif>
			</cfcase>
			<cfcase value="copyGroupRule,saveGroupRule,triggerGroupRuleConditions">
				<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='GroupAdmin', siteID=arguments.siteID)>
				<cfset local.tmpGroupAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

				<cfif local.tmpGroupAdminRights.ViewGroup is 1 AND local.qryGroupInfo.isSystemGroup EQ 0>
					<cfset local.hasRights = true>
				</cfif>
			</cfcase>
			<cfcase value="getGroupNameAndPath">
				<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='GroupAdmin', siteID=arguments.siteID)>
				<cfset local.tmpGroupAdminRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

				<cfif local.tmpGroupAdminRights.ViewGroup is 1>
					<cfset local.hasRights = true>
				</cfif>
			</cfcase>
		</cfswitch>

		<cfreturn local.hasRights>
	</cffunction>

	<cffunction name="getGroups" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="excludeGroupID" type="string" required="true">
		<cfargument name="hideProtected" type="boolean" required="true">

		<cfset var qryGroups = "">

		<cfquery name="qryGroups" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;			

			<cfif arguments.excludeGroupID NEQ 'Default'>
				DECLARE @excludeGroupID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.excludeGroupID#">;
				DECLARE @groupPathSortOrder varchar(max);

				SELECT @groupPathSortOrder = groupPathSortOrder FROM dbo.ams_groups WHERE groupID = @excludeGroupID;
			</cfif>

			SELECT groupID, groupName, groupPathSortOrder AS thePath, groupPathExpanded AS thePathExpanded
			from dbo.ams_groups 
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orgID#"> 
			AND status <> 'D'   
			AND hideOnGroupLists = 0 
			AND (isSystemGroup = 0 OR (isSystemGroup = 1 and allowManualAssignment = 1))
			<cfif arguments.hideProtected>
				AND isProtected = 0
			</cfif>
			<cfif arguments.excludeGroupID NEQ 'Default'>
				AND left(groupPathSortOrder,len(@groupPathSortOrder)) <> @groupPathSortOrder
			</cfif>
			order by groupPathSortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryGroups>
	</cffunction>
	
	<cffunction name="doMoveGroupOrder" access="public" output="false" returntype="struct" hint="Re-Order Group">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="parentGroupID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">
		
		<cfset var local = structNew()>
			
		<cftry>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_moveGroupOrder">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">
				<cfif arguments.parentGroupID gt 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.parentGroupID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="" null="yes">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="alphabetizeSubGroups" access="public" output="false" returntype="void">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		
		<cfset var qryAlphabetizeSubGroups = "">

		<cftry>
			<cfquery name="qryAlphabetizeSubGroups" datasource="#application.dsn.membercentral.dsn#">
				
				DECLARE @orgID int, @groupID int;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				SET @groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">;

				UPDATE g
				SET g.sortOrder = x.theRow
				FROM dbo.ams_groups as g
				INNER JOIN (
					SELECT g2.groupID, ROW_NUMBER() OVER (ORDER BY g2.groupName) AS theRow
					FROM dbo.ams_groups as g2
					WHERE g2.parentGroupID = @groupID
				) x ON x.groupID = g.groupID
				WHERE g.parentGroupID = @groupID;
				
				EXEC dbo.ams_reorderGroups @orgID=@orgID, @parentGroupID=@groupID;
			</cfquery>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
	</cffunction>
	
	<cffunction name="doRefreshGroups" access="public" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="hideProtected" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.groups = getGroups(orgID=arguments.orgID, excludeGroupID='Default', hideProtected=arguments.hideProtected)>
		
		<cfset local.depth = arrayNew(1)>
		<cfloop query="local.groups">
			<cfset arrayAppend(local.depth,listLen(local.groups.thePath,"."))>
		</cfloop>
		
		<cfset QueryAddColumn(local.groups, "depth", "integer", local.depth)> 
		
		<cfreturn serializejson(local.groups)>
	</cffunction>

	<cffunction name="getGroupMembersFromFilters" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="groupAssignment" type="string" required="true">
		<cfargument name="hideInactive" type="boolean" required="true">
		<cfargument name="recordTypeIDList" type="string" required="true">
		<cfargument name="fLastName" type="string" required="true">
		<cfargument name="fCompany" type="string" required="true">
		<cfargument name="fAssociatedMemberID" type="numeric" required="true">
		<cfargument name="fAssociatedGroupID" type="numeric" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="fieldsetID" type="numeric" required="false" default="0">
		<cfargument name="emailTagTypeID" type="numeric" required="false" default="0">
		<cfargument name="canEditProtected" type="boolean" required="false" default="0">
		<cfargument name="start" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">

		<cfset var local = structNew()>

		<cfif arguments.mode is 'grpTabEmailGrid'>
			<cfset local.orderby = "(mActive.lastname + mActive.firstname + mActive.membernumber) #arguments.direct#">
		</cfif>

		<cfquery name="local.qryGroupMembers" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpGroupMembersSearch') IS NOT NULL 
					DROP TABLE ##tmpGroupMembersSearch;
				CREATE TABLE ##tmpGroupMembersSearch (memberID int PRIMARY KEY, autoID bigint, memberNumber varchar(50));

				DECLARE @orgID int, @groupID int, @fLastName varchar(75), @fCompany varchar(200), @fAssociatedMemberID int, @fAssociatedGroupID int;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				SET @groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">;
				<cfif arguments.fLastName NEQ ''>
					SET @fLastName = replace(<cfqueryparam value="#arguments.fLastName#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
				</cfif>
				<cfif arguments.fCompany NEQ ''>
					SET @fCompany = replace(<cfqueryparam value="#arguments.fCompany#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
				</cfif>
				<cfif arguments.fAssociatedMemberID gt 0>
					SET @fAssociatedMemberID = <cfqueryparam value="#arguments.fAssociatedMemberID#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.fAssociatedGroupID gt 0>
					SET @fAssociatedGroupID = <cfqueryparam value="#arguments.fAssociatedGroupID#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>

				INSERT INTO ##tmpGroupMembersSearch
				SELECT m.memberID, mg.autoID, m.memberNumber
				FROM dbo.cache_members_groups as mg
				INNER JOIN dbo.ams_members as m on m.memberid = mg.memberid AND m.memberid = m.activememberID
					<cfif arguments.fAssociatedMemberID gt 0>
						AND m.memberID = @fAssociatedMemberID
					</cfif>
					<cfif arguments.fLastName NEQ ''>
						AND m.lastname LIKE '%'+@fLastName+'%' ESCAPE('\')
					</cfif>
					<cfif arguments.fCompany NEQ ''>
						AND m.company LIKE '%'+@fCompany+'%' ESCAPE('\')
					</cfif>
					AND mg.groupid = @groupID
				INNER JOIN dbo.ams_groups as g on g.groupID = mg.groupID
				<cfif arguments.fAssociatedGroupID gt 0>
					INNER JOIN dbo.cache_members_groups as mgAssoc on mgAssoc.memberID = m.memberID
						AND mgAssoc.groupid = @fAssociatedGroupID
				</cfif>
				WHERE m.orgID = @orgID
				AND m.isProtected = 0
				<cfif len(arguments.groupAssignment)>
					AND ( 1 = 0
					<cfif listFindNoCase(arguments.groupAssignment,"manual")>
						OR mg.isManualDirect = 1
					</cfif>
					<cfif listFindNoCase(arguments.groupAssignment,"virtual")>
						OR mg.isVirtualDirect = 1
					</cfif>
					<cfif listFindNoCase(arguments.groupAssignment,"indirect")>
						OR ( mg.isManualIndirect = 1 OR mg.isVirtualIndirect = 1 )
					</cfif> )
				</cfif>
				<cfif arguments.hideInactive is 1>
					AND m.status = 'A'
				<cfelse>
					AND m.status <> 'D'
				</cfif>
				<cfif len(arguments.recordTypeIDList)>
					and m.recordTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.recordTypeIDList#">)
				</cfif>
				<cfif arguments.mode eq "exportMemberPhotos">
					and m.hasMemberPhoto = 1
				</cfif>;

				<cfif arguments.mode eq "exportMemberPhotos">
					select memberNumber 
					from ##tmpGroupMembersSearch;
				<cfelseif arguments.mode eq "grpTabEmailGrid">
					DECLARE @posStart int, @posStartAndCount int, @emailTagTypeID int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.start#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.count#" cfsqltype="CF_SQL_INTEGER">;
					SET @emailTagTypeID = <cfqueryparam value="#arguments.emailTagTypeID#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tmpGroupMembers') IS NOT NULL 
						DROP TABLE ##tmpGroupMembers;
					CREATE TABLE ##tmpGroupMembers (memberID int, memberFirstname varchar(75), memberLastname varchar(75), membernumber varchar(50),
						memberCompany varchar(200), memberEmail varchar(400), row int);

					INSERT INTO ##tmpGroupMembers
					select distinct mActive.memberID, mActive.firstname, mActive.lastname, mActive.membernumber, mActive.company, me.email, 
						ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
					from ##tmpGroupMembersSearch as tmp
					inner join dbo.ams_members as m on m.orgID = @orgID
						and m.memberID = tmp.memberID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.memberID = mActive.memberID
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
						and metag.memberID = me.memberID
						and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
						and metagt.emailTagTypeID = metag.emailTagTypeID
						and metagt.emailTagTypeID = @emailTagTypeID;

					select @totalCount = @@ROWCOUNT;

					select @groupID as groupID, memberID, memberFirstname, memberLastname, membernumber, memberCompany, memberEmail, @totalCount as totalCount
					from ##tmpGroupMembers
					where row > @posStart
					and row <= @posStartAndCount
					order by row;

					IF OBJECT_ID('tempdb..##tmpGroupMembers') IS NOT NULL 
						DROP TABLE ##tmpGroupMembers;
				<cfelseif arguments.mode is 'grpTabEmail'>
					DECLARE @membersWithEmail int, @emailTagTypeID int;
					SET @emailTagTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailTagTypeID#">;

					select @membersWithEmail = count(*)
					from ##tmpGroupMembersSearch as tmp
					inner join dbo.ams_members as m on m.orgID = @orgID
						and m.memberID = tmp.memberID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.memberID = mActive.memberID
						and me.email <> ''
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID
						and metag.memberID = me.memberID
						and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
						and metagt.emailTagTypeID = metag.emailTagTypeID
						and metagt.emailTagTypeID = @emailTagTypeID;

					select memberID, @membersWithEmail as membersWithEmail
					from ##tmpGroupMembersSearch;
				<cfelseif arguments.mode is 'viewMembers'>
					DECLARE @posStart int, @posStartAndCount int, @resultsFSID int, @totalCount int, @isProtected bit, @canEditProtected bit, @outputFieldsXML xml;
					SET @posStart = <cfqueryparam value="#arguments.start#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.count#" cfsqltype="CF_SQL_INTEGER">;
					SET @resultsFSID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldsetID#">;
					SET @canEditProtected = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.canEditProtected#">;
					SELECT @totalCount = count(*) from ##tmpGroupMembersSearch;
					select @isProtected = isProtected from dbo.ams_groups where groupID = @groupID;

					IF OBJECT_ID('tempdb..##tmpGrpMembers') IS NOT NULL 
						DROP TABLE ##tmpGrpMembers;
					IF OBJECT_ID('tempdb..##tmpGrpMembersWithFS') IS NOT NULL 
						DROP TABLE ##tmpGrpMembersWithFS;
					CREATE TABLE ##tmpGrpMembers (memberID int INDEX IX_tmpGroupMembers_memberID, lastname varchar(75), firstname varchar(75), 
						membernumber varchar(50), MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200), isManualDirect bit, 
						isManualIndirect bit, isVirtualDirect bit, isVirtualIndirect bit, canRemove bit, mc_row int, manualDateAdded datetime,
						manualAddedBy varchar(150));
					CREATE TABLE ##tmpGrpMembersWithFS (MFSAutoID int IDENTITY(1,1) not null);

					insert into ##tmpGrpMembers (memberID, lastname, firstname, membernumber, MCAccountStatus, hasMemberPhotoThumb, company, 
						isManualDirect, isManualIndirect, isVirtualDirect, isVirtualIndirect, canRemove, mc_row)
					select distinct m.memberid, m.lastname, m.firstname, m.membernumber, m.status, m.hasMemberPhotoThumb, m.company,
						mg.isManualDirect, mg.isManualIndirect, mg.isVirtualDirect, mg.isVirtualIndirect, 
						case
						when @isProtected = 0 and mg.isManualDirect = 1 then 1
						when @isProtected = 1 and mg.isManualDirect = 1 and @canEditProtected = 1 then 1
						else 0
						end as canRemove,
						ROW_NUMBER() OVER (order by m.lastname, m.firstname, m.membernumber) as mc_row
					from ##tmpGroupMembersSearch as tmp
					inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mg.autoID = tmp.autoID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmp.memberID;

					delete from ##tmpGrpMembers
					where mc_row not between @posStart and @posStartAndCount;

					UPDATE tmp
					SET tmp.manualDateAdded = mg.dateAdded,
						tmp.manualAddedBy = concat(m2.firstname,' ',m2.lastname)
					FROM ##tmpGrpMembers as tmp
					INNER JOIN dbo.ams_memberGroups as mg on mg.orgID = @orgID
						and mg.memberID = tmp.memberID
						and mg.groupID = @groupID
						and mg.addedByMemberID is not null
					INNER JOIN dbo.ams_members as m on m.orgID in (1,@orgID)
						and m.memberID = mg.addedByMemberID
					INNER JOIN dbo.ams_members as m2 on m2.orgID in (1,@orgID)
						and m2.memberID = m.activeMemberID
					WHERE tmp.isManualDirect = 1;

					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@resultsFSID, @existingFields='m_lastname,m_firstname,m_membernumber,m_company',
						@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpGrpMembers', @membersResultTableName='##tmpGrpMembersWithFS',
						@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;

					-- return @outputFieldsXML in only the first row to reduce the query payload
					SELECT *, case when mc_row = @posStart then @outputFieldsXML else null end as mc_outputFieldsXML
					FROM (
						SELECT tmp.lastname, tmp.firstname, tmp.membernumber, tmp.MCAccountStatus, tmp.hasMemberPhotoThumb, tmp.company,
							tmp.isManualDirect, tmp.isManualIndirect, tmp.isVirtualDirect, tmp.isVirtualIndirect, tmp.canRemove,
							@totalCount as mc_totalMatches, tmp.mc_row, tmp.manualDateAdded, tmp.manualAddedBy, tmpM.*
						FROM ##tmpGrpMembers as tmp
						INNER JOIN ##tmpGrpMembersWithFS as tmpM on tmpM.memberID = tmp.memberID
					) tmp
					ORDER BY mc_row;

					IF OBJECT_ID('tempdb..##tmpGrpMembers') IS NOT NULL 
						DROP TABLE ##tmpGrpMembers;
					IF OBJECT_ID('tempdb..##tmpGrpMembersWithFS') IS NOT NULL 
						DROP TABLE ##tmpGrpMembersWithFS;
				<cfelse>
					select memberID 
					from ##tmpGroupMembersSearch;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpGroupMembersSearch') IS NOT NULL 
					DROP TABLE ##tmpGroupMembersSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryGroupMembers>
	</cffunction>

	<cffunction name="getGroupMembers" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="memPageNum" type="numeric" required="true">
		<cfargument name="perPageResults" type="numeric" required="true">
		<cfargument name="canEditProtected" type="boolean" required="true">
		<cfargument name="showMemberPhoto" type="boolean" required="true">
		<cfargument name="groupAssignment" type="string" required="true">
		<cfargument name="hideInactive" type="boolean" required="true">
		<cfargument name="recordTypeIDList" type="string" required="true">
		<cfargument name="fLastName" type="string" required="true">
		<cfargument name="fCompany" type="string" required="true">
		<cfargument name="fAssociatedMemberID" type="numeric" required="true">
		<cfargument name="fAssociatedGroupID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		
		<!--- preserve case --->
		<cfset local.returnStruct['success'] = true>
		<cfset local.returnStruct['arrgroupmembers'] = arrayNew(1)>
		<cfset local.returnStruct['strpage'] = structNew()>
		<cfset local.returnStruct['strpage']['rowsize'] = arguments.perPageResults>
		<cfset local.returnStruct['strpage']['currpage'] = arguments.memPageNum>
		<cfset local.returnStruct['strpage']['nextpage'] = arguments.memPageNum + 1>
		<cfset local.returnStruct['strpage']['prevpage'] = arguments.memPageNum - 1>
		<cfset local.returnStruct['strpage']['currcountstart'] = 0>
		<cfset local.returnStruct['strpage']['currcountstop'] = 0>
		<cfset local.returnStruct['strpage']['totalcount'] = 0>
		<cfset local.returnStruct['strpage']['showmemberphoto'] = arguments.showMemberPhoto>

		<!--- get result fields --->
		<cfset local.objMFS = CreateObject("component","model.system.platform.memberFieldsets")>
		<cfset local.MemberAdminSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].memberAdminSiteResourceID>
		<cfset local.qryResultsFieldsetID = getLocatorFieldsetID(siteResourceID=local.MemberAdminSRID,area='results')>
		
		<cfset local.qryFilteredGroupMembers = getGroupMembersFromFilters(orgID=arguments.mcproxy_orgID, groupID=arguments.groupID, 
			groupAssignment=arguments.groupAssignment, hideInactive=arguments.hideInactive, recordTypeIDList=arguments.recordTypeIDList, 
			fLastName=arguments.fLastName, fCompany=arguments.fCompany, fAssociatedMemberID=arguments.fAssociatedMemberID, 
			fAssociatedGroupID=arguments.fAssociatedGroupID, mode='viewMembers', fieldSetID=local.qryResultsFieldsetID.fieldsetID,
			canEditProtected=arguments.canEditProtected, start="#(local.returnStruct.strpage.prevPage*local.returnStruct.strpage.rowsize)+1#", 
			count=local.returnStruct.strpage.rowsize)>
		<cfif not local.qryFilteredGroupMembers.recordCount>
			<cfreturn local.returnStruct>
		</cfif>
		
		<cfif local.qryFilteredGroupMembers.recordCount>
			<cfset local.objMemberAdmin = createObject("component","model.admin.members.memberAdmin")>

			<cfset local.returnStruct['strpage']['totalcount'] = local.qryFilteredGroupMembers.mc_totalMatches>
			<cfif local.returnStruct.strpage.currpage eq 1>
				<cfset local.returnStruct['strpage']['currcountstart'] = 1>
				<cfset local.returnStruct['strpage']['currcountstop'] 	= local.returnStruct.strpage.rowsize>
			<cfelse>
				<cfset local.returnStruct['strpage']['currcountstart'] = local.returnStruct.strpage.rowSize * (local.returnStruct.strpage.currpage - 1) + 1>
				<cfset local.returnStruct['strpage']['currcountstop'] 	= local.returnStruct.strpage.rowsize * (local.returnStruct.strpage.currpage - 1) + local.qryFilteredGroupMembers.recordCount>
			</cfif>
			<cfset local.returnStruct['strpage']['nextcountstart'] = local.returnStruct.strpage.currPage *  local.returnStruct.strpage.rowsize + 1>

			<cfset local.xmlResultFields = local.qryFilteredGroupMembers.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = local.objMFS.getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
			<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
			<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
			<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
			<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>
			
			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.mcproxy_orgID, includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>
						
			<cfloop query="local.qryFilteredGroupMembers">
				<cfset local.tmpStr = structNew()>

				<cfset local.tmpStr['memberid'] = local.qryFilteredGroupMembers.memberID>
				<cfset local.tmpStr['company'] = local.qryFilteredGroupMembers.company>
				<cfset local.tmpStr['mcaccountstatus'] = local.qryFilteredGroupMembers.MCAccountStatus>
				<cfset local.tmpStr['isManualDirect'] = local.qryFilteredGroupMembers.isManualDirect>
				<cfset local.tmpStr['isManualIndirect'] = local.qryFilteredGroupMembers.isManualIndirect>
				<cfset local.tmpStr['manualDateAdded'] = dateformat(local.qryFilteredGroupMembers.manualDateAdded,"m/d/yyyy")>
				<cfset local.tmpStr['manualAddedBy'] = local.qryFilteredGroupMembers.manualAddedBy>
				<cfset local.tmpStr['isVirtualDirect'] = local.qryFilteredGroupMembers.isVirtualDirect>
				<cfset local.tmpStr['isVirtualIndirect'] = local.qryFilteredGroupMembers.isVirtualIndirect>
				<cfset local.tmpStr['canRemove'] = local.qryFilteredGroupMembers.canRemove>
				<cfset local.tmpStr['hasPhoto'] = local.qryFilteredGroupMembers.hasMemberPhotoThumb>
				<cfif local.qryFilteredGroupMembers.hasMemberPhotoThumb>
					<cfset local.tmpStr['memberphoto'] = local.qryFilteredGroupMembers.membernumber & ".jpg">
				<cfelse>
					<cfset local.tmpStr['memberphoto'] = "">
				</cfif>
				
				<cfset local.arrAllClassifications = local.objMemberAdmin.getClassificationsForMemberIDList(siteResourceID=local.MemberAdminSRID, memberIDList=valueList(local.qryFilteredGroupMembers.memberid))>

				<cfset local.tmpStr['mc_combinedName'] = local.qryFilteredGroupMembers['Extended MemberNumber'][local.qryFilteredGroupMembers.currentrow]>

				<!--- combine address fields if there are any --->
				<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
				<cfif StructCount(local.thisMem_mc_combinedAddresses)>
					<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
						<cfsavecontent variable="local.thisATFull">
							<cfoutput>
							<cfif left(local.thisATID,1) eq "t">
								<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
								<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfelse>
								<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
								<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
							</cfif>

							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>#local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>#local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>#local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>#local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]#</cfif>
							<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
							<cfif arrayLen(local.tmp2) is 1 and len(local.qryFilteredGroupMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>, #local.qryFilteredGroupMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]# </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])> #local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>#local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]# County<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])> #local.qryFilteredGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
							<cfloop array="#local.tmp#" index="local.thisPT">
								<cfif len(local.qryFilteredGroupMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>
									<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.qryFilteredGroupMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]#</div>
								</cfif>
							</cfloop>
							</cfoutput>
						</cfsavecontent>
						<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
						<cfif left(local.thisATfull,2) eq ", ">
							<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
						</cfif>
						<cfif len(local.thisATfull)>
							<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
						<cfelse>
							<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
						</cfif>
					</cfloop>

					<cfsavecontent variable="local.thisMemCombinedAddress">
						<cfoutput>
						<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
							<div><b style="font-size:95%; margin-right:15px;">#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_combinedAddresses'] = rereplace(replace(replace(local.thisMemCombinedAddress,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_combinedAddresses'] = "">
				</cfif>

				<!--- get recordtype if available --->
				<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryFilteredGroupMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>
					<cfset local.tmpStr['mc_recordType'] = local.qryFilteredGroupMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_recordType'] = "">
				</cfif>
				
				<!--- get membertypeid if available --->
				<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryFilteredGroupMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>
					<cfset local.tmpStr['mc_memberType'] = local.qryFilteredGroupMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_memberType'] = "">
				</cfif>
				
				<!--- get status if available --->
				<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryFilteredGroupMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>
					<cfset local.tmpStr['mc_memberStatus'] = local.qryFilteredGroupMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_memberStatus'] = "">
				</cfif>

				<!--- get last login date if available --->
				<cfif arrayLen(local.LastLoginDateInFS) is 1>
					<cfif len(local.qryFilteredGroupMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow])>
						<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(local.qryFilteredGroupMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupMembers.currentrow],"m/d/yy")#'>
					<cfelse>
						<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="dim"><i>none</i></span>'>
					</cfif>
				<cfelse>
					<cfset local.tmpStr['mc_lastlogin'] = "">
				</cfif>				

				<cfif local.qryOutputFieldsForLoop.recordCount>
					<cfsavecontent variable="local.thisMemExtraInfo">
						<cfoutput>
						<cfloop query="local.qryOutputFieldsForLoop">
							<cfset local.currValue = local.qryFilteredGroupMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryFilteredGroupMembers.currentrow]>
							<cfif len(local.currValue)>
								<div>
									#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
									<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
										#dollarFormat(local.currValue)#
									<cfelse>
										<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
											<cfcase value="DATE">
												#dateFormat(local.currValue,"m/d/yyyy")#
											</cfcase>
											<cfcase value="STRING,DECIMAL2,INTEGER">
												#local.currValue#
											</cfcase>
											<cfcase value="BIT">
												#YesNoFormat(local.currValue)#
											</cfcase>
										</cfswitch>
									</cfif>
								</div>
							</cfif>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_extraInfo'] = rereplace(replace(replace(local.thisMemExtraInfo,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_extraInfo'] = "">
				</cfif>
				
				<cfif isArray(local.arrAllClassifications) and arrayLen(local.arrAllClassifications)>
					<cfsavecontent variable="local.classifications">
						<cfoutput>
						<cfloop array="#local.arrAllClassifications#" index="local.currentClass">
							<cfset local.qryClass = local.currentClass.qryClass>
							<cfquery name="local.classifications" dbtype="query">
								select groupName
								from [local].qryClass
								where memberID = #local.qryFilteredGroupMembers.memberid#
								order by groupName
							</cfquery>
							<cfif local.classifications.recordCount>					
								<div class="mb-1"><span class="badge badge-info">#replace(local.currentClass.name,'_',' ','ALL')#</span></div>
								<div>
									<cfloop query="local.classifications">
										- #local.classifications.groupName# <br>
									</cfloop>
								</div>
							</cfif>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['classifications'] = rereplace(replace(replace(local.classifications,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['classifications'] = "">
				</cfif>

				<cfset arrayAppend(local.returnStruct['arrgroupmembers'],local.tmpStr)>
			</cfloop>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getLocatorFieldsetID" access="private" output="no" returntype="query">
		<cfargument name="siteresourceID" type="numeric" required="yes">
		<cfargument name="area" type="string" required="yes">
		
		<cfset var qryFieldSet = "">
		<cfquery name="qryFieldSet" datasource="#application.dsn.membercentral.dsn#">
			SELECT top 1 mfu.fieldsetID, mfs.fieldsetName, mfs.nameFormat, mfs.showHelp
			FROM dbo.ams_memberFieldUsage as mfu
			inner join dbo.ams_memberFieldSets mfs on mfs.fieldsetID = mfu.fieldsetID
			where mfu.siteResourceID = <cfqueryparam value="#arguments.siteresourceID#" cfsqltype="CF_SQL_INTEGER">
			and mfu.area = <cfqueryparam value="#arguments.area#" cfsqltype="CF_SQL_VARCHAR">
			order by mfu.fieldsetorder, mfu.fieldsetid
		</cfquery>
	
		<cfreturn qryFieldSet>
	</cffunction>

	<cffunction name="getGroupMemberDataForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<!--- verify and return group memberID --->
		<cfquery name="local.retStruct.qryData" datasource="#application.dsn.membercentral.dsn#">
			select m.memberID
			from dbo.cache_members_groups as mg
			inner join dbo.ams_members as m on m.memberID = mg.memberID and m.memberID = m.activememberID
			inner join dbo.ams_groups as g on g.groupID = mg.groupID
			where mg.groupID = <cfqueryparam value="#arguments.groupID#" cfsqltype="CF_SQL_INTEGER">
			and mg.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfset local.retStruct.extendedLinkedMergeCode = "">
		<cfset local.retStruct.arrResTypeMergeCodes = arrayNew(1)>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getFilteredGroupMembersForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='GroupAdmin', catTreeCode='ETGROUPS', extendedLinkedMergeCode='', extraMergeTagList='', errorCode='' }>

		<cfset local.qryGroupMembers = getGroupMembersFromFilters(orgID=arguments.event.getValue('mc_siteinfo.orgid'), 
					groupID=arguments.event.getValue('groupid'), groupAssignment=arguments.event.getValue('grpAssignment',''), 
					hideInactive=arguments.event.getValue('grpHideInactive',0), recordTypeIDList=arguments.event.getValue('grpMemberRecordType',''), 
					fLastName=arguments.event.getValue('fLastName',''), fCompany=arguments.event.getValue('fCompany',''), 
					fAssociatedMemberID=arguments.event.getValue('fAssociatedMemberID',0), fAssociatedGroupID=arguments.event.getValue('fAssociatedGroupID',0), 
					mode='grpTabEmail', emailTagTypeID=arguments.event.getValue('emailTagType',0))>

		<cfif local.qryGroupMembers.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		<cfelse>
			<cfset local.retStruct.itemIDList = "#arguments.event.getValue('groupid')#|#valueList(local.qryGroupMembers.memberID)#">
		</cfif>

		<!--- no email ids defined --->
		<cfif val(local.qryGroupMembers.membersWithEmail) is 0>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getGroupsFromFilters" access="public" output="false" returntype="any">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="reportFileName" type="string" required="false">

		<cfset var local = structNew()>
		<cfset local.filter_groupName = arguments.event.getTrimValue('fGroupName','')>
		<cfset local.filter_groupDesc = arguments.event.getTrimValue('fGroupDesc','')>
		<cfset local.filter_groupCountLow = int(val(arguments.event.getTrimValue('fGroupCountLow',0)))>
		<cfset local.filter_groupCountHigh = int(val(arguments.event.getTrimValue('fGroupCountHigh',0)))>
		<cfset local.filter_allowManualAssignment = arguments.event.getTrimValue('allowManualAssignment','')>
		<cfset local.filter_noRules = arguments.event.getTrimValue('fGrpAlert_noRules','')>
		<cfset local.filter_pendingRV = arguments.event.getTrimValue('fGrpAlert_pendingRV','')>
		<cfset local.filter_groupUID = arguments.event.getTrimValue('fGroupUID','')>
		<cfset local.filter_featuredGroup = arguments.event.getTrimValue('allowFeaturedGroup','')>

		<cfquery name="local.qryGroups" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				IF OBJECT_ID('tempdb..##groupCount') IS NOT NULL
					DROP TABLE ##groupCount;
				IF OBJECT_ID('tempdb..##tmpGroups') IS NOT NULL
					DROP TABLE ##tmpGroups;
				IF OBJECT_ID('tempdb..##tmpAlertGroups') IS NOT NULL 
					DROP TABLE ##tmpAlertGroups;
				CREATE TABLE ##groupCount (groupID int PRIMARY KEY, memberCount int);
				CREATE TABLE ##tmpGroups (groupID int PRIMARY KEY, groupName varchar(115), GroupDesc varchar(200), groupCode varchar(30), isSystemGroup bit,
					allowManualAssignment bit, imageExt varchar(4), uid uniqueidentifier, isProtected bit, alertIfPopulated bit, parentGroupID int,
					memberCount int, sortOrder int, thePathExpanded varchar(500), thePath varchar(500), isFeatured bit, row int, minParentSort int, maxParentSort int);
				CREATE TABLE ##tmpAlertGroups (groupID int PRIMARY KEY);

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
			
				INSERT INTO ##tmpAlertGroups (groupID)
				select g.groupID
				from dbo.ams_groups as g
				where g.isSystemGroup = 0
				and g.allowManualAssignment = 0
				and g.hideOnGroupLists = 0
				and g.orgID = @orgID
				and g.status = 'A'
				and not exists (
					select rg.autoID
					from dbo.cache_ams_recursiveGroups as rg
					inner join dbo.ams_virtualGroupRuleGroups as vgrg on vgrg.groupID = rg.startGroupID
					inner join dbo.ams_virtualGroupRules as vgr on vgr.ruleid = vgrg.ruleid and vgr.isActive = 1 and vgr.orgID = @orgID
					where rg.groupID = g.groupID
					and rg.orgID = @orgID
					)
				and not exists (
					select rg.autoID
					from dbo.cache_ams_recursiveGroups as rg
					inner join dbo.ams_groups as g2 on g2.groupID = rg.startGroupID
					where rg.groupID = g.groupID
					and g2.orgID = @orgID
					and g2.allowManualAssignment = 1
					and rg.orgID = @orgID
					);

				INSERT INTO ##groupCount (groupID, memberCount)
				select mg.groupID, count(mg.memberID)
				from dbo.cache_members_groups as mg
				inner join dbo.ams_members as m on m.orgID = @orgID
					and m.memberID = mg.memberID
					and m.status in ('A','I')
				where mg.orgID = @orgID
				group by mg.groupID;

				<cfif local.filter_pendingRV EQ 1>
					IF OBJECT_ID('tempdb..##tmpGroupsHavingUncommitedRuleVersions') IS NOT NULL 
						DROP TABLE ##tmpGroupsHavingUncommitedRuleVersions;
					CREATE TABLE ##tmpGroupsHavingUncommitedRuleVersions (groupID int PRIMARY KEY);

					INSERT INTO ##tmpGroupsHavingUncommitedRuleVersions (groupID)
					SELECT DISTINCT vgrg.groupID
					FROM dbo.ams_virtualGroupRules AS vgr
					INNER JOIN dbo.ams_virtualGroupRuleVersions AS vgrv ON vgrv.orgID = @orgID
						AND vgrv.ruleID = vgr.ruleID 
						AND vgrv.ruleVersionID > vgr.activeVersionID
					INNER JOIN dbo.ams_virtualGroupRuleGroups AS vgrg ON vgrg.ruleID = vgr.ruleID
					WHERE vgr.orgID = @orgID
					AND vgr.ruleTypeID = 1 
					AND vgr.isActive = 1;
				</cfif>

				INSERT INTO ##tmpGroups (groupID, groupName, GroupDesc, groupCode, isSystemGroup, allowManualAssignment, imageExt, uid, isProtected, alertIfPopulated,
					parentGroupID, memberCount, sortOrder, thePathExpanded, thePath, isFeatured, row, minParentSort, maxParentSort)
				SELECT g.groupID, g.groupName, g.GroupDesc, g.groupCode, g.isSystemGroup, g.allowManualAssignment, g.imageExt, g.uid, g.isProtected, g.alertIfPopulated,
					g.parentGroupID, isnull(gc.memberCount,0) as memberCount, g.sortOrder, g.groupPathExpanded as thePathExpanded, g.groupPathSortOrder as thePath,
					 CASE WHEN fg.groupID IS NOT NULL THEN 1 ELSE 0 END AS isFeatured, ROW_NUMBER() OVER (ORDER BY g.groupPathSortOrder ASC) AS row,
					0 as minParentSort, 0 as maxParentSort
				FROM dbo.ams_groups AS g
				LEFT OUTER JOIN ##groupCount AS gc ON gc.groupID = g.groupID
				LEFT OUTER JOIN dbo.ams_featuredGroups AS fg ON fg.orgID = @orgID 
					and fg.groupID = g.groupID
				WHERE g.status = 'A' 
				AND g.orgID = @orgID
				<cfif listFindNoCase("copyRuleFromGroup,groupsForRule", arguments.mode)>
					AND g.isProtected = 0
					AND (g.groupName NOT IN ('Public','Guests','Users') OR g.isSystemGroup = 0)
				</cfif>
				AND g.hideOnGroupLists = 0;

				<cfif listFindNoCase("listing,copyRuleFromGroup,groupsForRule", arguments.mode)>
					<cfif arguments.mode EQ 'copyRuleFromGroup'>
						IF OBJECT_ID('tempdb..##tmpDirectlyAssignedRuleGroups') IS NOT NULL 
							DROP TABLE ##tmpDirectlyAssignedRuleGroups;
						CREATE TABLE ##tmpDirectlyAssignedRuleGroups (groupID int PRIMARY KEY);

						INSERT INTO ##tmpDirectlyAssignedRuleGroups (groupID)
						SELECT DISTINCT vgrg.groupID
						FROM dbo.ams_virtualGroupRuleGroups AS vgrg
						INNER JOIN dbo.ams_virtualGroupRules AS vgr ON vgr.orgID = @orgID
							AND vgr.ruleid = vgrg.ruleid
						INNER JOIN ##tmpGroups AS g ON g.groupID = vgrg.groupID
						WHERE vgr.isActive = 1;
					</cfif>
					
					update tmp
					set tmp.minParentSort = tmpAggr.minSort,
						tmp.maxParentSort = tmpAggr.maxSort
					from ##tmpGroups as tmp
					inner join (
						select parentGroupID, min(sortOrder) as minSort, max(sortOrder) as maxSort
						from ##tmpGroups
						group by parentGroupID
					) as tmpAggr on isnull(tmpAggr.parentGroupID,0) = isnull(tmp.parentGroupID,0);

					WITH filteredGroups as (
						select tmp.groupID, tmp.parentGroupID, tmp.thePath
						from ##tmpGroups as tmp
						<cfif local.filter_noRules EQ 1>
							INNER JOIN ##tmpAlertGroups as ag on ag.groupID = tmp.groupID
						</cfif>
						<cfif local.filter_pendingRV EQ 1>
							INNER JOIN ##tmpGroupsHavingUncommitedRuleVersions as prvg on prvg.groupID = tmp.groupID
						</cfif>
						where 1 = 1
						<cfif local.filter_groupName neq "">
							AND tmp.groupName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#replace(local.filter_groupName,'_','\_','ALL')#%"> ESCAPE('\')
						</cfif>
						<cfif local.filter_groupDesc neq "">
							AND tmp.groupDesc LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#replace(local.filter_groupDesc,'_','\_','ALL')#%"> ESCAPE('\')
						</cfif>
						<cfif local.filter_groupCountLow gt 0>
							AND tmp.memberCount >= <cfqueryparam cfsqltype="cf_sql_integer" value="#local.filter_groupCountLow#">
						</cfif>
						<cfif local.filter_groupCountHigh gt 0>
							AND tmp.memberCount <= <cfqueryparam cfsqltype="cf_sql_integer" value="#local.filter_groupCountHigh#">
						</cfif>
						<cfif len(local.filter_allowManualAssignment)>
							AND tmp.allowManualAssignment = <cfqueryparam cfsqltype="cf_sql_bit" value="#local.filter_allowManualAssignment#">
						</cfif>
						<cfif local.filter_groupUID neq "">
							AND tmp.uid = <cfqueryparam value="#local.filter_groupUID#" cfsqltype="cf_sql_idstamp">
						</cfif>
						<cfif local.filter_featuredGroup neq "">
							AND tmp.isFeatured = <cfqueryparam value="#local.filter_featuredGroup#" cfsqltype="cf_sql_bit">
						</cfif>
							union all
						select allGroups.groupID, allGroups.parentGroupID, allGroups.thePath
						from ##tmpGroups as allGroups
						inner join filteredGroups on left(filteredGroups.thePath,len(allGroups.thePath)+1) = allGroups.thePath + '.'
					)
					SELECT tmp.*,
						'0001.' + tmp.thePath AS theNewPath,
						'All Groups\' + tmp.thePathExpanded AS theNewPathExpanded,
						CASE WHEN tmp.sortOrder = tmp.minParentSort THEN 1 ELSE 0 END AS listFirstItem,
						CASE WHEN tmp.sortOrder = tmp.maxParentSort THEN 1 ELSE 0 END AS listLastItem,
						CASE WHEN EXISTS (SELECT 1 FROM ##tmpGroups WHERE parentGroupID = tmp.groupID) THEN 1 ELSE 0 END as hasChildGrps
						<cfif arguments.mode EQ 'copyRuleFromGroup'>
							, CASE WHEN EXISTS (SELECT 1 FROM ##tmpDirectlyAssignedRuleGroups WHERE groupID = tmp.groupID) THEN 1 ELSE 0 END as canSelect
						</cfif>
					FROM (
						SELECT distinct allGroups.groupID, allGroups.uid, allGroups.groupName, allGroups.GroupDesc, allGroups.isSystemGroup, allGroups.allowManualAssignment,
							allGroups.parentGroupID, allGroups.memberCount, allGroups.thePathExpanded, allGroups.thePath, allGroups.sortOrder, allGroups.row,
							allGroups.minParentSort, allGroups.maxParentSort, CASE WHEN ag.groupID IS NOT NULL THEN 'Yes' ELSE 'No' END AS setupIssue
						FROM filteredGroups 
						INNER JOIN ##tmpGroups as allGroups on allGroups.thePath = filteredGroups.thePath
						LEFT OUTER JOIN ##tmpAlertGroups as ag on ag.groupID = allGroups.groupID
					) AS tmp
					ORDER BY tmp.row;

					<cfif arguments.mode EQ 'copyRuleFromGroup'>
						IF OBJECT_ID('tempdb..##tmpDirectlyAssignedRuleGroups') IS NOT NULL 
							DROP TABLE ##tmpDirectlyAssignedRuleGroups;
					</cfif>
				<cfelseif arguments.mode eq "export">
					IF OBJECT_ID('tempdb..##tmpGroupExport') IS NOT NULL
	      				DROP TABLE ##tmpGroupExport;

					SELECT tmp.groupName AS [Group Name], tmp.groupCode  AS [Group Code], tmp.GroupDesc AS [Description], tmp.allowManualAssignment AS [Allow Manual Assignment],
						tmp.imageExt AS [Image Ext], uid AS [UID], tmp.isProtected AS [Is Protected], tmp.alertIfPopulated AS [Alert If Populated], 
						CASE WHEN ag.groupID IS NOT NULL THEN 'Yes' ELSE 'No' END AS [Setup Issue], tmp.sortOrder AS [Sort Order], 
						tmp.thePathExpanded AS [Group Path], tmp.memberCount AS [Member Count], ROW_NUMBER() OVER (ORDER BY tmp.thePathExpanded) as row
					INTO ##tmpGroupExport
					FROM ##tmpGroups AS tmp
					<cfif local.filter_noRules EQ 1>
						INNER JOIN ##tmpAlertGroups as ag on ag.groupID = tmp.groupID
					<cfelse>
						LEFT OUTER JOIN ##tmpAlertGroups as ag on ag.groupID = tmp.groupID
					</cfif>
					<cfif local.filter_pendingRV EQ 1>
						INNER JOIN ##tmpGroupsHavingUncommitedRuleVersions as prvg on prvg.groupID = tmp.groupID
					</cfif>
					WHERE 1 = 1
					<cfif local.filter_groupName neq "">
						AND tmp.groupName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#replace(local.filter_groupName,'_','\_','ALL')#%"> ESCAPE('\')
					</cfif>
					<cfif local.filter_groupDesc neq "">
						AND tmp.groupDesc LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#replace(local.filter_groupDesc,'_','\_','ALL')#%"> ESCAPE('\')
					</cfif>
					<cfif local.filter_groupCountLow gt 0>
						AND tmp.memberCount >= <cfqueryparam cfsqltype="cf_sql_integer" value="#local.filter_groupCountLow#">
					</cfif>
					<cfif local.filter_groupCountHigh gt 0>
						AND tmp.memberCount <= <cfqueryparam cfsqltype="cf_sql_integer" value="#local.filter_groupCountHigh#">
					</cfif>
					<cfif len(local.filter_allowManualAssignment)>
						AND tmp.allowManualAssignment = <cfqueryparam cfsqltype="cf_sql_bit" value="#local.filter_allowManualAssignment#">
					</cfif>
					<cfif local.filter_groupUID neq "">
						AND tmp.uid = <cfqueryparam value="#local.filter_groupUID#" cfsqltype="cf_sql_idstamp">
					</cfif>
					<cfif local.filter_featuredGroup neq "">
						AND tmp.isFeatured = <cfqueryparam value="#local.filter_featuredGroup#" cfsqltype="cf_sql_bit">
					</cfif>;

					DECLARE @selectsql VARCHAR(MAX);
					SET @selectsql = 'SELECT [Group Name], [Group Code], [Description], [Allow Manual Assignment],
						[Image Ext], [UID], [Is Protected], [Alert If Populated], [Setup Issue], [Sort Order], [Group Path],
						[Member Count], row AS mcCSVorder
						*FROM* ##tmpGroupExport';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.reportFileName#', @returnColumns=1;

					IF OBJECT_ID('tempdb..##tmpGroupExport') IS NOT NULL 
						DROP TABLE ##tmpGroupExport;					
				</cfif>

				IF OBJECT_ID('tempdb..##groupCount') IS NOT NULL
					DROP TABLE ##groupCount;
				IF OBJECT_ID('tempdb..##tmpGroups') IS NOT NULL
					DROP TABLE ##tmpGroups;
				IF OBJECT_ID('tempdb..##tmpAlertGroups') IS NOT NULL 
					DROP TABLE ##tmpAlertGroups;
				<cfif local.filter_pendingRV EQ 1>
					IF OBJECT_ID('tempdb..##tmpGroupsHavingUncommitedRuleVersions') IS NOT NULL 
						DROP TABLE ##tmpGroupsHavingUncommitedRuleVersions;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif listFindNoCase("listing,copyRuleFromGroup,groupsForRule", arguments.mode)>
			<cfreturn local.qryGroups>
		</cfif>
	</cffunction>

	<cffunction name="getGroupUsages" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="subgroupsonly" type="boolean" required="true">
		
		<cfset var qryGroupUsage = "">

		<cfquery name="qryGroupUsage" datasource="#application.dsn.memberCentral.dsn#">
			EXEC dbo.ams_getGroupUsage
				@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">,
				@subgroupsonly = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.subgroupsonly#">;
		</cfquery>

		<cfreturn qryGroupUsage>
	</cffunction>

	<cffunction name="getDisplayGroupPath" access="public" output="false" returntype="string">
		<cfargument name="groupName" type="string" required="true">
		<cfargument name="parentGroupID" type="numeric" required="true">
		<cfargument name="thePathExpanded" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.fullPath = replaceNoCase(arguments.thePathExpanded, "&amp;", "&", "all")>
		<cfset local.displayGroupPath = val(arguments.parentGroupID) gt 0 ? trim(Left(local.fullPath, local.fullPath.lastIndexOf(" \ #arguments.groupName#"))) : "">

		<cfreturn displayGroupPath>
	</cffunction>

	<cffunction name="copyGroupRule" access="public" output="false" returntype="struct" hint="copy groupRule">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="copyRuleID" type="numeric" required="true">
		<cfargument name="ruleName" type="string" required="true">

		<cfif NOT hasRightsForGroupAction(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, action="copyGroupRule", groupID=arguments.groupID)>
			<cfreturn { "success":false, "errmsg":"Invalid request" }>
		</cfif>
		
		<cfset var ruleID = CreateObject("component","model.admin.virtualGroups.virtualGroups").copyGroupRule(orgID=arguments.mcproxy_orgID, copyRuleID=arguments.copyRuleID, ruleName=arguments.ruleName, incGroups=0)>

		<cfreturn {"success":true, "ruleid":ruleID}>
	</cffunction>

	<cffunction name="saveGroupRule" access="public" output="false" returntype="struct" hint="save groupRule">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="ruleVersionID" type="numeric" required="true">
		<cfargument name="forceActivateRule" type="boolean" required="true">

		<cfset var local = structNew()>
		
		<cfif NOT hasRightsForGroupAction(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, action="saveGroupRule", groupID=arguments.groupID)>
			<cfreturn { "success":false, "errmsg":"Invalid request" }>
		</cfif>

		<cfset local.numRuleConditions = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin").countRuleConditions(ruleID=arguments.ruleID, ruleVersionID=arguments.ruleVersionID)>

		<cfif NOT local.numRuleConditions>
			<cfreturn { "success":false, "errmsg":"Define one or more filters in the Rule Builder." }>
		</cfif>
		
		<cfquery name="local.qrySaveGroupRule" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
					@groupID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">,
					@ruleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleID#">,
					@ruleVersionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ruleVersionID#">,
					@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
					@isActive bit;

				SELECT @isActive = isActive
				FROM dbo.ams_virtualGroupRules
				WHERE ruleID = @ruleID
				AND orgID = @orgID;

				BEGIN TRAN;
					IF @ruleVersionID > 0
						EXEC dbo.ams_activateVirtualGroupRuleVersion @orgID=@orgID, @ruleID=@ruleID, @ruleVersionID=@ruleVersionID;

					EXEC dbo.ams_createVirtualGroupRuleGroup @ruleID=@ruleID, @groupID=@groupID, @recordedByMemberID=@recordedByMemberID, @bypassQueue=0;

					<cfif arguments.forceActivateRule>
						IF @isActive = 0	
							EXEC dbo.ams_activateVirtualGroupRule @ruleID=@ruleID, @forceCache=0;
					</cfif>
				COMMIT TRAN;
					
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="triggerGroupRuleConditions" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="groupID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfif NOT hasRightsForGroupAction(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, action="triggerGroupRuleConditions", groupID=arguments.groupID)>
			<cfreturn { "success":false, "errmsg":"Invalid request" }>
		</cfif>

		<cfsetting requesttimeout="240">
		
		<cfquery name="local.qryTriggerGroupRuleConditions" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			
			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
				@groupID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">;

			IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
				DROP TABLE ##tblMCQRun;
			CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

			INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
			SELECT DISTINCT @orgID, NULL, vgc.conditionID
			FROM dbo.ams_virtualGroupRules AS r
			INNER JOIN dbo.ams_virtualGroupRuleGroups AS rg ON rg.ruleID = r.ruleID
				AND rg.groupID = @groupID
			INNER JOIN dbo.ams_virtualGroupRuleVersions AS rv ON rv.orgID = @orgID
				AND rv.ruleVersionID = r.activeVersionID
			INNER JOIN dbo.ams_virtualGroupRuleConditionSets AS rcs ON rcs.ruleVersionID = rv.ruleVersionID
			INNER JOIN dbo.ams_virtualGroupRuleConditions AS rc ON rc.conditionSetID = rcs.conditionSetID
			INNER JOIN dbo.ams_virtualGroupConditions AS vgc ON vgc.orgID = @orgID AND vgc.conditionID = rc.conditionID
			WHERE r.orgID = @orgID
			AND r.isActive = 1;

			IF @@ROWCOUNT > 0
				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=1, @type='ConditionsAndGroups';

			IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
				DROP TABLE ##tblMCQRun;
		</cfquery>

		<cfreturn { "success":true }>
	</cffunction>

	<cffunction name="getGroupNameAndPath" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="groupID" type="numeric" required="true">

		<cfset var local = structNew()>
		
		<cfif NOT hasRightsForGroupAction(orgID=arguments.mcproxy_orgID, siteID=arguments.mcproxy_siteID, siteCode=arguments.mcproxy_siteCode, action="getGroupNameAndPath", groupID=arguments.groupID)>
			<cfreturn { "success":false, "errmsg":"Invalid request" }>
		</cfif>

		<cfquery name="local.qryGroupInfo" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select groupName, parentGroupID, groupPathExpanded
			from dbo.ams_groups
			where orgID = <cfqueryparam value="#arguments.mcproxy_orgID#" cfsqltype="CF_SQL_INTEGER">
			and groupID = <cfqueryparam value="#arguments.groupID#" cfsqltype="CF_SQL_INTEGER">
			and [status] = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.fullPath = replaceNoCase(local.qryGroupInfo.groupPathExpanded, "&amp;", "&", "all")>
		<cfset local.displayGroupPath = val(local.qryGroupInfo.parentGroupID) gt 0 ? trim(Left(local.fullPath, local.fullPath.lastIndexOf(" \ #local.qryGroupInfo.groupName#"))) : "">

		<cfreturn { "success":true, "groupname":local.qryGroupInfo.groupName, "grouppath":local.displayGroupPath }>
	</cffunction>
	
	<cffunction name="getGroupHistoryMembers" access="public" output="false" returntype="struct">		
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="groupID" type="numeric" required="true">
		<cfargument name="histcode" type="string" required="true">
		<cfargument name="memPageNum" type="numeric" required="true">
		<cfargument name="perPageResults" type="numeric" required="true">
		<cfargument name="showMemberPhoto" type="boolean" required="true">
		<cfargument name="startDate" type="date" required="true">
		<cfargument name="endDate" type="date" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		
		<!--- preserve case --->
		<cfset local.returnStruct['success'] = true>
		<cfset local.returnStruct['arrgrouphistorymembers'] = arrayNew(1)>
		<cfset local.returnStruct['strpage'] = structNew()>
		<cfset local.returnStruct['strpage']['rowsize'] = arguments.perPageResults>
		<cfset local.returnStruct['strpage']['currpage'] = arguments.memPageNum>
		<cfset local.returnStruct['strpage']['nextpage'] = arguments.memPageNum + 1>
		<cfset local.returnStruct['strpage']['prevpage'] = arguments.memPageNum - 1>
		<cfset local.returnStruct['strpage']['currcountstart'] = 0>
		<cfset local.returnStruct['strpage']['currcountstop'] = 0>
		<cfset local.returnStruct['strpage']['totalcount'] = 0>
		<cfset local.returnStruct['strpage']['showmemberphoto'] = arguments.showMemberPhoto>
		<cfset local.returnStruct['strpage']['histcode'] = arguments.histcode>

		<!--- get result fields --->
		<cfset local.objMFS = CreateObject("component","model.system.platform.memberFieldsets")>
		<cfset local.MemberAdminSRID = application.objSiteInfo.mc_siteInfo[arguments.mcproxy_siteCode].memberAdminSiteResourceID>
		<cfset local.qryResultsFieldsetID = getLocatorFieldsetID(siteResourceID=local.MemberAdminSRID,area='results')>
		
		<cfset local.qryFilteredGroupHistoryMembers = CreateObject("component","model.admin.reports.members.GroupMembershipChangeHistory").generateData(
				orgID=arguments.mcproxy_orgID, startDate=arguments.startDate, endDate=arguments.endDate, groupIDList=arguments.groupID, 
				frmReportView="grpHist#UcFirst(arguments.histcode)#", reportAction="screen", fieldSetIDList=local.qryResultsFieldsetID.fieldsetID, ovNameFormat='', ovMaskEmails=0,
				start="#(local.returnStruct.strpage.prevPage*local.returnStruct.strpage.rowsize)+1#", count=local.returnStruct.strpage.rowsize).qryData>

		<cfif not local.qryFilteredGroupHistoryMembers.recordCount>
			<cfreturn local.returnStruct>
		</cfif>
		
		<cfif local.qryFilteredGroupHistoryMembers.recordCount>
			<cfset local.objMemberAdmin = createObject("component","model.admin.members.memberAdmin")>

			<cfset local.returnStruct['strpage']['totalcount'] = local.qryFilteredGroupHistoryMembers.mc_totalMatches>
			<cfif local.returnStruct.strpage.currpage eq 1>
				<cfset local.returnStruct['strpage']['currcountstart'] = 1>
				<cfset local.returnStruct['strpage']['currcountstop'] 	= local.returnStruct.strpage.rowsize>
			<cfelse>
				<cfset local.returnStruct['strpage']['currcountstart'] = local.returnStruct.strpage.rowSize * (local.returnStruct.strpage.currpage - 1) + 1>
				<cfset local.returnStruct['strpage']['currcountstop'] 	= local.returnStruct.strpage.rowsize * (local.returnStruct.strpage.currpage - 1) + local.qryFilteredGroupHistoryMembers.recordCount>
			</cfif>
			<cfset local.returnStruct['strpage']['nextcountstart'] = local.returnStruct.strpage.currPage *  local.returnStruct.strpage.rowsize + 1>

			<cfset local.xmlResultFields = local.qryFilteredGroupHistoryMembers.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = local.objMFS.getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
			<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
			<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
			<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
			<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>
			
			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.mcproxy_orgID, includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>
			
			<cfloop query="local.qryFilteredGroupHistoryMembers">
				<cfset local.tmpStr = structNew()>

				<cfset local.tmpStr['memberid'] = local.qryFilteredGroupHistoryMembers.memberID>
				<cfset local.tmpStr['company'] = local.qryFilteredGroupHistoryMembers.company>
				<cfset local.tmpStr['mcaccountstatus'] = local.qryFilteredGroupHistoryMembers.MCAccountStatus>
				<cfset local.tmpStr['isManualDirect'] = local.qryFilteredGroupHistoryMembers.isManualDirect>
				<cfset local.tmpStr['isManualIndirect'] = local.qryFilteredGroupHistoryMembers.isManualIndirect>
				<cfset local.tmpStr['isVirtualDirect'] = local.qryFilteredGroupHistoryMembers.isVirtualDirect>
				<cfset local.tmpStr['isVirtualIndirect'] = local.qryFilteredGroupHistoryMembers.isVirtualIndirect>
				<cfset local.tmpStr['changeDate'] = dateformat(local.qryFilteredGroupHistoryMembers.LastChangeDate,'m/d/yy')>				
				<cfset local.tmpStr['hasPhoto'] = local.qryFilteredGroupHistoryMembers.hasMemberPhotoThumb>
				<cfif local.qryFilteredGroupHistoryMembers.hasMemberPhotoThumb>
					<cfset local.tmpStr['memberphoto'] = local.qryFilteredGroupHistoryMembers.membernumber & ".jpg">
				<cfelse>
					<cfset local.tmpStr['memberphoto'] = "">
				</cfif>
				
				<cfset local.arrAllClassifications = local.objMemberAdmin.getClassificationsForMemberIDList(siteResourceID=local.MemberAdminSRID, memberIDList=valueList(local.qryFilteredGroupHistoryMembers.memberid))>

				<cfset local.tmpStr['mc_combinedName'] = local.qryFilteredGroupHistoryMembers['Extended MemberNumber'][local.qryFilteredGroupHistoryMembers.currentrow]>

				<!--- combine address fields if there are any --->
				<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
				<cfif StructCount(local.thisMem_mc_combinedAddresses)>
					<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
						<cfsavecontent variable="local.thisATFull">
							<cfoutput>
							<cfif left(local.thisATID,1) eq "t">
								<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
								<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfelse>
								<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
								<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
							</cfif>

							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>#local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>#local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>#local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>#local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]#</cfif>
							<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
							<cfif arrayLen(local.tmp2) is 1 and len(local.qryFilteredGroupHistoryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>, #local.qryFilteredGroupHistoryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]# </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])> #local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>#local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]# County<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])> #local.qryFilteredGroupHistoryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
							<cfloop array="#local.tmp#" index="local.thisPT">
								<cfif len(local.qryFilteredGroupHistoryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>
									<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.qryFilteredGroupHistoryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]#</div>
								</cfif>
							</cfloop>
							</cfoutput>
						</cfsavecontent>
						<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
						<cfif left(local.thisATfull,2) eq ", ">
							<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
						</cfif>
						<cfif len(local.thisATfull)>
							<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
						<cfelse>
							<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
						</cfif>
					</cfloop>

					<cfsavecontent variable="local.thisMemCombinedAddress">
						<cfoutput>
						<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
							<div><b style="font-size:95%; margin-right:15px;">#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_combinedAddresses'] = rereplace(replace(replace(local.thisMemCombinedAddress,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_combinedAddresses'] = "">
				</cfif>

				<!--- get recordtype if available --->
				<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryFilteredGroupHistoryMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>
					<cfset local.tmpStr['mc_recordType'] = local.qryFilteredGroupHistoryMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_recordType'] = "">
				</cfif>
				
				<!--- get membertypeid if available --->
				<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryFilteredGroupHistoryMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>
					<cfset local.tmpStr['mc_memberType'] = local.qryFilteredGroupHistoryMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_memberType'] = "">
				</cfif>
				
				<!--- get status if available --->
				<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryFilteredGroupHistoryMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>
					<cfset local.tmpStr['mc_memberStatus'] = local.qryFilteredGroupHistoryMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]>
				<cfelse>
					<cfset local.tmpStr['mc_memberStatus'] = "">
				</cfif>

				<!--- get last login date if available --->
				<cfif arrayLen(local.LastLoginDateInFS) is 1>
					<cfif len(local.qryFilteredGroupHistoryMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow])>
						<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(local.qryFilteredGroupHistoryMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryFilteredGroupHistoryMembers.currentrow],"m/d/yy")#'>
					<cfelse>
						<cfset local.tmpStr['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="dim"><i>none</i></span>'>
					</cfif>
				<cfelse>
					<cfset local.tmpStr['mc_lastlogin'] = "">
				</cfif>				

				<cfif local.qryOutputFieldsForLoop.recordCount>
					<cfsavecontent variable="local.thisMemExtraInfo">
						<cfoutput>
						<cfloop query="local.qryOutputFieldsForLoop">
							<cfset local.currValue = local.qryFilteredGroupHistoryMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryFilteredGroupHistoryMembers.currentrow]>
							<cfif len(local.currValue)>
								<div>
									#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
									<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
										#dollarFormat(local.currValue)#
									<cfelse>
										<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
											<cfcase value="DATE">
												#dateFormat(local.currValue,"m/d/yyyy")#
											</cfcase>
											<cfcase value="STRING,DECIMAL2,INTEGER">
												#local.currValue#
											</cfcase>
											<cfcase value="BIT">
												#YesNoFormat(local.currValue)#
											</cfcase>
										</cfswitch>
									</cfif>
								</div>
							</cfif>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['mc_extraInfo'] = rereplace(replace(replace(local.thisMemExtraInfo,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['mc_extraInfo'] = "">
				</cfif>
				
				<cfif isArray(local.arrAllClassifications) and arrayLen(local.arrAllClassifications)>
					<cfsavecontent variable="local.classifications">
						<cfoutput>
						<cfloop array="#local.arrAllClassifications#" index="local.currentClass">
							<cfset local.qryClass = local.currentClass.qryClass>
							<cfquery name="local.classifications" dbtype="query">
								select groupName
								from [local].qryClass
								where memberID = #local.qryFilteredGroupHistoryMembers.memberid#
								order by groupName
							</cfquery>
							<cfif local.classifications.recordCount>					
								<div class="mb-1"><span class="badge badge-info">#replace(local.currentClass.name,'_',' ','ALL')#</span></div>
								<div>
									<cfloop query="local.classifications">
										- #local.classifications.groupName# <br>
									</cfloop>
								</div>
							</cfif>
						</cfloop>
						</cfoutput>
					</cfsavecontent>
					<cfset local.tmpStr['classifications'] = rereplace(replace(replace(local.classifications,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
				<cfelse>
					<cfset local.tmpStr['classifications'] = "">
				</cfif>

				<cfset arrayAppend(local.returnStruct['arrgrouphistorymembers'],local.tmpStr)>
			</cfloop>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>