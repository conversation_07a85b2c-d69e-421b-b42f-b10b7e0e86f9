<cfscript>
local.dataStruct = attributes.data;
local.rc = attributes.event.getCollection();
</cfscript>

<cfinclude template="/views/search/commonJS.cfm">
<cfinclude template="/views/search/responsive/commonSearchJS.cfm">

<cfhtmlhead text="<META NAME=""ROBOTS"" CONTENT=""INDEX,NOFOLLOW"">">
    
<cfset local.isPostSearchOp = local.dataStruct.searchAction eq "doSearch">
<cfset local.showBucketContent = NOT local.isPostSearchOp OR local.dataStruct.qryBucketInfo.bucketName neq "Introduction">
<cfif local.isPostSearchOp>
    <cfset local.initButtonText = "Back to ">
<cfelse>
    <cfset local.initButtonText = "Expand Databases">
</cfif>

<cfoutput>
    <!--- searchid holder for reference --->
    <div id="bk_sid_holder" style="display:none;">#local.dataStruct.intSearchID#</div>
    <div id="tsSearchAppWrapper">
        <div>
            <!--- tabs --->
            <cfif arrayLen(local.dataStruct.strTopBanner.resultArr)>
                <div id="searchBarTopNav">
                    <div class="hidden-phone">
                        <div class="navbar">
                            <div class="navbar-inner">
                                <ul class="nav" id="tssearchbar">
                                    <cfloop from="1" to="#arrayLen(local.dataStruct.strTopBanner.resultArr)#" index="local.thisEl">
                                        <li id="nav_#local.dataStruct.strTopBanner.resultArr[local.thisEl].tab#">#local.dataStruct.strTopBanner.resultArr[local.thisEl].tablink#</li>
                                    </cfloop>
                                </ul>
                                <ul id="tsdocumentviewerbar" class="nav" style="display:none;margin-bottom:10px;"></ul>
                                <ul id="tsinlinecartbar" class="nav"  style="display:none;margin-bottom:10px;">
                                    <li id="cartnav_checkout"><a href="javascript:doCheckoutDocumentCart(#session.cfcuser.memberdata.memberid#, #local.dataStruct.hasMissingTaxInfo#);"><i class="icon-shopping-cart" style="vertical-align: inherit;"></i> Checkout</a></li>
                                    <li id="cartnav_close"><a href="javascript:cart_closeInlineCart();"><i class="icon-remove-circle" style="vertical-align: inherit;"></i> Close Cart</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <!--- mobile view --->
                     <div class="visible-phone text-center">
                        <div id="searchBarMobile" class="btn-group" style="margin-bottom:20px;">
                            <cfif local.showBucketContent>
                                <a class="btn btn-info" href="javascript:toggleBKList(#local.isPostSearchOp#);">
                                    <i id="toggleBKListIcon" class="icon-plus-sign icon-large"></i>
                                    <span id="toggleBKListText" style="font-weight:bold;" data-initButtonText="#local.initButtonText#">#local.initButtonText#</span>
                                    <cfif local.isPostSearchOp>
                                        <span id="toggleBKListCountDisplay"><strong><span class="resultTotalCountDisplay_cnt">0</span></strong> Results</span>
                                    </cfif>
                                </a>
                            </cfif>
                            <cfloop array="#local.dataStruct.strTopBanner.resultArr#" item="local.thisEl">
                                <cfif local.thisEl.showOnMobile>
                                    <a id="#local.thisEl.tab#" class="btn" href="#local.thisEl.url#">
                                        <i class="#local.thisEl.icon# icon-large"></i> #local.thisEl.shortlabel#
                                        <cfif local.thisEl.label eq "Doc Cart">
                                            <span id="docCartCountDisplay" class="badge badge-inverse <cfif local.thisEl.count eq 0>hide</cfif>">#local.thisEl.count#</span>
                                        </cfif>
                                    </a>
                                </cfif>
                            </cfloop>
                        </div>
                        <div id="documentViewerBarMobile" class="btn-group hide" style="margin-bottom:20px;"></div>
                    </div>
                </div>
            </cfif>            

            <div id="s_listing">
                <!--- mobile view --->
                <div class="row-fluid visible-phone">
                    <cfif local.isPostSearchOp>
                        <div id="resultTotalCountHolder" class="row-fluid <cfif local.showBucketContent>hide</cfif>">
                            <div class="span12 well well-small" style="margin:0;">
                                <strong><span class="resultTotalCountDisplay_cnt">0</span></strong> items match your search.
                                <div class="muted" style="margin-top:3px;">Choose a database below.</div>
                            </div>
                        </div>
                    </cfif>
                </div>
                
                <div class="row-fluid">
                    </cfoutput>
                    <!--- bucket list --->
                    <cfif local.dataStruct.bitShowBucketList>
                        <cfoutput>
                        <div id="bk_list" class="well well-small span4 <cfif local.showBucketContent>hidden-phone</cfif>">
                            </cfoutput>
                            <cfoutput query="local.dataStruct.qryBuckets" group="bucketGroup">
                                <ul class="nav nav-list">
                                    <li class="nav-header" id="bk_#local.dataStruct.qryBuckets.bucketid#_grpnum">
                                        <h5>#UCASE(replaceNoCase(local.dataStruct.qryBuckets.bucketGroup,"*shortname*",local.rc.mc_siteinfo.sitename))#</h5>
                                    </li>
                                    
                                    <cfif local.dataStruct.qryBuckets.bucketGroup eq "TrialSmith Documents" and local.dataStruct.hasDepositionBucket and local.dataStruct.intSearchID gt 0 and local.dataStruct.searchAction eq "doSearch">
                                        <li>
                                            <button class="btn btn-success btn-small searchReportButton hiddenButton" onclick="showReport();" style="margin-bottom:10px;"><i class="icon-file"></i> Download Search Report</button>
                                        </li>
                                    </cfif>
                                    <cfoutput>
                                        <cfset local.bucketSettingsXML = XMLParse(local.dataStruct.qryBuckets.bucketSettings)>
                                        <cfset local.innerDivStyle = "padding:1px 0;">
                                        <cfif (local.dataStruct.qryBuckets.hideUntilSearched and not (local.dataStruct.intSearchID gt 0 and local.dataStruct.searchAction eq "doSearch")) OR 
                                                (StructKeyExists(local.bucketSettingsXML,"settings") and StructKeyExists(local.bucketSettingsXML.settings.XmlAttributes,"showInBucketList") and local.bucketSettingsXML.settings.XmlAttributes.showInBucketList eq 0)>
                                            <cfset local.innerDivStyle = local.innerDivStyle & "display:none;">                                                
                                        </cfif>
                                        <cfif val(local.dataStruct.qryBuckets.restrictToGroupID) gt 0 and local.dataStruct.qryBuckets.isMemberInRestrictedGroup is not 1>
                                            <cfset local.bucketImage = "/assets/common/images/search/folder_locked.png">
                                        <cfelseif local.dataStruct.qryBucketInfo.bucketid eq local.dataStruct.qryBuckets.bucketid>
                                            <cfset local.bucketImage = "/assets/common/images/search/folder_open.png">
                                        <cfelse>
                                            <cfset local.bucketImage = "/assets/common/images/search/folder_close.png">
                                        </cfif>
                                        <li id="bk_#local.dataStruct.qryBuckets.bucketid#_" style="#local.innerDivStyle#" <cfif local.dataStruct.qryBucketInfo.bucketid eq local.dataStruct.qryBuckets.bucketid>class="active"</cfif> >
                                            <a href="/?pg=search&bid=#local.dataStruct.qryBuckets.bucketid#<cfif local.dataStruct.intSearchID gt 0 and local.dataStruct.searchAction eq "doSearch">&sid=#local.dataStruct.intSearchID#&s_a=doSearch</cfif>">
                                                <div class="pull-right">
                                                    <span id="bk_#local.dataStruct.qryBuckets.bucketid#_cnt" style="height:18px;"></span>
                                                    <i class="icon-chevron-right <cfif local.dataStruct.searchAction eq "doSearch">hidden-phone</cfif>" style="margin-left:8px;"></i>
                                                </div>
                                                <div>
                                                    <img src="#local.bucketImage#" width="19" height="16" align="absmiddle" border="0" />
                                                    <span>#local.dataStruct.qryBuckets.bucketName#</span>
                                                </div>
                                                <div style="clear:both;"></div>
                                            </a>
                                        </li>
                                    </cfoutput>
                                </ul>
                                <br/>
                            </cfoutput>
                            <cfoutput>
                            <div id="searchSponsorContainer" style="text-align:center;"></div>
                            <cfsavecontent variable="local.sponsorAdJS">
                                <cfoutput>
                                    <script type="text/javascript">
                                        $().ready( function() {
                                            MCPromises.BackendPlatformServices.then(function() {
                                                var MCadlimit = 2;
                                                var MCadContainer = $("##searchSponsorContainer");
                                                var MCadSitecode = '#local.rc.mc_siteInfo.sitecode#';
                                                var MCadZoneType = 'search';
                                                
                                                var overrideCallback = function (adsArray) {
                                                    if (adsArray != null && adsArray.length) {
                                                        for (var i=0;i<Math.min(adsArray.length,MCadlimit);i++) {
                                                            if (adsArray[i].ADLINK.length)
                                                                $(MCadContainer).append('<a href="' + adsArray[i].ADLINK + '" title="Click for more information" target="_blank"><img style="width:' + adsArray[i].WIDTH + ';height:' + adsArray[i].HEIGHT + ';" src="' + adsArray[i].IMAGEURL + '" /></a><br/><br/>');
                                                            else
                                                                $(MCadContainer).append('<a href="/?pg=search&bid=#local.dataStruct.intBucketID#&s_a=otherTools&adID=' + adsArray[i].ADID + '" title="Click for more information" target="_self"><img style="width:' + adsArray[i].WIDTH + ';height:' + adsArray[i].HEIGHT + ';" src="' + adsArray[i].IMAGEURL + '" /></a><br/><br/>');
                                                        }
                                                    } else {
                                                        $(MCadContainer).remove();
                                                    }
                                                };
                                                MCBackendPlatformServices.SponsorAdsService.MCIncludeAds(MCadContainer, MCadZoneType,MCadSitecode,MCadlimit,'_top',overrideCallback);
                                            }).catch(error => {
                                                let msg = 'Failed to get ads for listViewerSponsorContainer';
                                                if (MCJSErrorReporting && MCJSErrorReporting.promiseRejectionHandler)
                                                    MCJSErrorReporting.promiseRejectionHandler(msg)
                                                else 
                                                    console.error(`MCJSErrorReporting.promiseRejectionHandler not defined, falling back to console message: ${msg}`);
                                            });
                                        });
                                    </script>
                                </cfoutput>
                            </cfsavecontent>
                            <cfhtmlhead text="#local.sponsorAdJS#">

                            <cfsavecontent variable="local.htmlhead">
                                <cfoutput>
                                    <script src="/sitecomponents/COMMON/javascript/webviewer/6.3.2/webviewer.min.js"></script>
                                </cfoutput>
                            </cfsavecontent>
                            <cfhtmlhead text="#local.htmlhead#">
                        </div>
                        </cfoutput>
                    </cfif>

                    <!--- content --->
                    <cfoutput>
                    <div id="bk_content" class="span8 <cfif NOT local.showBucketContent>hidden-phone</cfif>">
                        <cfswitch expression="#local.dataStruct.searchAction#">
                        <cfcase value="doSearch">
                            <cfoutput>#local.dataStruct.objCurrentBucket.showLoading(bucketID=local.dataStruct.qryBucketInfo.bucketID,searchID=local.dataStruct.intSearchID, viewDirectory="responsive")#</cfoutput>
                        </cfcase>
                        <cfcase value="NoKnownBucket">
                            <!--- if the site has buckets, go to 1st bucket. else, kick out and go to homepage --->
                            <cfif local.dataStruct.qryBuckets.recordcount>
                                <cflocation url="/?pg=search&bid=#local.dataStruct.qryBuckets.bucketid#" addtoken="no">
                            <cfelse>
                                <cflocation url="/" addtoken="no">
                            </cfif>
                        </cfcase>
                        <cfcase value="refineSearch">
                            <cfoutput>#local.dataStruct.objCurrentBucket.showSearchForm(bucketID=local.dataStruct.qryBucketInfo.bucketID,searchID=local.dataStruct.intSearchID, viewDirectory="responsive")#</cfoutput>
                        </cfcase>
                        <cfcase value="otherTools">
                            <cfoutput>#local.dataStruct.strOtherTools#</cfoutput>
                        </cfcase>
                        <cfdefaultcase>
                            <cfoutput>#local.dataStruct.objCurrentBucket.showSearchForm(bucketID=local.dataStruct.qryBucketInfo.bucketID, viewDirectory="responsive")#</cfoutput>
                        </cfdefaultcase>
                        </cfswitch>
                    </div>
                </div>
            </div>
        </div>
        <div id="tsDocumentViewer" style="height:1500px;overflow:hidden;display:none;"></div>
        <div id="tsCartViewer" style="display:none;"></div>
        
        <div id="addDocToCartModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="addDocToCartModalLabel" aria-hidden="true">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 id="addDocToCartModalLabel">Add Document To Cart</h4>
            </div>
            <div class="modal-body">
                <p><b>Document:</b><br/><span id="addDocCaseRefDesc"></span></p>
                <p><b>Case Reference:</b><br/>Add reference which will appear on your monthly statement.</p>
                <div id="err_addDocCart" class="alert alert-danger" style="display:none;margin:2px 0;"></div>
                <form onsubmit="return false;">
                    <div>
                        <input type="text" id="addDocCaseRef" name="addDocCaseRef" value="" maxlength="50" style="width:98%;">
                    </div>
                    <div id="addDocCaseRefBadges" class="well bk-p-2 bk-mt-2" style="display:none;"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn" data-dismiss="modal" aria-hidden="true">Close</button>
                <button type="button" name="btnAddDocToCart" id="btnAddDocToCart" class="btn btn-primary">Add to Cart</button>
            </div>
        </div>
        <div id="viewDocPromptModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="viewDocPromptModal" aria-hidden="true">
            <div class="modal-header text-center">
                 <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button></br>
                <p  class="text-center viewDocPromptModalHeader">Your Basic Plan only allows you to purchase documents a-la-carte.</p>
            </div>
            <div class="modal-body text-center">
                 <h5 >Upgrade Your TrialSmith Membership to Read ENTIRE Transcripts Before Purchasing</h5>
                
                <button type="button" onClick="location.href='/?pg=joinTrialSmith'" name="btnAddDocToCart" id="btnAddDocToCart" class="btn btn-primary">Learn more</button>
            </div>
            <div class="modal-footer">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">Close</button>           
            </div>
        </div>
        <div id="oneClickPurchaseModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="oneClickPurchaseModalLabel" aria-hidden="true">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 id="oneClickPurchaseModalLabel">Quick Download</h4>
            </div>
            <div class="modal-body"></div>
            <div class="modal-footer">
                <button type="button" name="btnCloseBillingInfo" id="btnCloseBillingInfo" class="btn" data-dismiss="modal" aria-hidden="true">Close</button>
                <button type="button" name="btnSaveBillingInfo" id="btnSaveBillingInfo" class="btn btn-primary">Continue</button>
            </div>
        </div>
        <div id="docDownloadWaitingModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="docDownloadWaitingModalLabel" aria-hidden="true">
            <div class="modal-header text-center">
                <h4 id="docDownloadWaitingModalLabel">Downloading Document</h4>
            </div>
            <div class="modal-body">
                <div style="min-height:75px;" class="text-center"><i class="icon-spin icon-spinner"></i> Please wait while we download your document.</div>
            </div>
        </div>
        <iframe id="docDownloadIframe" style="display:none;"></iframe>
    </div>
</cfoutput>