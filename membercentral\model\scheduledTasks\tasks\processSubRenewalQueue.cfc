<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="590">

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset local.itemCount = getQueueItemCount()>
		<cfset local.batchIdentifier = ''>

		<cfif local.itemCount GT 0>
			<cfset local.strBatch = markBatch()>
			<cfset local.batchIdentifier = local.strBatch.qryTaskParams.batchUID>

			<!--- Process queue --->
			<cfset local.success = processQueue(strBatch=local.strBatch)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier=local.batchIdentifier, itemcount=local.itemCount)>
	</cffunction>

	<cffunction name="markBatch" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.returnStruct = { rc=0, qrySubscribers='', qryTaskParams=''  }>
		
		<cftry>
			<cfstoredproc procedure="queue_subscriptionRenew_prioritize" datasource="#application.dsn.platformQueue.dsn#">
			</cfstoredproc>

			<cfstoredproc procedure="queue_subscriptionRenew_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.returnStruct.qrySubscribers" resultset="1">
				<cfprocresult name="local.returnStruct.qryTaskParams" resultset="2">
			</cfstoredproc>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
			<cfset local.returnStruct.rc = -1>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="processQueue" access="private" returntype="boolean" output="false">
		<cfargument name="strBatch" type="struct" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>
		<cfset local.errCode = 0>
		<cfset local.objSubRenew = CreateObject("component","model.admin.subscriptions.subscriptionRenew")>
		<cfset local.qrySubscribers = arguments.strBatch.qrySubscribers>

		<cfloop query="local.qrySubscribers">
			<cftry>
				<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @queueTypeID int, @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionRenew', @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_subscriptionRenew
					SET statusID = @statusProcessing,
						dateUpdated = getdate()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">;
				</cfquery>

				<!--- do renewal --->
				<cftry>
					<cfset local.renewResult = local.objSubRenew.doSubscribeRenew(subscriberID=local.qrySubscribers.subscriberID, 
						rescindDate=local.qrySubscribers.rescindDate, overrideStartDate=local.qrySubscribers.overrideStartDate, 
						siteID=local.qrySubscribers.siteID, orgID=local.qrySubscribers.orgID, actorMemberID=local.qrySubscribers.recordedByMemberID,
						renewMode="subRenewQueue")>

					<cfif local.renewResult.success eq false and structKeyExists(local.renewResult,"isException") and local.renewResult.isException>
						<cfset setErrorMessageForEntry(itemID=local.qrySubscribers.itemID, message=local.renewResult.excMessage, errCode=local.renewResult.errCode)>
					<cfelse>
						<cfwddx action="cfml2wddx" input="#local.renewResult.arrMessages#" output="local.wddxMessages">

						<cfquery datasource="#application.dsn.platformQueue.dsn#">
							update dbo.queue_subscriptionRenew
							set wddxMessages = <cfqueryparam value="#local.wddxMessages#" cfsqltype="CF_SQL_LONGVARCHAR">
							where itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">
						</cfquery>
					</cfif>
				<cfcatch type="Any">
					<cfset setErrorMessageForEntry(itemID=local.qrySubscribers.itemID, message="#cfcatch.message# #cfcatch.detail#", errCode="SUBRENEWPROCESSQUEUEFAIL")>
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>

				<cfquery name="local.updateReadyToNotify" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;

					DECLARE @queueTypeID int, @statusProcessing int, @statusReadyToNotify int;
					EXEC dbo.queue_getQueueTypeID @queueType='subscriptionRenew', @queueTypeID=@queueTypeID OUTPUT;
					EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingSubscriber', @queueStatusID=@statusProcessing OUTPUT;
					EXEC dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusReadyToNotify OUTPUT;

					UPDATE dbo.queue_subscriptionRenew
					SET statusID = @statusReadyToNotify,
						dateUpdated = GETDATE()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qrySubscribers.itemID#">
					AND statusID = @statusProcessing;
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
			</cftry>
		</cfloop>

		<!--- ----------------------------------------------------------------------------------- --->
		<!--- post processing: process any notifications for itemGroupUIDs that are readyToNotify --->
		<!--- ----------------------------------------------------------------------------------- --->
		<cftry>
			<!--- notify --->
			<cfstoredproc procedure="queue_subscriptionRenew_grabForNotification" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryNotifications" resultset="1">
				<cfprocresult name="local.qryIndividuals" resultset="2">
			</cfstoredproc>
			<cfif local.qryNotifications.recordcount>
				<cfloop query="local.qryNotifications">
					<cfset local.thisNotificationRecordedByMemberID = local.qryNotifications.recordedByMemberID>
					<cfset local.thisNotificationOrgID = local.qryNotifications.orgID>
					<cfset local.returnStr = { arrMessages=ArrayNew(1), arrRenewMessages=ArrayNew(1), successCount=0, errorCount=0, warningCount=0, resultMsg='' }>
					<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryNotifications.sitecode)>
					<cftry>
						<cfif application.MCEnvironment neq "production">
							<cfset local.thisReportEmail = "<EMAIL>">
						<cfelse>
							<cfset local.thisReportEmail = local.qryNotifications.reportEmail>
						</cfif>
						<cfif NOT len(local.thisReportEmail)>
							<cfset local.thisReportEmail = "<EMAIL>">
						</cfif>
						<cfset local.thisSiteName = local.qryNotifications.siteName>
						<cfset local.thisSiteCode = local.qryNotifications.siteCode>
						
						<cfquery name="local.qryIndividualsThis" dbtype="query">
							select errorMessage, wddxMessages
							from [local].qryIndividuals
							where upper(itemGroupUID) = '#UCASE(local.qryNotifications.itemGroupUID)#'
						</cfquery>

						<cfloop query="local.qryIndividualsThis">
							<cfif len(local.qryIndividualsThis.errorMessage) AND NOT findNoCase("[SQLDEADLOCK]",local.qryIndividualsThis.errorMessage)>
								<cfset local.returnStr.errorCount = local.returnStr.errorCount + 1>
							<cfelse>								
								<cfwddx action="wddx2cfml" input="#local.qryIndividualsThis.wddxMessages#" output="local.arrMessages">
								
								<cfloop array="#local.arrMessages#" index="local.thisMessage">
									<cfif local.thisMessage.errType eq "Warning">
										<cfset local.returnStr.warningCount = local.returnStr.warningCount + 1>
										<cfset ArrayAppend(local.returnStr.arrMessages, local.thisMessage)>
									<cfelseif local.thisMessage.errType eq "Error">
										<cfset local.returnStr.errorCount = local.returnStr.errorCount + 1>
										<cfset ArrayAppend(local.returnStr.arrMessages, local.thisMessage)>
									<cfelseif local.thisMessage.errType eq "Renewal">
										<cfset local.returnStr.successCount = local.returnStr.successCount + 1>
										<cfset ArrayAppend(local.returnStr.arrRenewMessages, local.thisMessage)>
									</cfif>
								</cfloop>
							</cfif>
						</cfloop>

						<cfsavecontent variable="local.printRenewalDate">
							<cfoutput>
								<div>Renewal Offer Expiration Date: <cfif local.qryIndividuals.rescindDate NEQ "">#dateformat(local.qryIndividuals.rescindDate,"m/d/yyyy")#<cfelse>None Specified</cfif></div><br />
							</cfoutput>
						</cfsavecontent>

						<cfsavecontent variable="local.returnStr.resultMsg">
							<cfoutput>
							Renewal Summary:<br/>
							#local.returnStr.successCount# successful renewal<cfif local.returnStr.successCount is not 1>s</cfif><br/>
							#local.returnStr.warningCount# warning<cfif local.returnStr.warningCount is not 1>s</cfif><br/>
							#local.returnStr.errorCount# error<cfif local.returnStr.errorCount is not 1>s</cfif>
							</cfoutput>
						</cfsavecontent>
						<cfset CreateObject('component','model.system.platform.history').addSubRenewalUpdateHistory(orgID=local.thisNotificationOrgID,
							actorMemberID=local.thisNotificationRecordedByMemberID, mainMessage=local.returnStr.resultMsg, messages=local.returnStr.arrMessages, 
							renewals=local.returnStr.arrRenewMessages)>

						<cfsavecontent variable="local.thisEmailContent">
							<cfoutput>
							<p>We have completed processing your subscription renewals.</p>
							<cfif local.returnStr.errorCount+local.returnStr.warningCount gt 0>
								<p style="color:##f00;font-weight:bold;">There were issues adding some of the subscriptions.</p>	
							<cfelse>
								<p>Subscriptions were created successfully.</p>
							</cfif>

							<div>#local.returnStr.resultMsg#</div>
							<br/>

							<div>#local.printRenewalDate#<div>

							<div><b>Issues with Renewal</b></div>
							<cfif arrayLen(local.returnStr.arrMessages)>
								<cfset local.currentMemberName = "">
								<cfloop array="#local.returnStr.arrMessages#" index="local.thisMessage">
									<cfif local.currentMemberName neq local.thisMessage.memberName>
										<cfset local.currentMemberName = local.thisMessage.memberName>
										<br/>
										<div>#local.thisMessage.memberName#</div>
									</cfif>
									<div>#local.thisMessage.errMessage#</div>
								</cfloop>
							<cfelse>
								<div><i>(none)</i></div>
							</cfif>

							<br/>
							<div><b>Successful Renewals</b></div>
							<cfif arrayLen(local.returnStr.arrRenewMessages)>
								<cfset local.currentMemberName = "">
								<cfloop array="#local.returnStr.arrRenewMessages#" index="local.thisMessage">
									<cfif local.currentMemberName neq local.thisMessage.memberName>
										<cfset local.currentMemberName = local.thisMessage.memberName>
										<br/>
										<div>#local.thisMessage.memberName#</div>
									</cfif>
									<div>#local.thisMessage.errMessage#</div>
								</cfloop>
							<cfelse>
								<div><i>(none)</i></div>
							</cfif>
							</cfoutput>
						</cfsavecontent>
							
						<cfset local.emailTitle = "#local.thisSiteName# Subscription Renewal Report">
						<cfscript>
							local.arrEmailTo = [];
							
							local.toEmailArr = listToArray(local.thisReportEmail,';');
							for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
								local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
							}
						</cfscript>
						<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name='MemberCentral', email='<EMAIL>' },
							emailto=local.arrEmailTo,
							emailreplyto="<EMAIL>",
							emailsubject="Subscription Renewal Report for #local.thisSiteName# - #application.mailservers.environmentHeaders["X-MCENVIRONMENT"]#",
							emailtitle=local.emailTitle,
							emailhtmlcontent=local.thisEmailContent,
							emailAttachments=[],
							siteID=local.mc_siteinfo.siteID,
							memberID=local.qryNotifications.recordedByMemberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
							sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID
						)>

						<!--- ------------- --->
						<!--- UPDATE STATUS --->
						<!--- ------------- --->
						<cfquery name="local.updateStatusInMass" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;
	
							DECLARE @statusDone int;
							EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionRenew', @queueStatus='done', @queueStatusID=@statusDone OUTPUT;

							UPDATE dbo.queue_subscriptionRenew
							SET statusID = @statusDone,
								dateUpdated = getdate()
							WHERE itemGroupUID = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#local.qryNotifications.itemGroupUID#">;
						</cfquery>

					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
					</cfcatch>
					</cftry>
				</cfloop>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>			

		<!--- -------------------------------------------------------- --->
		<!--- post processing - delete any itemGroupUIDs that are done --->
		<!--- -------------------------------------------------------- --->
		<cftry>
			<cfstoredproc procedure="queue_subscriptionRenew_clearDone" datasource="#application.dsn.platformQueue.dsn#">
			</cfstoredproc>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>			

		<cfreturn local.success>
	</cffunction>

	<cffunction name="setErrorMessageForEntry" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="message" type="string" required="true">
		<cfargument name="errCode" type="string" required="true">

		<cfset var qryUpdateQueueItem = "">

		<cfquery datasource="#application.dsn.platformQueue.dsn#" name="qryUpdateQueueItem">
			SET NOCOUNT ON;

			DECLARE @itemID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.itemID#">;

			<!--- deadlocks --->
			<cfif arguments.errCode EQ 'SQLDEADLOCK'>
				DECLARE @statusReady int;
				EXEC dbo.queue_getStatusIDbyType @queueType='subscriptionRenew', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

				UPDATE dbo.queue_subscriptionRenew
				SET statusID = @statusReady, 
					dateUpdated = GETDATE()
				WHERE itemID = @itemID;
			</cfif>

			UPDATE dbo.queue_subscriptionRenew
			SET errorMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="[#arguments.errCode#]-#arguments.message#">
			WHERE itemID = @itemID;
		</cfquery>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_subscriptionRenew;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>