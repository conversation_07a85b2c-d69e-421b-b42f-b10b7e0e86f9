<!--- bucket counts from cache --->
<cfif attributes.data.intSearchID gt 0 and attributes.data.searchAction eq "doSearch">
	<cfsavecontent variable="local.countsJS">
		<cfoutput>
		<script language="javascript">
			b_#attributes.data.intSearchID#_bcounts = new Object();
			<cfloop query="attributes.data.qryCachedCounts">
				b_#attributes.data.intSearchID#_bcounts["#attributes.data.qryCachedCounts.bucketid#"] = #attributes.data.qryCachedCounts.itemCount#;
			</cfloop>
		</script>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#ReReplace(local.countsJS,'\s{2,}',' ','ALL')#">
</cfif>

<cfsavecontent variable="local.searchCSS">
	<cfoutput>
	<style type="text/css">
		input.cal { background:##fff url(/assets/common/images/search/calendar.png) no-repeat right center; padding-right:18px; border:1px solid ##7f9db9; font-size:9pt; width:100px; }
		div.s_rbnm { padding:0 0 4px 0; }
		div.s_rnfne { padding:10px 0; font-weight:bold; }
		div.s_row { padding:6px; }
		div.s_row_alt { background-color:##DEDEDE; }
		div.s_dtl { margin-left:20px; margin-top:4px; }
		div.s_opt { padding:3px; }
		div.s_act { float:right; padding:0 0 5px 5px; }
		a.ctv { text-decoration:none; }
		div.s_pgbtm { padding:6px 3px 0px 3px;text-align:right; }
		div.s_pgtop { padding:3px 3px 6px 3px; }
		div.s_rhrd { background-color:##DEDEDE;padding:3px;margin-bottom:3px; min-height:18px; line-height:20px; }
		tr.s_rhrdtr { background-color:##DEDEDE;padding:3px; }
		div.s_nli { margin:10px 10px 0 0;padding:6px;border:1px solid ##ccc; }
		div.s_nliint { margin-bottom:10px; }
		div.s_options { margin-top:10px; clear:both;overflow:hidden;}
		div.s_err { font-weight:bold; color:##f00; }
		div.s_lh2 { padding:2px 0; }
		div##atcdiv { visibility:hidden; position:absolute; left:-300px; z-index:10000; }
		div.modalpopup { width:410px; }
		div##modalpopuphead { font-weight:bold; margin-bottom:6px; }
		div.modalpopupcontent { background-color:##BBB; border:1px solid ##666; border-bottom:3px solid ##666; color:##000; padding:10px; }
		select##modalpopupSuggestResults { position:absolute; visibility:hidden; }
		div.modalpopupinput { margin-top:6px; }
		div.modalpopuparrow { background-image:url(/assets/common/images/search/modal_triangler.gif); background-repeat:no-repeat; height:12px; position:relative; top:1px; left:385px; }
		div.modalpopuparrow span { display:none; }
		span##modalpopupreq { float:right; color:##f00; font-style:italic; font-weight:bold; }
		a.currSort { text-decoration:none; font-weight:bold; }
		div##s_caldiv { position:absolute;visibility:hidden;background-color:white;layer-background-color:white; }
	
		ul##s_tabs {
			border-bottom:1px solid ##ccc;
			margin:0; 
			padding-bottom:21px;
			padding-left:10px;
		}
		ul##s_tabs ul, ul##s_tabs li { display:inline; list-style-type:none; margin:0; padding:0; }	
		ul##s_tabs a:link, ul##s_tabs a:visited { background:##E8EBF0; border:1px solid ##ccc; color:##666; float:left; line-height:14px; margin-right:8px; padding:2px 10px; text-decoration:none; }
		ul##s_tabs a:link.active, ul##s_tabs a:visited.active { border-bottom:1px solid ##fff; color:##000; }
		ul##s_tabs a:hover { color:##f00; }
		div##s_tabsetSM ul##s_tabs li##nav_SM a, 
		div##s_tabsetSR ul##s_tabs li##nav_SR a, 
		div##s_tabsetNS ul##s_tabs li##nav_NS a, 
		div##s_tabsetRS ul##s_tabs li##nav_RS a, 
		div##s_tabsetAD ul##s_tabs li##nav_AD a, 
		div##s_tabsetVC ul##s_tabs li##nav_VC a { background:##fff; border-bottom:1px solid ##fff; color:##000; }
		ul##s_tabs ul a:hover { color:##f00 !important; }
		div##s_listing { 
			/*border:1px solid ##ccc; */
			border-top:none; 
			clear:both; 
			margin:0px; 
			padding:5px 0;
		}
		.bucketHeaderButton {-webkit-border-radius: 4px;-moz-border-radius: 4px;border-radius: 4px; padding: 2px;}
		.bucketListButton {-webkit-border-radius: 4px;-moz-border-radius: 4px;border-radius: 4px;width:100%; margin-bottom:10px;background-color:##a0ce67;color:##000000;font-size:9px;}
		.hiddenButton {display:none;}

		a.ctv{white-space: nowrap;}
		##searchSponsorRightSideContainer {float: right;text-align:center;}
		##searchSponsorRightSideContainer a {display:block;margin-top:12px;}
		##searchSponsorRightSideContainer img {max-width:125px;}
		##searchSponsorRightSideContainer ~ div.s_row {float:left;}
		<cfif local.rc.viewDirectory eq "default">
			##searchSponsorRightSideContainer ~ div.s_row {width:75%;}
			##searchSponsorRightSideContainer{width:20%;}
		</cfif>
		##viewDocPromptModal .modal-header {
			border-bottom:0px !important;
		} 
		##viewDocPromptModal .modal-body {
			margin: 0px 20px;
			background-color: ##f5f5f5;
		}
		##viewDocPromptModal .modal-footer {
			border-top: 0px;
			background: ##fff !important;
		}
		##viewDocPromptModal .viewDocPromptModalHeader{
			margin: 0px;
			color: ##000;
			font-size: 15px;
		}
		##viewDocPromptModal .close {
			font-size: 15px !important;
			opacity: 0.8 !important;
		}
		 .modal-backdrop.fade.in {
			opacity: 0.4 !important;
		}
		<!--- steps --->
		.wizard.vertical > .steps { width: 70px!important; }
		.wizard.vertical > .content { margin: 0 0 0.5em 0!important; width: calc(100% - 75px)!important; }
		<!--- .wizard.vertical > .actions { margin: 0!important; } --->
		.wizard > .actions a, .wizard > .actions a:hover, .wizard > .actions a:active { color: ##fff!important; }
		.wizard > .content { 
			min-height: 18em!important; background: ##fff!important;
			-webkit-border-radius: 0!important; -moz-border-radius: 0!important; border-radius: 0!important;
			border-left: 1px solid rgba(122,123,151,.2)!important;
		}
		.wizard > .content > .body { position: relative!important; padding-top: 0.5em!important; }
		.wizard > .steps a { text-align:center; }
		.wizard > .steps .number { font-size: 1em!important; }
		.dt-cell-exclude { background-color:##fff8f8!important;color:##d3d3d3!important; }
	</style>
	<script language="javascript">
		var strSearchReportBucketTypes = '#valuelist(attributes.data.BucketTypesForSearchReport.bucketTypeID)#';
		var arrSearchReportBucketTypes = strSearchReportBucketTypes.split(",");
		var #toScript(application.regEx.email, "mc_emailregex")#

		<!--- billing info --->
		<cfset local.qryStates = application.objCommon.getTSStates()>
		<cfset local.user_billingstate = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingState')>
		<cfset local.user_billingzip = application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingZip')>

		var tsStatesArray = JSON.parse('#JSStringFormat(SerializeJSON(local.qryStates))#').DATA;
		var vars_user_billingstate = '#local.user_billingstate#';
		var vars_user_billingzip = '#local.user_billingzip#';

		function showReport() {
			window.open('/?pg=searchReport&sid=#attributes.data.intSearchID#&mode=stream','searchReport','width=780,height=500');
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.searchCSS,'\s{2,}',' ','ALL')#">