<cfcomponent>

	<cffunction name="gather" access="package" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="showCOF" type="boolean" required="yes">
		<cfargument name="usePopup" type="boolean" required="yes">
		<cfargument name="usePopupDIVName" type="string" required="yes">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="hideSelect" type="boolean" required="no" default="0">
		<cfargument name="offerDelete" type="boolean" required="no" default="0">
		<cfargument name="autoShowForm" type="boolean" required="no" default="1">
		<cfargument name="adminForm" type="boolean" required="no" default="0">
		<cfargument name="overrideCustomerID" type="string" required="no" default="">
		<cfargument name="editMode" type="string" required="no" default="frontEndPayment" hint="frontEndPayment|frontEndManage|controlPanelPayment|controlPanelManage">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { jsvalidation='', inputForm='', headcode='' }>
		<cfset local.isValidSSID = application.objPlatformStats.isValidStatsSessionID()>

		<cfif NOT local.isValidSSID>
			<cfsavecontent variable="local.returnStruct.inputForm">
				<cfoutput>
				There is a problem with your browser that prevents us from loading the payment form.<br/><br/>
				Support staff have just been notified of this issue and will work to correct it as soon as possible.<br/><br/>
				If you continue to see this message, please contact Support for assistance and provide this error code: <b>BD-G-InvalidSession</b>.
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfif>

		<cfset structAppend(local, getUserPermissions(editMode=arguments.editMode), true)>
		
		<cfif not arguments.showCOF>
			<cfset local.allowReassociate = 0>
		</cfif>

		<cfset local.strAllowedBankAcctTypes = allowedAccountTypes(profileID=arguments.qryGateWayID.profileID, adminForm=arguments.adminForm)>
		<cfset local.qryAllProfilesOnFile = getProfilesOnFile(siteID=arguments.siteID, profileID=arguments.qryGateWayID.profileID, memberID=arguments.pmid, showCOF=arguments.showCOF, statsSessionID=session.cfcuser.statsSessionID)>

		<cfquery name="local.qryProfilesOnFile" dbtype="query">
			SELECT payProfileID, detail, nickname, routingNumber, accountNumber, acctType, dateAdded, 
				failedSinceDate, failedLastDate, nextAllowedAutoChargeDate, failedCount, lastUpdatedDate, 
				maxFailedAutoAttempts, addedByName, updatedByName, canRemoveCard, MPPayProfileGroup
			FROM local.qryAllProfilesOnFile
			WHERE 1 = 1
			<cfif arguments.editMode NEQ 'controlPanelManage'>
				<cfif NOT local.strAllowedBankAcctTypes.business>
					AND acctType <> 'Business'
				</cfif>
				<cfif NOT local.strAllowedBankAcctTypes.personal>
					AND acctType <> 'Personal'
				</cfif>
			</cfif>
			ORDER BY MPPayProfileGroup DESC
		</cfquery>

		<!--- get associated items if needed --->
		<cfif local.showAssociatedItems is 1>
			<cfset local.qryAssociatedInvoices = application.objPayments.getMemberPayProfileInvoices(memberID=arguments.pmid, includeOpenInvoices=local.includeOpenInvoices)>
			<cfset local.qryAssociatedSubscriptionTypes = application.objPayments.getMemberPayProfileSubscriptions(memberID=arguments.pmid)>
			<cfset local.qryAssociatedContributions = application.objPayments.getMemberPayProfileContributions(memberID=arguments.pmid)>
		</cfif>

		<cfif local.qryProfilesOnFile.recordcount is 0>
			<cfset local.hideCancelOnAdd = 1>
		<cfelse>
			<cfset local.hideCancelOnAdd = 0>
		</cfif>

		<cfset local.EncAddPayProfile = "<data><pmid>#arguments.pmid#</pmid><action>addPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><hcnl>#local.hideCancelOnAdd#</hcnl><adm>#arguments.adminForm#</adm><em>#arguments.editMode#</em></data>">
		<cfset local.EncAddPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
		<cfset local.EncAddPayProfileReturn = "<data><pmid>#arguments.pmid#</pmid><c>#arguments.showCOF#</c><s>#arguments.siteid#</s><o>#arguments.usePopup#</o><h>#arguments.hideSelect#</h><adm>#arguments.adminForm#</adm><od>#arguments.offerDelete#</od><d>#arguments.usePopupDIVName#</d><action>addPaymentProfileReturn</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><ocid>#xmlformat(arguments.overrideCustomerID)#</ocid><em>#arguments.editMode#</em></data>">
		<cfset local.EncAddPayProfileReturn = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfileReturn,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>

		<cfset local.isControlPanel = findNoCase("controlPanel",arguments.editMode) GT 0 OR arguments.adminForm EQ 1>
		<cfset local.isFrontEnd = NOT local.isControlPanel AND findNoCase("frontend",arguments.editMode) GT 0>
		<cfset local.cardWidth = 275>
		<cfset local.formActionWidth = arguments.hideSelect ? local.cardWidth : (local.cardWidth + 20)>
		<cfset local.loadingIcon = local.isFrontEnd ? '<i class="icon-spin icon-spinner"></i>' : '<i class="fa-light fa-circle-notch fa-spin fa-lg"></i>'>
	
		<cfsavecontent variable="local.returnStruct.headcode">
			<cfoutput>
			#getCommonStyles()#
			<script language="javascript">
				$(function() {
					if ($('##divManageFormWrapper#arguments.qryGateWayID.profileID#').length == 0)
						$('###arguments.usePopupDIVName#').after('<div id="divManageFormWrapper#arguments.qryGateWayID.profileID#" style="display:none;"><div id="iframeManageForm#arguments.qryGateWayID.profileID#Loading">#local.loadingIcon# Loading...<br/><br/></div><iframe name="iframeManageForm#arguments.qryGateWayID.profileID#" id="iframeManageForm#arguments.qryGateWayID.profileID#" frameborder="0" allowTransparency="true" scrolling="no" style="display:none;"></iframe></div>');

					<cfif NOT arguments.usePopup and arguments.autoShowForm is 1 and local.qryProfilesOnFile.recordcount is 0>
						p_#arguments.qryGateWayID.profileID#_addPaymentProfile();
					</cfif>
				});
	
				function p_#arguments.qryGateWayID.profileID#_resizeIFrame() {
					$('##iframeManageForm#arguments.qryGateWayID.profileID#Loading').hide();
					var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
					ifr.contents().find('body, div.container, body > div.bodyText').css({margin:0});
					ifr.contents().find('body, div.inner-content, body > div.container-fluid, div.container').css({padding:0});
					ifr.show();
					var nh = ifr.contents().find("div##zoneMain").height();
					if (nh > 0) ifr.attr('height',nh+40 + 'px');
				}
				function p_#arguments.qryGateWayID.profileID#_addPaymentProfile() {
					p_#arguments.qryGateWayID.profileID#_hideAlert();
					<cfif NOT arguments.usePopup>
						var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
						$('###arguments.usePopupDIVName#').hide();
						ifr.hide();
						ifr .off('load');
						ifr	.attr('width','100%')
							.attr('height','438px')
							.attr('src','/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfile)#')
							.load(function() { p_#arguments.qryGateWayID.profileID#_resizeIFrame(); });
						$('##divManageFormWrapper#arguments.qryGateWayID.profileID#,##iframeManageForm#arguments.qryGateWayID.profileID#Loading').show();
					<cfelse>
						top.$.colorbox( {innerWidth:470, innerHeight:480, href:'/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfile)#', iframe:true, overlayClose:false} );
					</cfif>
				}
				function p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(obj) {
					p_#arguments.qryGateWayID.profileID#_hideAlert();

					<!--- post message to control app when card is added/updated --->
					if (obj && obj.a && obj.a=='save' && obj.mccardevent && ['cardadded','cardupdated'].indexOf(obj.mccardevent.toLowerCase()) != -1) {
						var message = { success:true, 
										messagetype:"MCGatewayEvent", 
										eventname:obj.mccardevent, 
										profileid:#arguments.qryGateWayID.profileID#,
										payprofileid:obj.payprofileid };
						window.postMessage(message,'#JSStringFormat(getHostNameWithProtocol())#');
					}

					<cfif NOT arguments.usePopup>
						var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
						$('##divManageFormWrapper#arguments.qryGateWayID.profileID#').hide();
						$('###arguments.usePopupDIVName#').show();
						if (obj && obj.a && obj.a=='save') {
							$('##divInputFormWrapper#arguments.qryGateWayID.profileID#').attr('pofcount','0').html('<span>#local.loadingIcon# Reloading...</span>');
							ifr.attr('src','/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfileReturn)#');
						} else 
							ifr.attr('width','1px').attr('height','1px').attr('src','about:blank');
						if (obj && obj.err) p_#arguments.qryGateWayID.profileID#_showAlert(obj.err);
					<cfelse>
						if (obj && obj.a && obj.a=='save') {
							$('##divInputFormWrapper#arguments.qryGateWayID.profileID#').attr('pofcount','0').html('<span>#local.loadingIcon# Reloading...</span>');
							top.$.colorbox.close();
							$('##iframeManageForm#arguments.qryGateWayID.profileID#').attr('src','/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfileReturn)#');
						} else top.$.colorbox.close();
						if (obj && obj.err) p_#arguments.qryGateWayID.profileID#_showAlert(obj.err);
					</cfif>
				}
				function p_#arguments.qryGateWayID.profileID#_refreshPaymentProfile() {
					var objRet = new Object();
						objRet.a = 'save';
						objRet.mccardevent = 'cardRefreshed';
					if (p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
				}
				function p_#arguments.qryGateWayID.profileID#_editPaymentProfile(we) {
					p_#arguments.qryGateWayID.profileID#_hideAlert();
					<cfif NOT arguments.usePopup>
						var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
						$('###arguments.usePopupDIVName#').hide();
						ifr.hide();
						ifr .off('load');
						ifr	.attr('width','100%')
							.attr('height','438px')
							.attr('src','/?pg=buyNow&mode=direct&wizard=' + escape(we))
							.load(function() { p_#arguments.qryGateWayID.profileID#_resizeIFrame(); });
						$('##divManageFormWrapper#arguments.qryGateWayID.profileID#,##iframeManageForm#arguments.qryGateWayID.profileID#Loading').show();
					<cfelse>
						top.$.colorbox( {innerWidth:470, innerHeight:480, href:'/?pg=buyNow&mode=direct&wizard=' + escape(we), iframe:true, overlayClose:false} );
					</cfif>
				}
				function p_#arguments.qryGateWayID.profileID#_cancelForm(ppid) {
					$('##BDFrmContainer'+ppid).html('').hide();
				}
				<cfif arguments.offerDelete>
					function p_#arguments.qryGateWayID.profileID#_removePaymentProfile(ppid) {
						let BDRemoveTemplateSource = $('##mcp-bd-removecard-template#arguments.qryGateWayID.profileID#').html();
						var BDRemoveTemplate = Handlebars.compile(BDRemoveTemplateSource);
						$('##BDFrmContainer'+ppid).html('#local.loadingIcon# Please wait...');
						$('##BDFrmContainer'+ppid).slideToggle(300);
						$('##BDFrmContainer'+ppid).html(BDRemoveTemplate({payprofileid:ppid}));
					}
					function p_#arguments.qryGateWayID.profileID#_doRemovePaymentProfile(ppid) {
						$('##btnRemoveCard'+ppid).html('#local.loadingIcon# Removing...').prop("disabled", true);
						
						let removeResult = function(r) {
							if (r.success && r.success == 'true') {
								let objRet = { a:'save', mccardevent:'cardRemoved' };
								p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
							} else {
								alert('We were unable to remove this bank account. Try again.');
								$("##btnRemoveCard"+ppid).html('Yes, Remove Account').prop("disabled", false);
							}
						};

						let objParams = { pmid:#arguments.pmid#, payProfileID:ppid };
						TS_AJX('BANKDRAFT','removePaymentProfile',objParams,removeResult,removeResult,20000,removeResult);
					}
				</cfif>
				function p_#arguments.qryGateWayID.profileID#_reassignPaymentProfile(we) {
					p_#arguments.qryGateWayID.profileID#_hideAlert();
					<cfif NOT arguments.usePopup>
						var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
						$('###arguments.usePopupDIVName#').hide();
						ifr.hide();
						ifr .off('load');
						ifr	.attr('width','100%')
							.attr('height','210px')
							.attr('src','/?pg=buyNow&mode=direct&wizard=' + escape(we))
							.load(function() { p_#arguments.qryGateWayID.profileID#_resizeIFrame(); });
						$('##divManageFormWrapper#arguments.qryGateWayID.profileID#,##iframeManageForm#arguments.qryGateWayID.profileID#Loading').show();
					<cfelse>
						top.$.colorbox( {innerWidth:350, innerHeight:480, href:'/?pg=buyNow&mode=direct&wizard=' + escape(we), iframe:true, overlayClose:false} );
					</cfif>
				}
				<cfif local.showAssociatedItems is 1>
					function p_#arguments.qryGateWayID.profileID#_mppid_toggleAssocItems(x) {
						$('##p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_container_'+x+', ##p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_'+x+'_show, ##p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_'+x+'_hide').toggleClass('mcp-bd-d-none');
					}
				</cfif>
				function p_#arguments.qryGateWayID.profileID#_selectPayProfile(pid) {
					$('.payProfileWrapper#arguments.qryGateWayID.profileID#').removeClass('mcp-bd-card-selected');
					$('##payProfileWrapper#arguments.qryGateWayID.profileID#_'+pid).addClass('mcp-bd-card-selected');
				}
				function p_#arguments.qryGateWayID.profileID#_toggleCardInfo(pid) {
					$('##BDInfoContainer'+pid).slideToggle(300);
				}
				function p_#arguments.qryGateWayID.profileID#_hideAlert() { 
					$('##inputFormStepErr#arguments.qryGateWayID.profileID#').html('').hide(); 
					p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				}
				function p_#arguments.qryGateWayID.profileID#_showAlert(msg) { 
					$('##inputFormStepErr#arguments.qryGateWayID.profileID#').html(msg).show(); 
					p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				}
			</script>
			<cfif arguments.offerDelete>
				<script id="mcp-bd-removecard-template#arguments.qryGateWayID.profileID#" type="text/x-handlebars-template">
					<div class="mcp-bd-font-weight-bold">Remove Bank Account</div>
					<div class="mcp-bd-alert-warning mcp-bd-p-2 mcp-bd-font-size-sm mcp-bd-mt-2 mcp-bd-mb-2">This action is permanent.<br/>Are you sure you want to remove this bank account?</div>
					<div>
						<button type="button" id="btnRemoveCard{{payprofileid}}" class="btn btn-sm btn-danger" onclick="p_#arguments.qryGateWayID.profileID#_doRemovePaymentProfile({{payprofileid}});">Yes, Remove Account</button>
						<button type="button" class="btn btn-sm btn-secondary mcp-bd-ml-2" onclick="p_#arguments.qryGateWayID.profileID#_cancelForm({{payprofileid}});">Cancel</button>
					</div>
				</script>
			</cfif>
			</cfoutput>
		</cfsavecontent>
	
		<cfsavecontent variable="local.returnStruct.jsvalidation">
			<cfoutput>
			if ($('##divInputFormWrapper#arguments.qryGateWayID.profileID#').attr("pofcount") == 0) arrReq[arrReq.length] = 
				<cfif not arguments.showCOF>
					'Enter your bank account to continue.';
				<cfelse>
					'There are no bank accounts on file. Add a bank account to continue.';
				</cfif>
			</cfoutput>
		</cfsavecontent>
	
		<cfsavecontent variable="local.returnStruct.inputForm">
			<cfoutput>
			<div id="divInputFormWrapper#arguments.qryGateWayID.profileID#" pofcount="#local.qryProfilesOnFile.recordcount#" class="mcp-bd-card-box mcp-bd-shadow-none mcp-bd-p-3 mcp-bd-mb-3 mcp-bd-w-sm-100" style="width:#arguments.hideSelect?320:340#px;min-height:150px;font-size:15px;box-sizing:border-box;">
				<div id="inputFormStepErr#arguments.qryGateWayID.profileID#" class="mcp-bd-text-danger mcp-bd-font-size-sm mcp-bd-mb-2 mcp-bd-d-none"></div>

				<cfif local.qryProfilesOnFile.recordcount is 0>
					<cfif arguments.showCOF>
						<div class="mcp-bd-mb-3">There are no bank accounts on file.</div>
					</cfif>
				<cfelse>
					
					<cfset local.radioSelected = false>
					<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>
					
					<cfoutput query="local.qryProfilesOnFile" group="MPPayProfileGroup">
						<div class="mcp-bd-font-size-md mcp-bd-font-weight-bold mcp-bd-mb-4 mcp-bd-border-bottom mcp-bd-pb-1">#local.qryProfilesOnFile.MPPayProfileGroup#</div>
						<cfoutput>
						<cfset local.EncEditPayProfile = "<data><pmid>#arguments.pmid#</pmid><action>editPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><ppid>#local.qryProfilesOnFile.payProfileID#</ppid><rtn>#local.qryProfilesOnFile.routingNumber#</rtn><acn>#local.qryProfilesOnFile.accountNumber#</acn><adm>#arguments.adminForm#</adm><em>#arguments.editMode#</em></data>">
						<cfset local.EncEditPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncEditPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
						<cfif local.allowReassociate and local.qryProfilesOnFile.recordcount gt 1>
							<cfset local.EncReassignPayProfile = "<data><pmid>#arguments.pmid#</pmid><action>reassignPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><rtn>#local.qryProfilesOnFile.routingNumber#</rtn><acn>#local.qryProfilesOnFile.accountNumber#</acn><em>#arguments.editMode#</em><ioi>#local.includeOpenInvoices#</ioi></data>">
							<cfset local.EncReassignPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncReassignPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
						</cfif>

						<cfset local.addPaymentEncString = "">

						<cfif local.showAssociatedItems is 1>
							<cfquery name="local.qryAssociatedInvoicesThisCard" dbtype="query">
								select payProfileID, profileName, invoiceID, invoicenumber, dateDue, amountDue, status
								from [local].qryAssociatedInvoices
								where payProfileID = #local.qryProfilesOnFile.payProfileID#
								order by dateDue, invoicenumber
							</cfquery>
							<cfquery name="local.qryAssociatedInvoicesThisCardOverdue" dbtype="query">
								select invoiceID, invoicenumber, amountDue
								from [local].qryAssociatedInvoicesThisCard
								where dateDue < #now()#
							</cfquery>
							<cfquery name="local.qryThisBadCardtotals" dbtype="query">
								select count(*) as overdueInvoiceCount, sum(amountDue) as overdueAmount
								from [local].qryAssociatedInvoicesThisCardOverdue
							</cfquery>
							<cfif val(local.qryThisBadCardtotals.overdueAmount) gt 0>
								<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=arguments.pmid, t="Invoices #valuelist(local.qryAssociatedInvoicesThisCardOverdue.invoiceNumber)#", ta=local.qryThisBadCardtotals.overdueAmount, tmid=arguments.pmid, ad="v|#valuelist(local.qryAssociatedInvoicesThisCardOverdue.invoiceID)#")>
							</cfif>

							<cfquery name="local.qryAssociatedSubscriptionTypesThisCard" dbtype="query">
								select typename, subscriptionName
								from [local].qryAssociatedSubscriptionTypes
								where payProfileID = #local.qryProfilesOnFile.payProfileID#
								order by typename, subscriptionName
							</cfquery>

							<cfquery name="local.qryAssociatedContributionsThisCard" dbtype="query">
								select programName
								from [local].qryAssociatedContributions
								where payProfileID = #local.qryProfilesOnFile.payProfileID#
								order by programName
							</cfquery>
						</cfif>

						<div id="payProfileContainer#arguments.qryGateWayID.profileID#_#local.qryProfilesOnFile.payProfileID#" class="payProfileContainer#arguments.qryGateWayID.profileID# mcp-bd-mb-3">
							<div class="mcp-bd-d-flex">
								<cfif NOT arguments.hideSelect>
									<cfif NOT local.radioSelected>
										<cfset local.radioAttribute = "checked">
										<cfset local.radioSelected = true>
									<cfelse>
										<cfset local.radioAttribute = "">
									</cfif>
									<input type="radio" name="p_#arguments.qryGateWayID.profileID#_mppid" value="#local.qryProfilesOnFile.payProfileID#" class="mcp-bd-align-self-center mcp-bd-mr-2" onclick="p_#arguments.qryGateWayID.profileID#_selectPayProfile(#local.qryProfilesOnFile.payProfileID#);" #local.radioAttribute#>
								</cfif>
								<cfset local.highlightFailedCard = listFindNoCase("frontEndManage,controlPanelManage,controlPanelPayment",arguments.editMode) AND len(local.qryProfilesOnFile.failedSinceDate)>
								<div id="payProfileWrapper#arguments.qryGateWayID.profileID#_#local.qryProfilesOnFile.payProfileID#" class="payProfileWrapper#arguments.qryGateWayID.profileID# mcp-bd-card-box#local.highlightFailedCard ? ' mcp-bd-border-danger mcp-bd-border-2' : ' mcp-bd-border-1'#<cfif NOT arguments.hideSelect AND local.radioAttribute EQ "checked"> mcp-bd-card-selected</cfif>" style="width:#local.cardWidth#px;">
									<div class="mcp-card-body mcp-bd-p-2">
										<div class="mcp-bd-cof-info">
											<div class="mcp-bd-d-flex mcp-bd-align-items-center">
												<img src="/assets/common/images/payment/check.png" style="width:25px;" class="mcp-bd-mr-3">
												<div class="mcp-bd-d-flex mcp-bd-flex-column">
													<div class="mcp-bd-font-size-lg mcp-bd-font-weight-bold">**** #right(local.qryProfilesOnFile.detail,4)#</div>
													<cfif len(local.qryProfilesOnFile.nickname)><div class="mcp-bd-font-size-sm mcp-bd-mt-1">#local.qryProfilesOnFile.nickname#</div></cfif>
												</div>
												<cfset local.arrActions = []>
												<cfset local.arrActions.append({ "title":"Update Account", "clickFn":"p_#arguments.qryGateWayID.profileID#_editPaymentProfile('#JSStringFormat(local.EncEditPayProfile)#');",
															"cpIconClass":"fa-solid fa-pen", "feIconClass":"icon-pencil" })>
												<cfif arguments.offerDelete>
													<cfset local.arrActions.append({ "title":"Remove Account", "clickFn":"p_#arguments.qryGateWayID.profileID#_removePaymentProfile(#local.qryProfilesOnFile.payProfileID#);",
															"cpIconClass":"fa-solid fa-trash-alt", "feIconClass":"icon-trash" })>
												</cfif>
												<cfif listFindNoCase("controlPanelPayment,controlPanelManage",arguments.editMode) OR arguments.showCOF>
													<cfset local.arrActions.append({ "title":"Account Info", "clickFn":"p_#arguments.qryGateWayID.profileID#_toggleCardInfo(#local.qryProfilesOnFile.payProfileID#);",
															"cpIconClass":"fa-solid fa-circle-info", "feIconClass":"icon-info-sign", "anchorClass":"#local.highlightFailedCard ? 'mcp-bd-text-danger' : ''#" })>
												</cfif>
												<cfif local.showPayOverdueNow is 1 and len(local.addPaymentEncString)>
													<cfset local.arrActions.append({ "title":"Pay Overdue Invoices Now", "clickFn":"top.addPayment('#local.addPaymentEncString#');",
															"cpIconClass":"fa-solid fa-money-bill", "feIconClass":"icon-usd" })>
												</cfif>
												<div class="mcp-bd-ml-auto mcp-bd-cof-actions">
													<cfif local.highlightFailedCard>
														<a href="##" class="mcp-bd-text-danger #local.isFrontEnd ? 'mcp-bd-mr-2' : 'mcp-bd-mr-1'#" onclick="p_#arguments.qryGateWayID.profileID#_toggleCardInfo(#local.qryProfilesOnFile.payProfileID#);return false;">
															<i class="#local.isFrontEnd ? 'icon-info-sign' : 'fa-solid fa-circle-info'#"></i>
														</a>
													</cfif>
													<div class="mcp-bd-dropdown" tabindex="1">
														<i class="mcp-bd-dropbtnmask" tabindex="1"></i>
														<span class="mcp-bd-dropbtn mcp-bd-font-size-xl" role="listbox" aria-label="Actions"><i class="<cfif local.isFrontEnd>icon-ellipsis-horizontal<cfelse>fa-solid fa-ellipsis-stroke</cfif>"></i></span>
														<div class="mcp-bd-dropdown-content mcp-bd-font-size-xs">
															<cfloop array="#local.arrActions#" index="local.thisAction">
																<a href="##" class="mcp-bd-col-auto#local.thisAction.keyExists('anchorClass') ? ' #local.thisAction.anchorClass#' : ''#" title="#local.thisAction.title#" onclick="#local.thisAction.clickFn#return false;">
																	<i class="<cfif local.isFrontEnd>#local.thisAction.feIconClass#<cfelse>#local.thisAction.cpIconClass#</cfif>" style="width:20px;"></i>
																	<span class="mcp-bd-ml-2">#local.thisAction.title#</span>
																</a>
															</cfloop>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div id="BDFrmContainer#local.qryProfilesOnFile.payProfileID#" class="mcp-bd-mt-3" style="display:none"></div>
										<cfif arguments.showCOF>
											<div id="BDInfoContainer#local.qryProfilesOnFile.payProfileID#" class="mcp-bd-mt-3 mcp-bd-font-size-xs" style="display:none">
												<div class="mcp-bd-d-flex mcp-bd-mb-2">
													<div class="mcp-bd-col"><span class="mcp-bd-text-dim">Added</span><br/>#DateTimeFormat(local.qryProfilesOnFile.dateAdded,"m/d/yy h:nn tt")#<cfif local.showAddedBy is 1 and len(local.qryProfilesOnFile.addedByName)> by #local.qryProfilesOnFile.addedByName#</cfif></div>
												</div>
												<cfif len(local.qryProfilesOnFile.lastUpdatedDate)>
													<div class="mcp-bd-d-flex mcp-bd-mb-2">
														<div class="mcp-bd-col"><span class="mcp-bd-text-dim">Updated</span><br/>#DateTimeFormat(local.qryProfilesOnFile.lastUpdatedDate,"m/d/yy h:nn tt")#<cfif local.showlastUpdater is 1 and len(local.qryProfilesOnFile.updatedByName)> by #local.qryProfilesOnFile.updatedByName#</cfif></div>
													</div>
												</cfif>
												<cfif local.showFailureInfo is 1 and len(local.qryProfilesOnFile.failedSinceDate)>
													<div class="mcp-bd-cof_fail">
														<cfif DateCompare(local.qryProfilesOnFile.failedSinceDate,local.qryProfilesOnFile.failedLastDate)>
															Failed <cfif local.qryProfilesOnFile.failedCount is 1>once on<cfelseif val(local.qryProfilesOnFile.failedCount) gt 1>#local.qryProfilesOnFile.failedCount# times since</cfif> #dateformat(local.qryProfilesOnFile.failedSinceDate,"m/d/yyyy")#
															&bull; 
															Last attempted #dateformat(local.qryProfilesOnFile.failedLastDate,"m/d/yyyy")# 
														<cfelse>
															Failed on #dateformat(local.qryProfilesOnFile.failedSinceDate,"m/d/yyyy")#
														</cfif>
														<cfif local.showAssociatedItems is 1 and local.qryAssociatedInvoicesThisCard.recordcount>
															<cfif len(local.qryProfilesOnFile.nextAllowedAutoChargeDate) and local.qryProfilesOnFile.nextAllowedAutoChargeDate gt now() and ((val(local.qryProfilesOnFile.maxFailedAutoAttempts) eq 0) or (local.qryProfilesOnFile.failedCount lt local.qryProfilesOnFile.maxFailedAutoAttempts))>
																&bull; Next attempt will be #dateformat(local.qryProfilesOnFile.nextAllowedAutoChargeDate,"m/d/yyyy")#
															<cfelseif ((val(local.qryProfilesOnFile.maxFailedAutoAttempts) gt 0) and (local.qryProfilesOnFile.failedCount gte local.qryProfilesOnFile.maxFailedAutoAttempts))>
																&bull; Maximum attempts reached
															</cfif>
														</cfif>
													</div>
												</cfif>
												<div class="mcp-bd-text-center"><a href="##" onclick="p_#arguments.qryGateWayID.profileID#_toggleCardInfo(#local.qryProfilesOnFile.payProfileID#);return false;">Hide Info</a></div>
											</div>
										</cfif>
									</div>
								</div>
							</div>
							<cfif arguments.showCOF AND local.showAssociatedItems is 1 and local.qryAssociatedSubscriptionTypesThisCard.recordcount + local.qryAssociatedInvoicesThisCard.recordcount + local.qryAssociatedContributionsThisCard.recordcount gt 0>
								<div id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_#local.qryProfilesOnFile.currentrow#" style="margin-top:3px;">
									<div class="mcp-bd-card-box" style="width:#local.cardWidth#px;border-radius:0.5rem;">
										<div class="mcp-bd-card-header" id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_heading_#local.qryProfilesOnFile.currentrow#" style="cursor:pointer;" onclick="p_#arguments.qryGateWayID.profileID#_mppid_toggleAssocItems(#local.qryProfilesOnFile.currentrow#);">
											<div class="mcp-bd-d-flex">
												<span>Associated With This Account</span>
												<div style="margin-left:auto;">
													<span id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_#local.qryProfilesOnFile.currentrow#_show">
														<cfif arguments.editMode EQ 'frontEndManage'>
															<i class="icon-chevron-right"></i>
														<cfelse>
															<i class="fas fa-angle-right"></i>
														</cfif>
													</span>
													<span id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_#local.qryProfilesOnFile.currentrow#_hide" class="mcp-bd-d-none">
														<cfif arguments.editMode EQ 'frontEndManage'>
															<i class="icon-chevron-down"></i>
														<cfelse>
															<i class="fas fa-angle-down"></i>
														</cfif>
													</span>
												</div>
											</div>
										</div>
										<div id="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_container_#local.qryProfilesOnFile.currentrow#" class="mcp-bd-d-none" aria-labelledby="p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_heading_#local.qryProfilesOnFile.currentrow#" data-parent="##p_#arguments.qryGateWayID.profileID#_mppid_assoc_items_#local.qryProfilesOnFile.currentrow#" style="padding:5px;">
											<cfif len(local.addPaymentEncString) OR (local.allowReassociate and local.qryProfilesOnFile.recordcount gt 1)>
												<div class="mcp-bd-d-flex" style="margin-bottom:10px;border-top:1px solid ##ccc;padding:5px;">
													<cfif len(local.addPaymentEncString)>
														<div class="mcp-bd-col">
															<div>OverDue</div>
															<div style="font-size:16px;">#dollarformat(local.qryThisBadCardtotals.overdueAmount)#</div>
															<cfif local.showPayOverdueNow is 1 and len(local.addPaymentEncString)>
																<div>
																	<button type="button" class="btn<cfif arguments.editMode NEQ 'frontEndManage'> btn-xs btn-primary</cfif>" onclick="top.addPayment('#local.addPaymentEncString#');">Pay</button>
																</div>
															</cfif>
														</div>
													</cfif>
													<cfif local.allowReassociate and local.qryProfilesOnFile.recordcount gt 1>
														<div class="mcp-bd-col">
															<div>Move to Another Account</div>
															<div>
																<button type="button" class="btn<cfif arguments.editMode NEQ 'frontEndManage'> btn-xs btn-primary</cfif>" onclick="p_#arguments.qryGateWayID.profileID#_reassignPaymentProfile('#JSStringFormat(local.EncReassignPayProfile)#');">Reassign</button>
															</div>
														</div>
													</cfif>
												</div>
											</cfif>
											<div style="border-top:1px solid ##ccc;max-height:285px;overflow-y:auto;">
												<ul style="padding:10px 5px;">
													<cfif local.qryAssociatedSubscriptionTypesThisCard.recordcount>
														<cfloop query="local.qryAssociatedSubscriptionTypesThisCard">
															<li title="Subscription">#local.qryAssociatedSubscriptionTypesThisCard.typename#: #local.qryAssociatedSubscriptionTypesThisCard.subscriptionName#</li>
														</cfloop>
													</cfif>
													<cfif local.qryAssociatedContributionsThisCard.recordcount>
														<cfloop query="local.qryAssociatedContributionsThisCard">
															<li title="Contribution">#local.qryAssociatedContributionsThisCard.programName#</li>
														</cfloop>
													</cfif>
													<cfif local.qryAssociatedInvoicesThisCard.recordcount>
														<cfloop query="local.qryAssociatedInvoicesThisCard">
															<li title="Invoice">
																<cfif local.qryAssociatedInvoicesThisCard.dateDue lt now()>
																	<div style="color:red;">Invoice #local.qryAssociatedInvoicesThisCard.invoicenumber#<cfif local.includeOpenInvoices is 1> (#local.qryAssociatedInvoicesThisCard.status#)</cfif> &bull; #local.qryAssociatedInvoicesThisCard.profileName# &bull; #dollarformat(local.qryAssociatedInvoicesThisCard.amountDue)# due on #dateformat(local.qryAssociatedInvoicesThisCard.dateDue,"m/d/yyyy")#</div>
																<cfelse>
																	<div class="small">Invoice #local.qryAssociatedInvoicesThisCard.invoicenumber#<cfif local.includeOpenInvoices is 1> (#local.qryAssociatedInvoicesThisCard.status#)</cfif> &bull; #local.qryAssociatedInvoicesThisCard.profileName# &bull; #dollarformat(local.qryAssociatedInvoicesThisCard.amountDue)# due on #dateformat(local.qryAssociatedInvoicesThisCard.dateDue,"m/d/yyyy")#</div>
																</cfif>
															</li>
														</cfloop>
													</cfif>
												</ul>
											</div>
										</div>
									</div>
								</div>
							</cfif>
						</div>
						</cfoutput>
					</cfoutput>
				</cfif>
				<cfif arguments.showCOF OR local.qryProfilesOnFile.recordcount is 0>
					<div class="mcp-bd-mt-1">
						<a href="##" class="mcp-bd-font-size-sm" onclick="p_#arguments.qryGateWayID.profileID#_addPaymentProfile();return false;">
							<cfif local.isFrontEnd><i class="icon-plus-sign"></i><cfelse><i class="fa-solid fa-circle-plus"></i></cfif> Add bank account
						</a>
					</div>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getInputFormLoaderData" access="package" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileCode" type="string" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="formHolderElementID" type="string" required="yes">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="editMode" type="string" required="no" default="frontEndManageV2" hint="frontEndManageV2">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=true, errMsg="", headcode='', strActionFn:{}, arrMemProfilesOnFile:[] }>
		<cfset local.qryProfilesOnFile = getProfilesOnFile(siteID=arguments.siteID, profileID=arguments.qryGateWayID.profileID, memberID=arguments.pmid, showCOF=0, statsSessionID=session.cfcuser.statsSessionID, skipStatsSessionID=1)>

		<cfset local.EncAddPayProfile = "<data><pmid>#arguments.pmid#</pmid><action>addPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><hcnl>0</hcnl><adm>0</adm><em>#arguments.editMode#</em></data>">
		<cfset local.EncAddPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>

		<cfsavecontent variable="local.returnStruct.headcode">
			<cfoutput>
			<script language="javascript">
				function p_#arguments.qryGateWayID.profileID#_addPaymentProfile() {
					var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
					$('###arguments.formHolderElementID#').hide();
					ifr.hide();
					ifr .off('load');
					ifr	.attr('width','100%')
						.attr('height','700px')
						.attr('src','/?pg=buyNow&mode=direct&wizard=#JSStringFormat(local.EncAddPayProfile)#')
						.load(function() { p_#arguments.qryGateWayID.profileID#_resizeIFrame(); });
					$('##divManageFormWrapper#arguments.qryGateWayID.profileID#').show();
				}
				function p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(obj) {
					var formHolderEl = $('###arguments.formHolderElementID#');
					var refID = formHolderEl.data('refid');

					if (obj && obj.a && obj.a=='save') {
						var onsavefn = eval(formHolderEl.data('onsavefn'));
						if(onsavefn) onsavefn(obj,refID);
						else p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(false);
					}
					else if (obj && obj.a && obj.a=='cancel') {
						var oncancelfn = eval(formHolderEl.data('oncancelfn'));
						if(oncancelfn) oncancelfn(obj,refID);
						else p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(false);
					}
					else ifr.attr('width','1px').attr('height','1px').attr('src','about:blank');

					if (obj && obj.err) {
						var onerrorfn = eval(formHolderEl.data('onerrorfn'));
						if(onerrorfn) onerrorfn(obj.err,refID);
						p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(false);
					}
				}
				function p_#arguments.qryGateWayID.profileID#_editPaymentProfile(we) {
					var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
					$('###arguments.formHolderElementID#').hide();
					ifr.hide();
					ifr .off('load');
					ifr	.attr('width','100%')
						.attr('height','438px')
						.attr('src','/?pg=buyNow&mode=direct&wizard=' + escape(we))
						.load(function() { p_#arguments.qryGateWayID.profileID#_resizeIFrame(); });

					p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(true);
				}
				function p_#arguments.qryGateWayID.profileID#_removePaymentProfile(ppid) {
					let removeResult = function(r) {
						if (r.success && r.success == 'true') {
							let objRet = { a:'save', mccardevent:'cardRemoved' };
							p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
							
						} else {
							let objRet = { err:'We were unable to remove this bank account. Try again.' };
							p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
						}
					};

					let objParams = { pmid:#arguments.pmid#, payProfileID:ppid };
					TS_AJX('BANKDRAFT','removePaymentProfile',objParams,removeResult,removeResult,20000,removeResult);
				}
				function p_#arguments.qryGateWayID.profileID#_resizeIFrame() {
					$('##iframeManageForm#arguments.qryGateWayID.profileID#Loading').hide();
					var ifr = $('##iframeManageForm#arguments.qryGateWayID.profileID#');
					ifr.contents().find('body, div.container, body > div.bodyText').css({margin:0});
					ifr.contents().find('body, div.inner-content, body > div.container-fluid, div.container').css({padding:0});
					ifr.show();
					var nh = ifr.contents().find("div##zoneMain").height();
					if (nh > 0) ifr.attr('height',nh+40 + 'px');
				}
				function p_#arguments.qryGateWayID.profileID#_toggleFormWrapper(f) {
					$('##divManageFormWrapper#arguments.qryGateWayID.profileID#,##iframeManageForm#arguments.qryGateWayID.profileID#Loading').toggle(f);
				}
				function p_#arguments.qryGateWayID.profileID#_initFormLoader() { 
					if ($('##divManageFormWrapper#arguments.qryGateWayID.profileID#').length == 0)
						$('###arguments.formHolderElementID#').after('<div id="divManageFormWrapper#arguments.qryGateWayID.profileID#" style="display:none;"><div id="iframeManageForm#arguments.qryGateWayID.profileID#Loading"><i class="icon-spin icon-spinner"></i> <span style="margin-left:3px;">Please Wait...</span><br/><br/></div><iframe name="iframeManageForm#arguments.qryGateWayID.profileID#" id="iframeManageForm#arguments.qryGateWayID.profileID#" frameborder="0" allowTransparency="true" scrolling="no" style="display:none;"></iframe></div>');
				}
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfset local.returnStruct.strActionFn["init"] = { "fnName": "p_#arguments.qryGateWayID.profileID#_initFormLoader" }>
		<cfset local.returnStruct.strActionFn["add"] = { "fnName": "p_#arguments.qryGateWayID.profileID#_addPaymentProfile" }>

		<cfloop query="local.qryProfilesOnFile">
			<cfset local.thisMemProfile = { "payProfileID": local.qryProfilesOnFile.payProfileID, "strActionFn": {} }>

			<cfset local.EncEditPayProfile = "<data><pmid>#arguments.pmid#</pmid><action>editPaymentProfile</action><profilecode>#xmlformat(arguments.profilecode)#</profilecode><ppid>#local.qryProfilesOnFile.payProfileID#</ppid><rtn>#local.qryProfilesOnFile.routingNumber#</rtn><acn>#local.qryProfilesOnFile.accountNumber#</acn><adm>0</adm><em>#arguments.editMode#</em></data>">
			<cfset local.EncEditPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncEditPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>

			<cfset local.thisMemProfile.strActionFn["edit"] = { "fnName": "p_#arguments.qryGateWayID.profileID#_editPaymentProfile", "fnParam": "#JSStringFormat(local.EncEditPayProfile)#" }>
			<cfset local.thisMemProfile.strActionFn["remove"] = { "fnName": "p_#arguments.qryGateWayID.profileID#_removePaymentProfile", "fnParam": "#local.qryProfilesOnFile.payProfileID#" }>

			<cfset arrayAppend(local.returnStruct.arrMemProfilesOnFile, local.thisMemProfile)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getProfilesOnFile" access="private" returntype="query" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="showCOF" type="boolean" required="yes">
		<cfargument name="statsSessionID" type="numeric" required="yes">
		<cfargument name="skipStatsSessionID" type="boolean" required="no" default="0">
	
		<cfset var qryProfilesOnFile = "">

		<!--- if we arent showing COF (if not logged in, for example), still need to show the ones added in this session --->
		<!--- merge will put active COF on the new memberID, but merged member may still be in session so we need force lookup of activeMemberID anyway --->
		<cfquery name="qryProfilesOnFile" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @orgID int, @mpProfileID int, @systemMemberID int, @cofMemberID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			set @mpProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">;

			select @orgID = orgID
			from dbo.sites
			where siteID = @siteID;

			select @systemMemberID = dbo.fn_ams_getMCSystemMemberID();
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_BIGINT">);

			select mpp.payProfileID, mpp.detail, mpp.nickname, b.routingNumber, b.accountNumber, b.acctType, mpp.dateAdded, 
				mpp.failedSinceDate, mpp.failedLastDate, mpp.nextAllowedAutoChargeDate, mpp.failedCount, mpp.lastUpdatedDate, 
				mp.maxFailedAutoAttempts, mp.siteID as MPProfileSiteID, 
				case when m.memberID <> @systemMemberID then am.firstname + ' ' + am.lastname else '' end as addedByName,
				case when m2.memberID <> @systemMemberID then am2.firstname + ' ' + am2.lastname else '' end as updatedByName,
				case 
				when exists (select invoiceID from dbo.tr_invoices where payProfileID = mpp.payProfileID and orgID = @orgID) then 0
				when exists (select subscriberID from dbo.sub_subscribers where payProfileID = mpp.payProfileID and orgID = @orgID) then 0
				else 1 end as canRemoveCard, b.acctType + ' Checking Accounts' as MPPayProfileGroup
			from dbo.tr_bankAccounts as b
			inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = b.MPPPayProfileID
				and mpp.memberID = @cofMemberID
				and mpp.status = 'A'
				<cfif NOT arguments.showCOF AND NOT arguments.skipStatsSessionID>
					and mpp.addedStatsSessionID = <cfqueryparam value="#arguments.statsSessionID#" cfsqltype="CF_SQL_INTEGER">
				</cfif>
			inner join dbo.mp_profiles as mp on mp.profileID = @mpProfileID
				and mp.siteID = @siteID
			inner join dbo.ams_members as m on m.orgID in (1,@orgID) and m.memberID = mpp.addedByMemberID
			inner join dbo.ams_members as am on am.orgID in (1,@orgID) and am.memberID = m.activeMemberID
			left outer join dbo.ams_members as m2 
				inner join dbo.ams_members as am2 on am2.orgID in (1,@orgID) and am2.memberID = m2.activeMemberID
				on m2.orgID in (1,@orgID) and m2.memberID = mpp.lastUpdatedByMemberID
			where b.orgID = @orgID
			ORDER BY mpp.payProfileID desc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryProfilesOnFile>
	</cffunction>
	
	<cffunction name="addPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">
		<cfargument name="hideCancel" type="boolean" required="yes">
		<cfargument name="adminForm" type="boolean" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
	
		<!--- prefill data --->
		<cfset local.strPrefill = { 
				fld_9_ = '',	
				fld_10_ = '',
				fld_10B_ = '',	
				fld_27_ = ''
			}>
		
		<cfreturn showPaymentProfileForm(qryGateWayID=arguments.qryGateWayID, qryGatewayProfileFields=arguments.qryGatewayProfileFields, EncSaveCardURL=arguments.EncSaveCardURL, strPrefill=local.strPrefill, hideCancel=arguments.hideCancel, adminForm=arguments.adminForm)>
	</cffunction>
	
	<cffunction name="editPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="payProfileId" type="numeric" required="yes">
		<cfargument name="routingNumber" type="string" required="yes">
		<cfargument name="accountNumber" type="string" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">
		<cfargument name="adminForm" type="boolean" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
	
		<!--- get info not stored in vault --->
		<cftry>
			<cfquery name="local.qryExtraInfo" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @cofMemberID int;
				select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pmid#">);

				select b.acctType, mpp.nickname
				from dbo.tr_bankAccounts as b
				inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = b.MPPPayProfileID
					and mpp.payProfileID = <cfqueryparam value="#arguments.payProfileId#" cfsqltype="CF_SQL_INTEGER">
					and mpp.memberID = @cofMemberID
					and mpp.status = 'A'
				where b.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.orgID#">
				and b.routingNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.routingNumber#">
				and b.accountNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.accountNumber#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
					
			<cfif local.qryExtraInfo.recordcount is 0>
				<cfthrow message="Unable to locate bank account on file.">
			</cfif>
			
			<cfset local.strPrefill = { 
				fld_9_ = arguments.routingNumber,	
				fld_10_ = 'XXXX' & right(arguments.accountNumber,4),
				fld_10B_ = 'XXXX' & right(arguments.accountNumber,4),
				fld_27_ = local.qryExtraInfo.acctType,
				nickname = local.qryExtraInfo.nickname
			}>
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<cfreturn showPaymentProfileForm(qryGateWayID=arguments.qryGateWayID, qryGatewayProfileFields=arguments.qryGatewayProfileFields, EncSaveCardURL=arguments.EncSaveCardURL, strPrefill=local.strPrefill, hideCancel=0, adminForm=arguments.adminForm)>
	</cffunction>
	
	<cffunction name="showPaymentProfileForm" access="private" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">
		<cfargument name="strPrefill" type="struct" required="yes">
		<cfargument name="hideCancel" type="boolean" required="yes">
		<cfargument name="adminForm" type="boolean" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
		
		<!--- put fields into str --->
		<cfset local.strFields = structNew()>
		<cfset local.strFieldsReq = structNew()>
		<cfloop query="arguments.qryGatewayProfileFields">
			<cfset local.strFields["#arguments.qryGatewayProfileFields.fieldName#"] = arguments.qryGatewayProfileFields.fieldID>
			<cfif arguments.qryGatewayProfileFields.isRequired>
				<cfset local.strFieldsReq["#arguments.qryGatewayProfileFields.fieldName#"] = arguments.qryGatewayProfileFields.fieldID>
			</cfif>
		</cfloop>
		<cfset local.defaultTemplateSettings = application.objSiteResource.getSiteResourceSettingsStruct(siteResourceID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultTemplateSiteResourceID)>
		
		<cfset local.formDisplayMode = local.defaultTemplateSettings.supportsBootstrap eq "true" or (session.keyExists("enableMobile") and session.enableMobile)
										? "responsive"
										: "default">

		<cfset local.strAllowedBankAcctTypes = allowedAccountTypes(profileID=arguments.qryGateWayID.profileID, adminForm=arguments.adminForm)>

		<cfif NOT local.strAllowedBankAcctTypes.business AND NOT local.strAllowedBankAcctTypes.personal>
			<cfsavecontent variable="local.returnStruct.html">
				<cfoutput>
				<cfif arguments.adminForm>
					Checking account information cannot be entered because there are no eligible Standard Entry Class Codes setup for this payment profile.
				<cfelse>
					Checking account information cannot be entered at this time. Contact your association for assistance.
				</cfif>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfif>
	
		<!--- validation js --->
		<cfsavecontent variable="local.returnStruct.head">
			<cfoutput>
			<style type="text/css">
			<cfif local.formDisplayMode EQ "responsive">
				.bankDraftFormContent .wellContainer { border: 1px solid ##c2c2c2; border-radius: 5px; padding-bottom: 15px; width: 375px;}
				.bankDraftFormContent .help-block { font-size:12px;color:##999;margin:0; }
				.bankDraftFormContent .crdInput { width: 98% !important; height: 37px !important; padding-left: 10px; border-color: ##bebebe;}
				.bankDraftFormContent .wellHeading { float: left; font-size: 20px;}
				.bankDraftFormContent ##err_bdfrm { width: 95%;  margin-bottom: 10px; }
				.bankDraftFormContent .control-label { margin-bottom: 2px !important; font-size:12px !important; font-weight:bold; }
				.bankDraftFormContent .control-group { margin-bottom: 6px !important; }
				.bankDraftFormContent .control-group input, .bankDraftFormContent .control-group select { margin-bottom: 4px !important;box-sizing:border-box !important; }
				.bankDraftFormContent input::placeholder {font-size: 12px;}
				.bankDraftFormContent input[type="text"] { border-radius: 4px !important; }
				@media only screen and (max-width: 766px) and (min-width: 370px) {
					.wellContainer .span6 {width: 50% !important;float: left;}
					.bankDraftFormContent .crdInput { width: 95% !important;}
				}
				@media only screen and (max-width: 430px) {
					.bankDraftFormContent .wellContainer {width:100%}
					.bankDraftFormContent .wellHeading { font-size: 18px;}
				}
				@media only screen and (max-width: 369px) {
					.bankDraftFormContent .crdInput { width: 89% !important;}
				}
				@media only screen and (max-width: 310px) {
					.bankDraftFormContent .crdInput { width: 86% !important;}
					.bankDraftFormContent ##err_bdfrm { width: 93%;}
					.bankDraftFormContent .wellHeading { float: none;}
				}
			<cfelse>
				.bankDraftFormContent .alert { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 
				.bankDraftFormContent .reviewmsg { background:##fff6bf; text-align:left; padding:5px 20px; border:2px solid ##fc6; }
				.bankDraftFormContent input[type=text], .bankDraftFormContent input[type=password], .bankDraftFormContent select { margin-top: 4px; margin-bottom: 0px; }
				.bankDraftFormContent input[type=text], .bankDraftFormContent input[type=password] { border: 1px solid ##7F9DB9; padding: 2px; }
				.bankDraftFormContent select { border: 1px solid ##7F9DB9; }
				.bankDraftFormContent input[type=text][disabled=disabled] { background-color: ##EBEBE4; }
				.bankDraftFormContent input.Disabled { background-color: ##EBEBE4; }
				.bankDraftFormContent .FieldGroupSeparator { background-color: ##e0e0e0; font-weight: bold; padding: 5px 10px; margin-bottom: 10px; }
				.bankDraftFormContent .FieldGroupSeparatorBillingInfo { margin-top: 15px; }
				.bankDraftFormContent .EditButtons { background-color: ##e0e0e0; padding: 5px; margin-top: 10px; }
				.bankDraftFormContent .FieldGroupSeparatorPaymentInfo { display: block; }
				.bankDraftFormContent .PaymentItemEditData { padding: 4px; }
				.bankDraftFormContent .BankInfo .DataLabelEdit, .bankDraftFormContent .EditButtons .DataLabelEdit { display: inline-block; text-align: right; width: 160px; }
				.bankDraftFormContent .BankInfo div.BIitem { margin-bottom:4px; }
				.bankDraftFormContent .DataLabelEdit, .bankDraftFormContent .DataLabelEdit2 { font-weight: bold; }
				.bankDraftFormContent .DataValEditRoutNum input, .bankDraftFormContent .DataValEditAcctNum input {width: 130px;}
				.bankDraftFormContent .help-block { font-size:12px;color:##999;margin:0; }
			</cfif>
			</style>
			<script language="javascript">
				function hideAlert() { 
					$('##err_bdfrm').html('').hide(); 
					if (parent.p_#arguments.qryGateWayID.profileID#_resizeIFrame) parent.p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				}
				function showAlert(msg) { 
					$('##err_bdfrm').html(msg).show(); 
					if (parent.p_#arguments.qryGateWayID.profileID#_resizeIFrame) parent.p_#arguments.qryGateWayID.profileID#_resizeIFrame();
				}
				function cancelIt() { 
					var obj = new Object();
						obj.a = 'cancel';
					if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(obj);
				}
				function _FB_hasValue(obj, obj_type) {
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){
						tmp = obj.value;
						tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
						if (tmp.length == 0) return false;
						else return true;
					} else if (obj_type == 'SELECT'){
						for (var i=0; i < obj.length; i++) {
							if (obj.options[i].selected){
								tmp = obj.options[i].value;
								tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
								if (tmp.length > 0) return true;
							}
						}
						return false;	
					} else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){
						if (obj.checked) return true;
						else return false;	
					} else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){
						if (obj.length == undefined && obj.checked) return true;
						else{
							for (var i=0; i < obj.length; i++){
								if (obj[i].checked) return true;
							}
						}
						return false;
					}else{
						return true;
					}
				}
				function valAcctNum(x) {
					if (_FB_hasValue(x, 'TEXT')) {
						var pattern=/^[a-zA-Z0-9 ]*$/;
						var matchStatus = pattern.test(x.value);
						return matchStatus;
					}
					return false;				
				}
				function verifyAcctNum(x,y) {
					if (_FB_hasValue(x, 'TEXT') && _FB_hasValue(y, 'TEXT')) {
						if(x.value.trim() == y.value.trim()) 
							return true;
						else 
							return false;
					}
					return false;				
				}
				function valRoutingNum(x) {
					if (_FB_hasValue(x, 'TEXT')) {
						var i, n, t, s, c;
						s = x.value;
						t = "";
						for (i = 0; i < s.length; i++) {
							c = parseInt(s.charAt(i), 10);
							if (c >= 0 && c <= 9) t = t + c;
						}
						if (t.length != 9) return false;
						n = 0;
						for (i = 0; i < t.length; i += 3) {
							n += parseInt(t.charAt(i), 10) * 3 + parseInt(t.charAt(i + 1), 10) * 7 + parseInt(t.charAt(i + 2), 10);
						}
						if (n != 0 && n % 10 == 0) return true;
					}
					return false;
				}
				function valDraftForm() {
					$('form##frmBankDraft button[type="submit"]').prop('disabled',true);
					hideAlert();
					var arrReq = new Array();
					var thisForm = document.forms["frmBankDraft"];
					
					if ($('##fld_27_').val() == '') arrReq[arrReq.length] = 'Select a Bank Account Type.';
					if (!valRoutingNum(thisForm['fld_9_'])) arrReq[arrReq.length] = 'Routing Number must be valid.';
					<cfif len(arguments.strPrefill.fld_10_)>if (thisForm['fld_#local.strFields["Account Number"]#_'].value.indexOf("XXXX") < 0) {</cfif>
						if (!valAcctNum(thisForm['fld_10_'])) arrReq[arrReq.length] = 'Account Number must be valid.';
					<cfif len(arguments.strPrefill.fld_10_)>}</cfif>
					if (!verifyAcctNum(thisForm['fld_10_'],thisForm['fld_10B_'])) arrReq[arrReq.length] = 'Account Numbers do not match.';
	
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						$('form##frmBankDraft button[type="submit"]').prop('disabled',false);
						return false;
					}
					return true;
				}
				function onReceivePaymentAppMessage(event) {
					if (window.location.href.indexOf(event.origin) === 0 && event.data.success && event.data.success == true && event.data.messagetype.toLowerCase() == 'mcfrontendpaymentevent' && event.data.profileid == #arguments.qryGateWayID.profileID#) {
						$('form##frmBankDraft button[type="submit"]').text(event.data.paymentbuttonname);
					} else {
						return false;
					}
				}

				$(function() {
					if (window.addEventListener) {
						window.addEventListener("message", onReceivePaymentAppMessage, false);
					} else if (window.attachEvent) {
						window.attachEvent("message", onReceivePaymentAppMessage);
					}

					$("##fld_10_").on('keydown', function(e) { 
						var keyCode = e.keyCode || e.which; 

						if (keyCode == 9 || keyCode == 13 ) { 
							$("##fld_10B_").prop('disabled', false);
						} 
					});

					$("##fld_10_").focusout(function(){
						$("##fld_10B_").prop('disabled', false);
					});

					$("##fld_10_,##fld_10B_").bind('copy paste cut',function(e) { 
						e.preventDefault(); 
					});

					<!--- let parent know that payment form is ready and if there is anything to broadcast --->
					parent.postMessage({ success:true, messagetype:'MCPaymentFormLoadEvent', profileid:#arguments.qryGateWayID.profileID# },'#JSStringFormat(getHostNameWithProtocol())#');
				});
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfif local.formDisplayMode EQ "responsive">
			<cfsavecontent variable="local.returnStruct.html">
				<cfoutput>
				<cfform name="frmBankDraft" id="frmBankDraft" method="post" action="/?pg=buyNow&mode=direct&wizard=#arguments.EncSaveCardURL#" onsubmit="return valDraftForm();">
					<div class="container-fluid bankDraftFormContent">
						<div class="row-fluid">
							<div class="wellContainer span5">
								<div class="well well-sm">
									<span class="wellHeading">Payment Information</span>
								</div>
								<div style="padding:0 10px;">
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_27_">Type of Bank Account</label>
												<select name="fld_27_" id="fld_27_" class="crdInput" onchange="hideAlert();">
													<option value=""></option>
													<cfif local.strAllowedBankAcctTypes.personal>
														<option value="Personal" <cfif arguments.strPrefill.fld_27_ eq 'Personal'>selected</cfif>>Personal Checking</option>
													</cfif>
													<cfif local.strAllowedBankAcctTypes.business>
														<option value="Business" <cfif arguments.strPrefill.fld_27_ eq 'Business'>selected</cfif>>Business Checking</option>
													</cfif>
												</select>
											</div>
										</div>
									</div>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_9_">Routing Number</label>
												<cfinput type="text" onkeypress="hideAlert();" id="fld_9_" name="fld_9_" class="crdInput" maxLength="9" value="#arguments.strPrefill.fld_9_#" autocomplete="off">
											</div>
										</div>
									</div>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_10_">Account Number</label>
												<div class="controls">
													<cfinput type="text" onkeypress="hideAlert();" id="fld_10_" name="fld_10_" class="crdInput" maxLength="17" value="#arguments.strPrefill.fld_10_#" autocomplete="off">
												</div>
											</div>
										</div>
									</div>
									<div class="row-fluid">
										<div class="span12">
											<div class="control-group">
												<label class="control-label" for="fld_10B_">Verify Account Number</label>
												<div class="controls">
													<cfinput type="text" disabled="disabled" onkeypress="hideAlert();" id="fld_10B_" name="fld_10B_" class="crdInput" maxLength="17" value="#arguments.strPrefill.fld_10B_#" autocomplete="off">
												</div>
											</div>
										</div>
									</div>
									<cfif StructKeyExists(arguments.strPrefill,"nickname")>
										<div class="row-fluid">
											<div class="span12">
												<div class="control-group">
													<label class="control-label" for="fld_nickname">Optional Account Nickname</label>
													<div class="controls">
														<cfinput type="text" id="fld_nickname" name="fld_nickname" class="crdInput" maxLength="30" value="#arguments.strPrefill.nickname#" autocomplete="off">
														<span class="help-block">For security, do NOT use the account number in the nickname.</span>
													</div>
												</div>
											</div>
										</div>
									</cfif>
									<div id="err_bdfrm" class="span12 alert alert-error" style="display:none;"></div>
									<div class="row-fluid">
										<div class="span12">
											<button class="btn btn-success" type="submit">Continue</button>
											<cfif arguments.hideCancel is 0><button class="btn btn-secondary" onclick="cancelIt()" type="button">Cancel</button> </cfif>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</cfform>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.returnStruct.html">
				<cfoutput>
				<cfform name="frmBankDraft" id="frmBankDraft" method="post" action="/?pg=buyNow&mode=direct&wizard=#arguments.EncSaveCardURL#" onsubmit="return valDraftForm();">
				<div class="bankDraftFormContent tsAppBodyText">
					<div class="PaymentItemEditData">
						<div class="FieldGroupSeparator FieldGroupSeparatorPaymentInfo">Payment Information</div>
						<div class="BankInfo">
							<div class="BIitem">
								<span class="DataLabelEdit">Type of Bank Account:&nbsp;</span>
								<select name="fld_27_" id="fld_27_" class="tsAppBodyText" onClick="hideAlert();">
									<option value=""></option>
									<cfif local.strAllowedBankAcctTypes.personal>
										<option value="Personal" <cfif arguments.strPrefill.fld_27_ eq 'Personal'>selected</cfif>>Personal Checking</option>
									</cfif>
									<cfif local.strAllowedBankAcctTypes.business>
										<option value="Business" <cfif arguments.strPrefill.fld_27_ eq 'Business'>selected</cfif>>Business Checking</option>
									</cfif>
								</select>
							</div>
							<div class="BIitem"><span class="DataLabelEdit">Routing Number:&nbsp;</span> <span class="DataValEditRoutNum"><cfinput autocomplete="off" class="tsAppBodyText" onkeypress="hideAlert();" id="fld_9_" name="fld_9_" maxLength="9" type="text" value="#arguments.strPrefill.fld_9_#"> *</span></div>
							<div class="BIitem"><span class="DataLabelEdit">Account Number:&nbsp;</span> <span class="DataValEditAcctNum"><cfinput autocomplete="off" class="tsAppBodyText" onkeypress="hideAlert();" id="fld_10_" name="fld_10_" maxLength="17" type="text" value="#arguments.strPrefill.fld_10_#"> *</span></div>
							<div class="BIitem"><span><b>Verify Account Number:&nbsp;</b></span> <span class="DataValEditAcctNum"><cfinput autocomplete="off" class="tsAppBodyText" disabled="disabled" onkeypress="hideAlert();" id="fld_10B_" name="fld_10B_" maxLength="17" type="text" value="#arguments.strPrefill.fld_10B_#"> *</span></div>
							<cfif StructKeyExists(arguments.strPrefill,"nickname")>
								<div class="BIitem">
									<span class="DataLabelEdit"><b>Optional Account Nickname:&nbsp;</b></span> 
									<span style="display:inline-flex;flex-direction:column;">
										<cfinput type="text" class="tsAppBodyText" id="fld_nickname" name="fld_nickname" maxLength="30" value="#arguments.strPrefill.nickname#" autocomplete="off">
										<div class="help-block">For security, do NOT use the account number in your nickname.</div>
									</span>
								</div>
							</cfif>
						</div>
					</div>
					<div id="err_bdfrm" class="alert" style="display:none;margin:6px 0;"></div>
					<div class="EditButtons">
						<div>
							<span class="DataLabelEdit">&nbsp;</span>
							<span>
								<button class="tsAppBodyButton" type="submit">Continue</button> 
								<cfif arguments.hideCancel is 0><button class="tsAppBodyButton" onclick="cancelIt()" type="button">Cancel</button> </cfif>
							</span>
						</div>
					</div>
				</div>
				</cfform>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="validateABARoutingNumber" access="private" returntype="numeric" output="no">
		<cfargument name="ABARoutingNumber" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.t = arguments.ABARoutingNumber>
		
		<cfquery name="local.qryLookup" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_tr_checkABARoutingNumber(<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.t#">) as isValid
		</cfquery>
		<cfif local.qryLookup.isValid is 0>
			<cfset local.t = 0>
		</cfif>

		<cfreturn local.t>
	</cffunction>

	<cffunction name="insertPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="fld_9_" type="string" required="yes">
		<cfargument name="fld_10_" type="string" required="yes">
		<cfargument name="fld_27_" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
	
		<!--- Ensure acct type is either Personal or Business --->
		<cfif NOT listFindNoCase("Personal,Business",arguments.fld_27_)>
			<cfset arguments.fld_27_ = "Personal">
		</cfif>
		
		<!--- Ensure routing number is valid formed --->
		<cftry>
			<cfset arguments.fld_9_ = validateABARoutingNumber(arguments.fld_9_)>
			<cfif arguments.fld_9_ is 0>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'Unable to Save Bank Account.';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<!--- Ensure account number is less than 17 digits. remove spaces. cannot start with a space. --->
		<cftry>
			<cfset arguments.fld_10_ = rereplace(arguments.fld_10_,"[^A-Za-z0-9]","","ALL")>
			<cfif len(arguments.fld_10_) gt 17>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'Unable to Save Bank Account.';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<cftry>
			<cfsavecontent variable="local.otherFieldsXML">
				<cfoutput>
				<fields>
				<cfloop list="27" index="local.thisEl">
					<fld_#local.thisEl#_>#XMLFormat(arguments["fld_#local.thisEl#_"])#</fld_#local.thisEl#_>
				</cfloop>
				</fields>
				</cfoutput>
			</cfsavecontent>

			<!--- force lookup of activeMemberID in case the member is merged mid-session --->
			<cfquery name="local.qryInsertAcct" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @cofMemberID int, @payProfileID int;
					SELECT @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pmid#">);

					BEGIN TRAN;
						INSERT INTO dbo.ams_memberPaymentProfiles (memberid, profileID, status, detail, nickname, customerProfileID, 
							paymentProfileID, otherFields, addedStatsSessionID, addedByMemberID)
						VALUES (
							@cofMemberID,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.profileID#">,
							'A', 
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="XXXX#right(arguments.fld_10_,4)#">,
							'', 
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_9_#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_10_#">,
							<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.otherFieldsXML#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">,
							<cfif session.cfcuser.memberdata.memberid gt 0>
								<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
							<cfelse>
								@cofMemberID
							</cfif>
						);

						SET @payProfileID = SCOPE_IDENTITY();

						INSERT INTO dbo.tr_bankAccounts (orgID, siteID, MPPPayProfileID, routingNumber, accountNumber, acctType)
						VALUES (
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.orgID#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.siteID#">,
							@payProfileID,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_9_#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_10_#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_27_#">
						);
					COMMIT TRAN;

					SELECT @payProfileID AS payProfileID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
	
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objRet = new Object();
							objRet.a = 'save';
							objRet.mccardevent = 'cardAdded';
							objRet.payprofileid = #val(local.qryInsertAcct.payProfileID)#;
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="addPaymentProfileReturn" access="public" returntype="struct" output="no">
		<cfargument name="siteid" type="numeric" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="routingNumber" type="string" required="yes">
		<cfargument name="accountNumber" type="string" required="yes">
		<cfargument name="showCOF" type="boolean" required="yes">
		<cfargument name="usePopup" type="boolean" required="yes">
		<cfargument name="usePopupDIVName" type="string" required="yes">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="adminForm" type="boolean" required="yes">
		<cfargument name="hideSelect" type="boolean" required="no" default="0">
		<cfargument name="offerDelete" type="boolean" required="no" default="0">
		<cfargument name="overrideCustomerID" type="string" required="no" default="">
		<cfargument name="editMode" type="string" required="no" default="frontEndPayment" hint="frontEndPayment|frontEndManage|controlPanelPayment|controlPanelManage">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { head='' }>
	
		<!--- get the gather form to update the calling page --->
		<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.siteid, profilecode=arguments.qryGateWayID.profileCode)>
		<cfset local.strGatherVars = { siteid=arguments.siteid, profileCode=arguments.qryGateWayID.profileCode, pmid=arguments.pmid, showCOF=arguments.showCOF,
									   hideSelect=arguments.hideSelect, offerDelete=arguments.offerDelete, usePopup=arguments.usePopup, adminForm=arguments.adminForm, 
									   usePopupDIVName=arguments.usePopupDIVName, qryGateWayID=arguments.qryGateWayID, qryGatewayProfileFields=local.qryGatewayProfileFields,
									   overrideCustomerID=arguments.overrideCustomerID, editMode=arguments.editMode } > 
		<cfinvoke method="gather" argumentcollection="#local.strGatherVars#" returnvariable="local.strGather">
	
		<!--- output html etc --->
		<cfif len(local.strGather.inputForm)>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				#local.returnStruct.head#
				<script language="javascript">
					$(function() {
						parent.$('##divInputFormWrapper#arguments.qryGateWayID.profileID#').parent().html('#JSStringFormat(application.objCommon.minText(local.strGather.inputForm))#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfif>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updatePaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="payProfileId" type="numeric" required="yes">
		<cfargument name="routingNumber" type="string" required="yes">
		<cfargument name="accountNumber" type="string" required="yes">
		<cfargument name="fld_9_" type="string" required="yes">
		<cfargument name="fld_10_" type="string" required="yes">
		<cfargument name="fld_27_" type="string" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>
	
		<!--- lookup acct --->
		<cftry>
			<cfquery name="local.qryExtraInfo" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @cofMemberID int;
				select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pmid#">);

				select b.acctType
				from dbo.tr_bankAccounts as b
				inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = b.MPPPayProfileID
					and mpp.payProfileID = <cfqueryparam value="#arguments.payProfileId#" cfsqltype="CF_SQL_INTEGER">
					and mpp.memberID = @cofMemberID
					and mpp.status = 'A'
				where b.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.orgID#">
				and b.routingNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.routingNumber#">
				and b.accountNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.accountNumber#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
						
			<cfif local.qryExtraInfo.recordcount is 0>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'There was an error locating this bank account on file.';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>

		<!--- Ensure acct type is either Personal or Business --->
		<cfif NOT listFindNoCase("Personal,Business",arguments.fld_27_)>
			<cfset arguments.fld_27_ = "Personal">
		</cfif>
		
		<!--- Ensure routing number is valid formed --->
		<cftry>
			<cfset arguments.fld_9_ = validateABARoutingNumber(arguments.fld_9_)>
			<cfif arguments.fld_9_ is 0>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'Unable to Save Bank Account.';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<!--- Ensure account number is less than 17 digits --->
		<cfif NOT FindNoCase("XXXX",arguments.fld_10_)>
			<cftry>
				<cfset arguments.fld_10_ = rereplace(arguments.fld_10_,"[^A-Za-z0-9 ]","","ALL")>
				<cfif len(arguments.fld_10_) gt 17>
					<cfthrow>
				</cfif>
			<cfcatch type="Any">
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'Unable to Save Bank Account.';
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn local.returnStruct>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfset arguments.fld_10_ = arguments.accountNumber>
		</cfif>

		<cftry>
			<cfsavecontent variable="local.otherFieldsXML">
				<cfoutput>
				<fields>
				<cfloop list="27" index="local.thisEl">
					<fld_#local.thisEl#_>#XMLFormat(arguments["fld_#local.thisEl#_"])#</fld_#local.thisEl#_>
				</cfloop>
				</fields>
				</cfoutput>
			</cfsavecontent>

			<cfquery name="local.qryUpdateAcct" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					DECLARE @orgID int, @payProfileID int, @systemMemberID int, @performedByMemberID int
					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.orgID#">;
					SET @payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileId#">;
					SELECT @systemMemberID = dbo.fn_ams_getMCSystemMemberID();
					SET @performedByMemberID = <cfif session.cfcuser.memberdata.memberid gt 0><cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#"><cfelse>@systemMemberID</cfif>;

					BEGIN TRAN;
						UPDATE dbo.tr_bankAccounts
						SET routingNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_9_#">,
							accountNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_10_#">,
							acctType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_27_#">
						WHERE MPPPayProfileID = @payProfileID
						AND orgID = @orgID;

						UPDATE dbo.ams_memberPaymentProfiles
						SET detail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="XXXX#right(arguments.fld_10_,4)#">,
							customerProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_9_#">,
							paymentProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.fld_10_#">,
							otherFields = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.otherFieldsXML#">,
							lastUpdatedDate = GETDATE(),
							lastUpdatedByMemberID = @performedByMemberID,
							failedLastDate = NULL,
							failedSinceDate = NULL,
							nextAllowedAutoChargeDate = NULL,
							failedCount = NULL
							<cfif arguments.keyExists('nickname')>
								, nickname = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.nickname#">
							</cfif>
						WHERE payProfileID = @payProfileID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objRet = new Object();
							objRet.a = 'save';
							objRet.mccardevent = 'cardUpdated';
							objRet.payprofileid = #arguments.payProfileId#;
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="reassignPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="routingNumber" type="string" required="yes">
		<cfargument name="accountNumber" type="string" required="yes">
		<cfargument name="formpost" type="string" required="yes">
		<cfargument name="includeOpenInvoices" type="boolean" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { html='', head='' }>

		<cfquery name="local.qryLookup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @cofMemberID int;
			select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pmid#">);

			select top 1 mpp.payProfileID, mpp.detail, mpp.nickname
			from dbo.tr_bankAccounts as b
			inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = b.MPPPayProfileID
				and mpp.memberID = @cofMemberID
				and mpp.status = 'A'
			where b.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.orgID#">
			and b.routingNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.routingNumber#">
			and b.accountNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.accountNumber#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryLookup.recordcount is not 1>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'Unable to reassociate the items associated with this account.';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfif>
		<cfset local.qryProfilesOnFile = getProfilesOnFile(siteID=arguments.qryGateWayID.siteID, profileID=arguments.qryGateWayID.profileID, memberID=arguments.pmid, showCOF=1, statsSessionID=session.cfcuser.statsSessionID)>

		<cfif not isDefined("arguments.newPayProfileID") or not listfind(valuelist(local.qryProfilesOnFile.payProfileID),arguments.newPayProfileID)>
			<!--- Get associations for this payProfileID --->
			<cfset local.qryAssociatedInvoicesThisCard = application.objPayments.getMemberPayProfileInvoices(memberID=arguments.pmid, includeOpenInvoices=arguments.includeOpenInvoices, limitToPayProfileID=local.qryLookup.payProfileID)>
			<cfset local.qryAssociatedSubscriptionTypesThisCard = application.objPayments.getMemberPayProfileSubscriptions(memberID=arguments.pmid, limitToPayProfileID=local.qryLookup.payProfileID)>
			<cfset local.qryAssociatedContributionsThisCard = application.objPayments.getMemberPayProfileContributions(memberID=arguments.pmid, limitToPayProfileID=local.qryLookup.payProfileID)>

			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					function cancelIt() {
						var objErr = new Object();
							objErr.a = 'cancel';
						if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
					}
					function showLoadingBtn() {
						$('##frmReAssignBDPayProfile button:submit').html('Please wait...').prop('disabled',true);
						return true;
					}
				</script>
				<style type="text/css">
					.reviewmsg { background:##fff6bf; text-align:left; padding:5px 20px; border:2px solid ##fc6; }
					##divInputFormWrapper#arguments.qryGateWayID.profileID# div.cof_meta { font-style:italic; }
					##divInputFormWrapper#arguments.qryGateWayID.profileID# div.cof_invlist { padding-left:30px; padding-top:8px; }
				</style>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.returnStruct.html">
				<cfoutput>
				<div class="tsAppHeading">Choose Another Account for the Items Associated with <cfif len(local.qryLookup.nickname)>#local.qryLookup.nickname# &nbsp;(#local.qryLookup.detail#)<cfelse>#local.qryLookup.detail#</cfif></div>
				<br/>
				<div class="tsAppBodyText" style="margin-bottom:6px;"><strong>Choose another account from the list below to associate:</strong><br/></div>
				<cfform method="post" action="/?pg=buyNow&wizard=#arguments.formpost#" id="frmReAssignBDPayProfile" name="frmReAssignBDPayProfile" onsubmit="return showLoadingBtn();">
					<select name="newPayProfileID" id="newPayProfileID" class="tsAppBodyText">
						<cfloop query="local.qryProfilesOnFile">
							<option value="#local.qryProfilesOnFile.payProfileID#" <cfif local.qryProfilesOnFile.payProfileID eq local.qryLookup.payProfileID>selected</cfif>><cfif len(local.qryProfilesOnFile.nickname)>#local.qryProfilesOnFile.nickname# &nbsp;(#local.qryProfilesOnFile.detail#)<cfelse>#local.qryProfilesOnFile.detail#</cfif></option>
						</cfloop>
					</select>
					<br/><br/>
					<button type="submit" class="tsAppBodyButton">Save</button> 
					<button type="button" class="tsAppBodyButton" onclick="cancelIt();">Cancel</button>
				</cfform>

				<div class="dim50 cof_invlist tsAppBodyText">
					<b>Items to be Reassociated</b>
					<cfif local.qryAssociatedSubscriptionTypesThisCard.recordcount>
						<div style="padding-bottom:10px;">
							<cfloop query="local.qryAssociatedSubscriptionTypesThisCard">
								<div>#local.qryAssociatedSubscriptionTypesThisCard.typename#: #local.qryAssociatedSubscriptionTypesThisCard.subscriptionName#</div>
							</cfloop>
						</div>
					</cfif>
					<cfif local.qryAssociatedContributionsThisCard.recordcount>
						<div style="padding-bottom:10px;">
							<cfloop query="local.qryAssociatedContributionsThisCard">
								<div>#local.qryAssociatedContributionsThisCard.programName#</div>
							</cfloop>
						</div>
					</cfif>
					<cfif local.qryAssociatedInvoicesThisCard.recordcount>
						<div style="padding-bottom:10px;">
						<cfloop query="local.qryAssociatedInvoicesThisCard">
							<cfif local.qryAssociatedInvoicesThisCard.dateDue lt now()>
								<div class="red">Invoice #local.qryAssociatedInvoicesThisCard.invoicenumber# &bull; #local.qryAssociatedInvoicesThisCard.profileName# &bull; #dollarformat(local.qryAssociatedInvoicesThisCard.amountDue)# due on #dateformat(local.qryAssociatedInvoicesThisCard.dateDue,"m/d/yyyy")#</div>
							<cfelse>
								<div>Invoice #local.qryAssociatedInvoicesThisCard.invoicenumber# &bull; #local.qryAssociatedInvoicesThisCard.profileName# &bull; #dollarformat(local.qryAssociatedInvoicesThisCard.amountDue)# due on #dateformat(local.qryAssociatedInvoicesThisCard.dateDue,"m/d/yyyy")#</div>
							</cfif>
						</cfloop>
					</cfif>
				</div>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cftry>
				<cfif arguments.newPayProfileID neq local.qryLookup.payProfileID>
					<cfset application.objPayments.reassignMemberPayProfileApplications(memberID=arguments.pmid, MPProfileID=arguments.qryGateWayID.profileID, payProfileID=local.qryLookup.payProfileID, newPayProfileID=arguments.newPayProfileID)>
				</cfif>	
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objRet = new Object();
								objRet.a = 'save';
								objRet.mccardevent = 'cardReassigned';
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objRet);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				
			<cfcatch type="any">
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'Unable to reassociate the items associated with this account.';
							if (parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn) parent.p_#arguments.qryGateWayID.profileID#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
			</cfcatch>
			</cftry>
		</cfif>
	
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="removePaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="pmid" type="numeric" required="yes">
		<cfargument name="payProfileId" type="numeric" required="yes">
		<cfargument name="overrideRemoveByMemberID" type="numeric" required="no" default="0">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false }>
	
		<cftry>
			<cfquery name="local.qryLookup" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int, @cofMemberID int;
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
				select @cofMemberID = dbo.fn_getActiveMemberID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pmid#">);

				select top 1 mpp.payProfileID, mpp.detail
				from dbo.tr_bankAccounts as b
				inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = b.MPPPayProfileID
					and mpp.payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileId#">
					and mpp.memberID = @cofMemberID
					and mpp.status = 'A'
				where b.orgID = @orgID
				and not exists (select invoiceID from dbo.tr_invoices where payProfileID = mpp.payProfileID and orgID = @orgID)
				and not exists (select ss.subscriberID from dbo.sub_subscribers as ss
								inner join dbo.sub_statuses st on st.statusID = ss.statusID
								where ss.payProfileID = mpp.payProfileID
								and ss.orgID = @orgID
								and st.statusCode in ('A','P','I','O'));

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qryLookup.recordcount is not 1>
				<cfthrow>
			</cfif>
		<cfcatch type="any">
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
	
		<cftry>
			<cfstoredproc procedure="ams_deleteCardOnFile" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileId#">
				<cfif arguments.overrideRemoveByMemberID gt 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.overrideRemoveByMemberID#">
				<cfelseif session.cfcuser.memberdata.memberid gt 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.pmid#">
				</cfif>
			</cfstoredproc>

			<cfset local.returnStruct = { "success":true }>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct = { "success":false }>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="charge" access="package" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="returnStruct" type="struct" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryInfoOnFile" type="query" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="x_testmode" type="numeric" required="no" default="0">
		<cfargument name="adminForm" type="boolean" required="no" default="0">
	
		<cfset var local = structNew()>
	
		<!--- copy args for xml history --->
		<cfset local.strArgsCopy = duplicate(arguments)>
		<cfset StructDelete(local.strArgsCopy,'qryGateWayID')>
		<cfset StructDelete(local.strArgsCopy,'returnStruct')>
		<cfset StructDelete(local.strArgsCopy,'qryGatewayProfileFields')>
		<cfset StructDelete(local.strArgsCopy,'qryInfoOnFile')>
	
		<!--- return back the amount since applepay/googlepay need it anyway --->
		<cfset arguments.returnStruct.x_amount = arguments.x_amount>

		<cfscript>
		local.tmpStr = { 
			x_amount=arguments.x_amount,
			x_description=arguments.x_description,
			x_testmode=arguments.x_testmode,
			fld_9_=arguments.qryInfoOnFile.routingNumber,
			fld_10_=arguments.qryInfoOnFile.accountNumber,
			fld_27_=arguments.qryInfoOnFile.acctType
		};
		
		if (arguments.adminForm AND local.tmpStr.fld_27_ eq "Personal")
			StructInsert(local.tmpStr, "seccode", "PPD");
		else if (NOT arguments.adminForm AND local.tmpStr.fld_27_ eq "Personal")
			StructInsert(local.tmpStr, "seccode", "WEB");
		else
			StructInsert(local.tmpStr, "seccode", "CCD");
			
		
		local.xmlPaymentInfo = '<payment gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
		for (local.fld in local.strArgsCopy) {
			local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
		}
		local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway>';
		for (local.fld in local.tmpStr)
			local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.tmpStr[local.fld])#</#lcase(local.fld)#>";
		local.xmlPaymentInfo = local.xmlPaymentInfo & '</gateway></payment>';
		
		local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.assignedToMemberID, 
			memberPaymentProfileID=arguments.qryInfoOnFile.payProfileID, paymentInfo=local.xmlPaymentInfo, 
			gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='payment');

		// handle response
		local.strResponse = callGateway(authFields=local.tmpStr);
		arguments.returnStruct.rawResponse = local.strResponse.rawResponse;
		arguments.returnStruct.responseCode = local.strResponse.responseCode;
		arguments.returnStruct.responseReasonText = local.strResponse.responseReasonText;
		arguments.returnStruct.publicResponseReasonText = local.strResponse.publicResponseReasonText;
		arguments.returnStruct.responseReasonCode = local.tmpStr.seccode; // sec code here so we can use it if needed in post-processing
		arguments.returnStruct.transactionid = local.strResponse.transactionid;
		arguments.returnStruct.approvalCode = local.strResponse.approvalCode;
		arguments.returnStruct.transactionDetail = "Payment by bank account XXXX#right(local.tmpStr.fld_10_,4)#";
		arguments.returnStruct.status = 'Active';
		arguments.returnStruct.GLAccountID = arguments.qryGateWayID.GLAccountID;

		// record history		
		local.xmlResponseInfo = '<response>';
		for (local.fld in arguments.returnStruct)
			local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.returnStruct[local.fld])#</#lcase(local.fld)#>";
		local.xmlResponseInfo = local.xmlResponseInfo & '</response>';
			
		arguments.returnStruct.historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
			gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.returnStruct.responseReasonCode);

		if (arguments.returnStruct.responseCode is 1)
			consolidateMemberPayProfile(orgID=arguments.qryGateWayID.orgID, siteID=arguments.qryGateWayID.siteID, profileID=arguments.qryGateWayID.profileid, payProfileID=arguments.qryInfoOnFile.payProfileID);
		</cfscript>
	
		<cfreturn arguments.returnStruct>
	</cffunction>
	
	<cffunction name="credit" access="package" returntype="array" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="arrReturnStructs" type="array" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryPaymentHistory" type="query" required="yes">
		<cfargument name="paymentTransactionID" type="numeric" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="x_testmode" type="numeric" required="no" default="0">
	
		<cfset var local = structNew()>

		<!--- grab empty returnstruct for later --->
		<cfset local.returnstruct = duplicate(arguments.arrReturnStructs[1])>

		<!--- copy args for xml history --->
		<cfset local.strArgsCopy = duplicate(arguments)>
		<cfset StructDelete(local.strArgsCopy,'qryGateWayID')>
		<cfset StructDelete(local.strArgsCopy,'arrReturnStructs')>
		<cfset StructDelete(local.strArgsCopy,'qryGatewayProfileFields')>
		<cfset StructDelete(local.strArgsCopy,'qryPaymentHistory')>

		<cfscript>
		// get info about original payment
		local.rawxmlPaymentInfo = XMLParse(arguments.qryPaymentHistory.paymentInfo);
		local.rawxmlGatewayResponse = XMLParse(arguments.qryPaymentHistory.gatewayResponse);
		local.origPaymentAmount = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/x_amount/text())");
		local.origPaymentDescription = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/x_description/text())");
		local.origPaymentRoutingNumber = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/fld_9_/text())");
		local.origPaymentAccountNumber = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/fld_10_/text())");
		local.origPaymentSecCode = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/seccode/text())");
		local.origPaymentAccountType = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/fld_27_/text())");
		local.origPaymentGLAccountID = xmlSearch(local.rawxmlGatewayResponse,"string(/response/glaccountid/text())");
		local.origPaymentMemberPaymentProfileID = val(arguments.qryPaymentHistory.memberPaymentProfileID);
		</cfscript>

		<!--- if this is a partial refund, need to check for a valid payment profile used for the repayment --->
		<cfif local.origPaymentAmount-arguments.x_amount gt 0>
			<cfquery name="local.qryCheckProfile" datasource="#application.dsn.membercentral.dsn#">
				select b.accountID
				from dbo.tr_bankAccounts as b
				inner join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = b.MPPPayProfileID
					and mpp.status = 'A'
				where b.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.qryGateWayID.orgID#">
				and b.routingNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.origPaymentRoutingNumber#">
				and b.accountNumber = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.origPaymentAccountNumber#">
			</cfquery>
		</cfif>

		<!--- if (partial refund and profile exists) OR full refund, try void first --->
		<cfif (local.origPaymentAmount-arguments.x_amount gt 0 AND local.qryCheckProfile.recordcount is 1) OR local.origPaymentAmount-arguments.x_amount lte 0>
			<cfscript>
			local.voidReturnStruct = { 	responseCode=99999, responseReasonText="Invalid request", publicResponseReasonText="Invalid request", responseReasonCode="", 
				rawResponse="", historyID=0, transactionid="", approvalCode="" };
		
			local.voidReturnStruct = void(qryGateWayID=arguments.qryGateWayID, returnStruct=local.voidReturnStruct, 
				qryGatewayProfileFields=arguments.qryGatewayProfileFields, qryPaymentHistory=arguments.qryPaymentHistory,
				x_amount=local.origPaymentAmount, x_description=local.origPaymentDescription);
			arguments.arrReturnStructs[1].rawResponse = local.voidReturnStruct.rawResponse;
			arguments.arrReturnStructs[1].responseCode = local.voidReturnStruct.responseCode;
			arguments.arrReturnStructs[1].responseReasonText = local.voidReturnStruct.responseReasonText;
			arguments.arrReturnStructs[1].publicResponseReasonText = local.voidReturnStruct.publicResponseReasonText;
			arguments.arrReturnStructs[1].responseReasonCode = local.voidReturnStruct.responseReasonCode;
			arguments.arrReturnStructs[1].transactionid = local.voidReturnStruct.transactionid;
			arguments.arrReturnStructs[1].approvalCode = local.voidReturnStruct.approvalCode;
			arguments.arrReturnStructs[1].refundType = "void";
			arguments.arrReturnStructs[1].status = "Active";
			arguments.arrReturnStructs[1].refundAmt = local.origPaymentAmount;
			arguments.arrReturnStructs[1].GLAccountID = local.origPaymentGLAccountID;
			arguments.arrReturnStructs[1].historyID = local.voidReturnStruct.historyID;
			</cfscript>
			
			<!--- if the void was a success, we need to charge the difference between original amount and refund amount --->
			<cfif arguments.arrReturnStructs[1].responseCode is 1 and (local.origPaymentAmount-arguments.x_amount) gt 0>
				<cfscript>
				local.chargeReturnStruct = { responseCode=99999, responseReasonText="Invalid request", publicResponseReasonText="Invalid request", responseReasonCode="", 
					rawResponse="", historyID=0, transactionDetail="", GLAccountID=0, transactionid="", approvalCode="", status="Active" };
		
				local.qryInfoOnFile = queryNew("payProfileID,routingNumber,accountNumber,acctType","integer,varchar,varchar,varchar");
				if (queryAddRow(local.qryInfoOnFile)) {
					QuerySetCell(local.qryInfoOnFile,"payProfileID",local.origPaymentMemberPaymentProfileID);
					QuerySetCell(local.qryInfoOnFile,"routingNumber",local.origPaymentRoutingNumber);
					QuerySetCell(local.qryInfoOnFile,"accountNumber",local.origPaymentAccountNumber);
					QuerySetCell(local.qryInfoOnFile,"acctType",local.origPaymentAccountType);
				}
		
				local.chargeReturnStruct = charge(qryGateWayID=arguments.qryGateWayID, returnStruct=local.chargeReturnStruct, 
									qryGatewayProfileFields=arguments.qryGatewayProfileFields, qryInfoOnFile=local.qryInfoOnFile, adminForm=1, 
									assignedToMemberID=arguments.assignedToMemberID, 
									x_amount=local.origPaymentAmount-arguments.x_amount, x_description=local.origPaymentDescription);
				arguments.arrReturnStructs[2] = duplicate(local.returnstruct);
				arguments.arrReturnStructs[2].rawResponse = local.chargeReturnStruct.rawResponse;
				arguments.arrReturnStructs[2].responseCode = local.chargeReturnStruct.responseCode;
				arguments.arrReturnStructs[2].responseReasonText = local.chargeReturnStruct.responseReasonText;
				arguments.arrReturnStructs[2].publicResponseReasonText = local.chargeReturnStruct.publicResponseReasonText;
				arguments.arrReturnStructs[2].responseReasonCode = local.chargeReturnStruct.responseReasonCode;
				arguments.arrReturnStructs[2].transactionid = local.chargeReturnStruct.transactionid;
				arguments.arrReturnStructs[2].approvalCode = local.chargeReturnStruct.approvalCode;
				arguments.arrReturnStructs[2].transactionDetail = local.chargeReturnStruct.transactionDetail;
				arguments.arrReturnStructs[2].status = local.chargeReturnStruct.status;
				arguments.arrReturnStructs[2].refundType = "payment";
				arguments.arrReturnStructs[2].refundAmt = local.origPaymentAmount-arguments.x_amount;
				arguments.arrReturnStructs[2].GLAccountID = local.chargeReturnStruct.GLAccountID;
				arguments.arrReturnStructs[2].historyID = local.chargeReturnStruct.historyID;
				</cfscript>		
			
			<!--- else if the void was not a success, try to refund --->
			<cfelseif arguments.arrReturnStructs[1].responseCode is not 1>
				<cfscript>
				local.tmpStr = { 
					x_amount=arguments.x_amount,
					x_description=arguments.x_description,
					x_testmode=arguments.x_testmode,
					fld_9_=local.origPaymentRoutingNumber,
					fld_10_=local.origPaymentAccountNumber,
					fld_27_=local.origPaymentAccountType
					};
				
				if (local.tmpStr.fld_27_ eq "Personal")
					StructInsert(local.tmpStr, "seccode", "PPD");
				else
					StructInsert(local.tmpStr, "seccode", "CCD");
					
				local.xmlPaymentInfo = '<refund gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
				for (local.fld in local.strArgsCopy) {
					local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
				}
				local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway>';
				for (local.fld in local.tmpStr)
					local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.tmpStr[local.fld])#</#lcase(local.fld)#>";
				local.xmlPaymentInfo = local.xmlPaymentInfo & '</gateway></refund>';
				
				local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.assignedToMemberID, 
					memberPaymentProfileID=local.origPaymentMemberPaymentProfileID, paymentInfo=local.xmlPaymentInfo,
					gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='refund');

				// handle response
				local.strResponse = callGateway(authFields=local.tmpStr);
				arguments.arrReturnStructs[2] = duplicate(local.returnstruct);
				arguments.arrReturnStructs[2].rawResponse = local.strResponse.rawResponse;
				arguments.arrReturnStructs[2].responseCode = local.strResponse.responseCode;
				arguments.arrReturnStructs[2].responseReasonText = local.strResponse.responseReasonText;
				arguments.arrReturnStructs[2].publicResponseReasonText = local.strResponse.publicResponseReasonText;
				arguments.arrReturnStructs[2].responseReasonCode = local.tmpStr.seccode; // sec code here so we can use it if needed in post-processing
				arguments.arrReturnStructs[2].transactionid = local.strResponse.transactionid;
				arguments.arrReturnStructs[2].approvalCode = local.strResponse.approvalCode;
				arguments.arrReturnStructs[2].transactionDetail = "Refund by bank account XXXX#right(local.tmpStr.fld_10_,4)#";
				arguments.arrReturnStructs[2].status = 'Active';	// Set to active since pending refunds are not supported.
				arguments.arrReturnStructs[2].refundType = "refund";
				arguments.arrReturnStructs[2].refundAmt = local.tmpStr.x_amount;
				arguments.arrReturnStructs[2].GLAccountID = arguments.qryGateWayID.GLAccountID;

				// record history		
				local.xmlResponseInfo = '<response>';
				for (local.fld in arguments.arrReturnStructs[2])
					local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.arrReturnStructs[2][local.fld])#</#lcase(local.fld)#>";
				local.xmlResponseInfo = local.xmlResponseInfo & '</response>';
				
				arguments.arrReturnStructs[2].historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
					gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.arrReturnStructs[2].responseReasonCode);
				</cfscript>

			</cfif>
		
		<cfelse>

			<cfscript>
			local.tmpStr = { 
				x_amount=arguments.x_amount,
				x_description=arguments.x_description,
				x_testmode=arguments.x_testmode,
				fld_9_=local.origPaymentRoutingNumber,
				fld_10_=local.origPaymentAccountNumber,
				fld_27_=local.origPaymentAccountType
				};
			
			if (local.tmpStr.fld_27_ eq "Personal")
				StructInsert(local.tmpStr, "seccode", "PPD");
			else
				StructInsert(local.tmpStr, "seccode", "CCD");
				
			local.xmlPaymentInfo = '<refund gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
			for (local.fld in local.strArgsCopy)
				local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
			local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway>';
			for (local.fld in local.tmpStr)
				local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.tmpStr[local.fld])#</#lcase(local.fld)#>";
			local.xmlPaymentInfo = local.xmlPaymentInfo & '</gateway></refund>';
			
			local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.assignedToMemberID, 
				memberPaymentProfileID=local.origPaymentMemberPaymentProfileID, paymentInfo=local.xmlPaymentInfo,
				gatewayID=arguments.qryGateWayID.gatewayID,	profileID=arguments.qryGateWayID.profileID, paymentType='refund');

			// handle response
			local.strResponse = callGateway(authFields=local.tmpStr);
			arguments.arrReturnStructs[1] = duplicate(local.returnstruct);
			arguments.arrReturnStructs[1].rawResponse = local.strResponse.rawResponse;
			arguments.arrReturnStructs[1].responseCode = local.strResponse.responseCode;
			arguments.arrReturnStructs[1].responseReasonText = local.strResponse.responseReasonText;
			arguments.arrReturnStructs[1].responseReasonCode = local.tmpStr.seccode; // sec code here so we can use it if needed in post-processing
			arguments.arrReturnStructs[1].publicResponseReasonText = local.strResponse.publicResponseReasonText;
			arguments.arrReturnStructs[1].transactionid = local.strResponse.transactionid;
			arguments.arrReturnStructs[1].approvalCode = local.strResponse.approvalCode;
			arguments.arrReturnStructs[1].transactionDetail = "Refund by bank account XXXX#right(local.tmpStr.fld_10_,4)#";
			arguments.arrReturnStructs[1].status = 'Active';	// Set to active since pending refunds are not supported.
			arguments.arrReturnStructs[1].refundType = "refund";
			arguments.arrReturnStructs[1].refundAmt = local.tmpStr.x_amount;
			arguments.arrReturnStructs[1].GLAccountID = arguments.qryGateWayID.GLAccountID;

			// record history	
			local.xmlResponseInfo = '<response>';
			for (local.fld in arguments.arrReturnStructs[1])
				local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.arrReturnStructs[1][local.fld])#</#lcase(local.fld)#>";
			local.xmlResponseInfo = local.xmlResponseInfo & '</response>';
			
			arguments.arrReturnStructs[1].historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
				gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.arrReturnStructs[1].responseReasonCode);
			</cfscript>

		</cfif>

		<cfreturn arguments.arrReturnStructs>
	</cffunction>
	
	<cffunction name="void" access="package" returntype="struct" output="no">
		<cfargument name="qryGateWayID" type="query" required="yes">
		<cfargument name="returnStruct" type="struct" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="qryPaymentHistory" type="query" required="yes">
		<cfargument name="x_amount" type="numeric" required="yes">
		<cfargument name="x_description" type="string" required="yes">
		<cfargument name="x_testmode" type="numeric" required="no" default="0">

		<cfset var local = structNew()>

		<!--- copy args for xml history --->
		<cfset local.strArgsCopy = duplicate(arguments)>
		<cfset StructDelete(local.strArgsCopy,'qryGateWayID')>
		<cfset StructDelete(local.strArgsCopy,'returnStruct')>
		<cfset StructDelete(local.strArgsCopy,'qryGatewayProfileFields')>
		<cfset StructDelete(local.strArgsCopy,'qryPaymentHistory')>

		<cfscript>
		// get info about original payment
		local.rawxmlPaymentInfo = XMLParse(arguments.qryPaymentHistory.paymentInfo);
		local.rawxmlGatewayResponse = XMLParse(arguments.qryPaymentHistory.gatewayResponse);
		local.origPaymentAmount = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/x_amount/text())");
		local.origPaymentDescription = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/x_description/text())");
		local.origPaymentRoutingNumber = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/fld_9_/text())");
		local.origPaymentAccountNumber = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/fld_10_/text())");
		local.origPaymentSecCode = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/seccode/text())");
		local.origPaymentAccountType = xmlSearch(local.rawxmlPaymentInfo,"string(/payment/gateway/fld_27_/text())");
		local.origPaymentGLAccountID = xmlSearch(local.rawxmlGatewayResponse,"string(/response/glaccountid/text())");
		local.origPaymentMemberPaymentProfileID = val(arguments.qryPaymentHistory.memberPaymentProfileID);

		local.tmpStr = { 
			x_amount=arguments.x_amount,
			x_description=arguments.x_description,
			x_testmode=arguments.x_testmode,
			fld_9_=local.origPaymentRoutingNumber,
			fld_10_=local.origPaymentAccountNumber,
			fld_27_=local.origPaymentAccountType,
			seccode=local.origPaymentSecCode
			};
			
		local.xmlPaymentInfo = '<void gatewayid="#arguments.qryGateWayID.gatewayID#" profileid="#arguments.qryGateWayID.profileID#"><args>';
		for (local.fld in local.strArgsCopy)
			local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.strArgsCopy[local.fld])#</#lcase(local.fld)#>";
		local.xmlPaymentInfo = local.xmlPaymentInfo & '</args><gateway>';
		for (local.fld in local.tmpStr)
			local.xmlPaymentInfo = local.xmlPaymentInfo & "<#lcase(local.fld)#>#xmlformat(local.tmpStr[local.fld])#</#lcase(local.fld)#>";
		local.xmlPaymentInfo = local.xmlPaymentInfo & '</gateway></void>';
		
		local.returnHistory = application.objPayments.insertPaymentHistory(payerMemberID=arguments.qryPaymentHistory.assignedToMemberID, 
			memberPaymentProfileID=local.origPaymentMemberPaymentProfileID, paymentInfo=local.xmlPaymentInfo,
			gatewayID=arguments.qryGateWayID.gatewayID, profileID=arguments.qryGateWayID.profileID, paymentType='void');
			
		// if batch this payment is on is Open, then void is successful because we assume the file has not been uploaded to their bank yet.
		// If batch is not open, void is a failure.
		if (arguments.qryPaymentHistory.batchStatus eq "Open") {
			local.strResponse = callGateway(authFields=local.tmpStr);
			arguments.returnStruct.rawResponse = local.strResponse.rawResponse;
			arguments.returnStruct.responseCode = local.strResponse.responseCode;
			arguments.returnStruct.responseReasonText = local.strResponse.responseReasonText;
			arguments.returnStruct.publicResponseReasonText = local.strResponse.publicResponseReasonText;
			arguments.returnStruct.responseReasonCode = local.tmpStr.seccode; // sec code here so we can use it if needed in post-processing
			arguments.returnStruct.transactionid = local.strResponse.transactionid;
			arguments.returnStruct.approvalCode = local.strResponse.approvalCode;
		} else {		
			arguments.returnStruct.rawResponse = 'ACH void denied';
			arguments.returnStruct.responseCode = 3;
			arguments.returnStruct.responseReasonText = 'Batch containing payment is not open so this payment cannot be voided.';
			arguments.returnStruct.publicResponseReasonText = "Refund Failed";
			arguments.returnStruct.responseReasonCode = local.tmpStr.seccode; // sec code here so we can use it if needed in post-processing
			arguments.returnStruct.transactionid = 0;
			arguments.returnStruct.approvalCode = 0;
		}

		// record history
		local.xmlResponseInfo = '<response>';
		for (local.fld in arguments.returnStruct)
			local.xmlResponseInfo = local.xmlResponseInfo & "<#lcase(local.fld)#>#xmlformat(arguments.returnStruct[local.fld])#</#lcase(local.fld)#>";
		local.xmlResponseInfo = local.xmlResponseInfo & '</response>';
		
		arguments.returnStruct.historyID = application.objPayments.updatePaymentHistory(historyID=local.returnHistory.historyID, 
			gatewayResponse=local.xmlResponseInfo, responseReasonCode=arguments.returnStruct.responseReasonCode);
		</cfscript>
		
		<cfreturn arguments.returnStruct>
	</cffunction>
	
	<cffunction name="history" access="package" returntype="struct" output="no">
		<cfargument name="qryHistory" type="query" required="yes">
		<cfargument name="qryGatewayProfileFields" type="query" required="yes">
		<cfargument name="maskSensitive" type="boolean" required="yes">
		<cfargument name="typeID" type="numeric" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStr = { strHistory=structNew(), htmlHistory='' }>

		<cfsavecontent variable="local.returnStr.htmlHistory">
			<cfoutput>
			<tr class="tsAppBodyText" valign="top">
				<td><b>Routing Number:</b> &nbsp;</td>
				<td>#XMLSearch(arguments.qryHistory.paymentInfo,"string(//gateway/fld_9_/text())")#</td>
			</tr>
			<cfset StructInsert(local.returnStr.strHistory,"Routing Number",XMLSearch(arguments.qryHistory.paymentInfo,"string(//gateway/fld_9_/text())"),true)>

			<tr class="tsAppBodyText" valign="top">
				<td><b>Account Number:</b> &nbsp;</td>
				<td>XXXX#right(XMLSearch(arguments.qryHistory.paymentInfo,"string(//gateway/fld_10_/text())"),4)#</td>
			</tr>
			<cfset StructInsert(local.returnStr.strHistory,"Account Number","XXXX"&right(XMLSearch(arguments.qryHistory.paymentInfo,"string(//gateway/fld_10_/text())"),4),true)>

			<tr class="tsAppBodyText" valign="top">
				<td><b>Type of Account:</b> &nbsp;</td>
				<td>#XMLSearch(arguments.qryHistory.paymentInfo,"string(//gateway/fld_27_/text())")#</td>
			</tr>
			<cfset StructInsert(local.returnStr.strHistory,"Type of Account",XMLSearch(arguments.qryHistory.paymentInfo,"string(//gateway/fld_27_/text())"),true)>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnStr>
	</cffunction>
	
	<cffunction name="callGateway" access="private" returntype="struct" output="no">
		<cfargument name="authFields" type="struct" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.response = structNew()>

		<!--- 
		0: no testing. real deal.   
		1: no testing. real deal.   
		2: testing using success fake structure return. 
		3 (or anything else): testing using failure fake structure return. 
		--->
		<cfparam name="arguments.authFields.x_testmode" default="0">
		
		<cfif listFind("0,1",arguments.authFields.x_testmode)>

			<cfset local.response.rawResponse = 'ACH payment approved'>
			<cfset local.response.responseCode = 1>
			<cfset local.response.approvalCode = 1>
			<cfset local.response.responseReasonText = ''>
			<cfset local.response.publicResponseReasonText = ''>
			<cfset local.response.responseReasonCode = 1>
			<cfset local.response.avsCode = 1>
			<cfset local.response.transactionID = '123'>

		<cfelse>
			<cfif arguments.authFields.x_testmode is 2>
				<cfset local.response.rawResponse = 'TestMode Raw Response'>
				<cfset local.response.approvalCode = 1>
				<cfset local.response.responseCode = 1>
				<cfset local.response.responseReasonCode = 1>
				<cfset local.response.responseReasonText = ''>
				<cfset local.response.publicResponseReasonText = ''>
				<cfset local.response.avsCode = 1>
				<cfset local.response.transactionID = '123'>
			<cfelse>
				<cfset local.response.rawResponse = 'TestMode Raw Response'>
				<cfset local.response.approvalCode = 0>
				<cfset local.response.responseCode = 3>
				<cfset local.response.responseReasonCode = 3>
				<cfset local.response.responseReasonText = 'Testing failure request.'>
				<cfset local.response.publicResponseReasonText = "Invalid Request">
				<cfset local.response.avsCode = 0>
				<cfset local.response.transactionID = 0>
			</cfif>
		</cfif>
	
		<cfreturn local.response>
	</cffunction>

	<cffunction name="consolidateMemberPayProfile" access="private" returntype="void" output="no">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="payProfileID" type="numeric" required="yes">
	
		<cfset var local = StructNew()>

		<cfset local.recordedByMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.orgID)>
		<cfif NOT local.recordedByMemberID>
			<cfset local.recordedByMemberID = application.objCommon.getMCSystemMemberID()>
		</cfif>

		<cftry>
			<cfstoredproc procedure="ams_consolidateMemberPayProfiles" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.payProfileID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.recordedByMemberID#">
				<cfprocresult name="local.qryConsolidatedProfiles">
			</cfstoredproc>

			<cfif local.qryConsolidatedProfiles.recordcount>
				<cfquery name="local.gatewaySettings" datasource="#application.dsn.membercentral.dsn#">
					SELECT pr.siteID, pr.profileID, pr.profileCode, ga.gatewayType, pr.gatewayUsername, pr.gatewayPassword, pr.gatewayMerchantID
					FROM dbo.mp_profiles as pr
					INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
					WHERE pr.profileID = <cfqueryparam value="#local.qryConsolidatedProfiles.profileID#" cfsqltype="cf_sql_integer">
					AND pr.[status] = 'A'
				</cfquery>

				<cfset local.threadID = createUUID()>
				<cfthread action="run" name="MPPConsolidation-#local.threadID#" threadid="#local.threadID#" consolidateProfiles="#local.qryConsolidatedProfiles#" gatewaySettings="#local.gatewaySettings#" orgID="#arguments.orgID#">
					<cfloop query="attributes.consolidateProfiles">
						<cftry>
							<cfset thread.tokenArgs = { mcproxy_orgID=attributes.orgID, pmid=attributes.consolidateProfiles.memberID, payProfileID=attributes.consolidateProfiles.payProfileID } >
							<cfset thread.removeResult = removePaymentProfile(argumentcollection=thread.tokenArgs)>
						<cfcatch type="any">
							<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
						</cfcatch>			
						</cftry>
					</cfloop>
				</cfthread>
			</cfif>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="getHostNameWithProtocol" access="private" output="false" returntype="string">
		<cfset var local = structNew()>

		<cfif application.objPlatform.isRequestSecure()>
			<cfset local.thisServerProtocol = "https://">
		<cfelse>
			<cfset local.thisServerProtocol = "http://">
		</cfif>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.thisServerProtocol & application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname>
		<cfelse>
			<cfset local.thisHostname = local.thisServerProtocol & application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfreturn local.thisHostname>
	</cffunction>

	<cffunction name="getUserPermissions" access="public" output="false" returntype="struct">
		<cfargument name="editMode" type="string" required="true">
	
		<cfset var local = structNew()>
	
		<cfswitch expression="#arguments.editmode#">
			<cfcase value="frontEndPayment">
				<cfset local.showFailureInfo = 0>
				<cfset local.showAssociatedItems = 0>
				<cfset local.showAddedBy = 0>
				<cfset local.showlastUpdater = 0>
				<cfset local.showPayOverdueNow = 0>
				<cfset local.includeOpenInvoices = 0>
				<cfset local.allowReassociate = 0>
			</cfcase>	
			<cfcase value="frontEndManage">
				<cfset local.showFailureInfo = 1>
				<cfset local.showAssociatedItems = 1>
				<cfset local.showAddedBy = 0>
				<cfset local.showlastUpdater = 0>
				<cfset local.showPayOverdueNow = 0>
				<cfset local.includeOpenInvoices = 0>
				<cfset local.allowReassociate = 1>
			</cfcase>	
			<cfcase value="controlPanelPayment">
				<cfset local.showFailureInfo = 1>
				<cfset local.showAssociatedItems = 0>
				<cfset local.showAddedBy = 1>
				<cfset local.showlastUpdater = 1>
				<cfset local.showPayOverdueNow = 0>
				<cfset local.includeOpenInvoices = 1>
				<cfset local.allowReassociate = 0>
			</cfcase>	
			<cfcase value="controlPanelManage">
				<cfset local.showFailureInfo = 1>
				<cfset local.showAssociatedItems = 1>
				<cfset local.showAddedBy = 1>
				<cfset local.showlastUpdater = 1>
				<cfset local.showPayOverdueNow = 1>
				<cfset local.includeOpenInvoices = 1>
				<cfset local.allowReassociate = 1>
			</cfcase>
		</cfswitch>
	
		<cfreturn local>
	</cffunction>

	<cffunction name="getCommonStyles" access="public" output="false" returntype="string">
		<cfset var commonStyles = "">
		
		<cfsavecontent variable="commonStyles">
			<cfoutput>
			<style type="text/css">
				.mcp-bd-card-box { position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal;
					-ms-flex-direction: column;flex-direction: column; min-width: 0; word-wrap: break-word; background-color: ##fff; background-clip: border-box; 
					border: 0 solid rgba(122, 123, 151, 0.3); border-radius: 0.65em; box-shadow: 0 .46875em 2.1875em rgba(0,0,0,.03),0 .9375em 1.40625em rgba(0,0,0,.03),0 .25em .53125em rgba(0,0,0,.05),0 .125em .1875em rgba(0,0,0,.03); }
				.mcp-bd-card-header { padding:.75em;margin-bottom:0; }
				.mcp-bd-card-body {padding:1.25em;}
				.mcp-bd-text-danger {color:##f83245!important}
				.mcp-bd-text-center {text-align:center !important;}
				.mcp-bd-text-dim {color:##808080!important}
				.mcp-bd-d-none{display:none !important;}
				.mcp-bd-d-flex {display:flex !important;}
				.mcp-bd-col {flex-basis:0;flex-grow:1;max-width:100%;padding-right:5px;padding-left:5px;}
				.mcp-bd-col-auto {flex:0 0 auto;width:auto;max-width:100%;padding-right:5px;padding-left:5px;}
				.mcp-bd-p-0 {padding:0!important;}
				.mcp-bd-pt-0 {padding-top:0!important;}
				.mcp-bd-pr-0 {padding-right:0!important;}
				.mcp-bd-pr-2 {padding-right:.5em!important;}
				.mcp-bd-p-1 {padding:.25em!important;}
				.mcp-bd-p-2 {padding:.5em!important;}
				.mcp-bd-p-2 {padding:.5em!important;}
				.mcp-bd-p-3 {padding:1em!important;}
				.mcp-bd-pt-3 {padding-top:1em!important;}
				.mcp-bd-pl-0 {padding-left:0!important;}
				.mcp-bd-pl-1 {padding-left:.25em!important;}
				.mcp-bd-pl-2 {padding-left:.5em!important;}
				.mcp-bd-pl-3 {padding-left:1em!important;}
				.mcp-bd-pl-5 {padding-left:2em!important;}
				.mcp-bd-pb-0 {padding-bottom:0!important;}
				.mcp-bd-pb-1 {padding-bottom:.25em!important;}
				.mcp-bd-pb-2 {padding-bottom:.5em!important;}
				.mcp-bd-m-0 {margin:0!important;}
				.mcp-bd-m-1 {margin:.25em!important;}
				.mcp-bd-m-2 {margin:.5em!important;}
				.mcp-bd-mt-0 {margin-top:0!important;}
				.mcp-bd-mt-1 {margin-top:.25em!important;}
				.mcp-bd-mt-2 {margin-top:.5em!important;}
				.mcp-bd-mt-3 {margin-top:1em!important;}
				.mcp-bd-mt-4 {margin-top:1.5em!important;}
				.mcp-bd-mt-5 {margin-top:2em!important;}
				.mcp-bd-mb-0 {margin-bottom:0!important;}
				.mcp-bd-mb-1 {margin-bottom:.25em!important;}
				.mcp-bd-mb-2 {margin-bottom:.5em!important;}
				.mcp-bd-mb-3 {margin-bottom:1em!important;}
				.mcp-bd-mb-4 {margin-bottom:1.5em!important;}
				.mcp-bd-mb-5 {margin-bottom:2em!important;}
				.mcp-bd-ml-1 {margin-left:.25em!important;}
				.mcp-bd-ml-2 {margin-left:.5em!important;}
				.mcp-bd-mr-1 {margin-right:.25em!important;}
				.mcp-bd-mr-2 {margin-right:.5em!important;}
				.mcp-bd-mr-3 {margin-right:1em!important;}
				.mcp-bd-mr-4 {margin-right:1.5em!important;}
				.mcp-bd-mr-5 {margin-right:2em!important;}
				.mcp-bd-align-items-center {align-items:center;}
				.mcp-bd-align-self-center{align-self:center !important;}
				.mcp-bd-flex-column {flex-direction: column !important;}
				.mcp-bd-font-weight-bold {font-weight:bold!important;}
				.mcp-bd-font-italic {font-style: italic !important;}
				.mcp-bd-ml-auto {margin-left:auto !important;}
				.mcp-bd-mr-auto {margin-right:auto !important;}
				.mcp-bd-mx-auto {margin-left:auto !important;margin-right:auto !important;}
				.mcp-bd-font-size-xs { font-size:.79em!important; }
				.mcp-bd-font-size-sm { font-size:.85em!important; }
				.mcp-bd-font-size-md {font-size:.95em!important;}
				.mcp-bd-font-size-lg {font-size:1.1875em!important;}
				.mcp-bd-font-size-xl {font-size:1.425em!important;}
				.mcp-bd-line-height-1 {line-height:1!important;}
				.mcp-bd-opacity-2 {opacity: 0.2 !important;}
				.mcp-bd-card-selected { border-color: rgb(60, 115, 205) !important;border-width: 2px !important;background-color: rgba(18, 101, 241, 0.07); }
				.mcp-bd-bg-secondary {background-color: ##f8f9ff !important;}
				.mcp-bd-btn {border-color:##4f4f4f;border-width:1px;background-color:transparent;border-radius:.2em;padding:.3em 1.1em;}
				.mcp-bd-btn:hover {background-color:##f0f0f0;}
				.mcp-bd-cof_fail { margin-top:4px; color:##F00;background:url(/assets/common/images/exclamation.png) no-repeat left center; padding-left:20px;}
				.mcp-bd-procfee-radio {margin:3px 5px 0 0 !important;}
				.mcp-bd-shadow-none {-webkit-box-shadow: none !important;box-shadow: none !important;}
				.mcp-bd-border-1 {border-width:1px;border-style:solid;}
				.mcp-bd-border-2 {border-width:2px !important;border-style:solid;}
				.mcp-bd-border-bottom {border-bottom: 1px solid ##ccc !important;}
				.mcp-bd-border-gray {border-color:gray!important;}
				.mcp-bd-border-danger {border-color:##f83245 !important;}
				.mcp-bd-alert-warning { color:##824224;background-color:##fde4d5;border-color:##fcd9c4; }
				.mcp-bd-dropdown {display: inline-block;position:relative;outline:none;}
				.mcp-bd-dropbtn {cursor: pointer;transition: 0.35s ease-out;}
				.mcp-bd-dropdown .mcp-bd-dropdown-content {position: absolute;top:10%;right:50%;background-color: ##f7f7f7;min-width:190px;box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
					z-index: 100000;visibility: hidden;opacity: 0;transition: 0.35s ease-out;}
				.mcp-bd-dropdown-content a {color: black;padding: 12px 16px;display: block;text-decoration: none;transition: 0.35s ease-out;}
				.mcp-bd-dropdown-content a:hover {background-color: ##eaeaea;}
				.mcp-bd-dropdown:focus .mcp-bd-dropdown-content { outline: none;transform: translateY(20px);visibility: visible;opacity: 1;}
				.mcp-bd-dropbtn:hover, .mcp-bd-dropdown:focus .mcp-bd-dropbtn {transform: translateY(-2px);}
				.mcp-bd-dropdown .mcp-bd-dropbtnmask {position: absolute;top:0;right:0;bottom:0;left:0;opacity:0;cursor:pointer;z-index:10;display:none;}
				.mcp-bd-dropdown:focus .mcp-bd-dropbtnmask {display: inline-block;}
				.mcp-bd-dropdown .mcp-bd-dropbtnmask:focus .mcp-bd-dropdown-content {outline: none;visibility: hidden;opacity: 0;}
				a.mcp-bd-dropbtn {text-decoration:none !important;}
				.mcp-bd-dropbtn i {width:30px;height:30px;text-align:center;line-height:30px;}
				.mcp-bd-dropbtn i:hover {background-color:##eee;border-radius: 50%;}
				.mcp-bd-dropdown i {float:none !important;}
				/*mobile styles*/
				@media (max-width: 576px) {
					.mcp-bd-w-sm-100 {width:100% !important;}
				}
			</style>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn commonStyles>
	</cffunction>

	<cffunction name="generateSharedProfileMessage" access="public" returntype="string" output="false">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.retString = "">

		<cfquery name="local.qryOrgBankDraftMPProfiles" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT mp.profileName
			FROM dbo.mp_profiles AS mp
			INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
				AND g.isActive = 1
				AND g.tokenStore = 'BankDraft'
			INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
				AND s.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			WHERE mp.status = 'A'
			ORDER BY mp.profileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryOrgBankDraftMPProfiles.recordCount GT 1>
			<cfsavecontent variable="local.retString">
				<cfoutput>
				<div class="alert d-flex align-items-center pl-2 align-content-center alert-info font-size-sm mb-2" role="alert">
					<span class="font-size-lg d-block d-40 mr-2 text-center">
						<i class="fa-solid fa-circle-info"></i>
					</span>
					<span>
						Unlike credit cards, which are tied to one specific payment profile, a member's bank account is stored in a central vault for your organization 
						and can be used by any bank draft payment profile defined in your organization, including: 
						#replace(valuelist(local.qryOrgBankDraftMPProfiles.profileName,chr(7)),chr(7),", ","ALL")#.
					</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.retString>
	</cffunction>

	<cffunction name="allowedAccountTypes" access="public" output="false" returntype="struct">
		<cfargument name="profileID" type="numeric" required="yes">
		<cfargument name="adminForm" type="boolean" required="no" default="0">

		<cfset var local = structNew()>
		<cfset local.strAllowedTypes = { "personal": false, "business": false }>

		<!--- get supported SEC codes --->
		<cfquery name="local.qrySupportedSECCodes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT sc.secCode
			FROM dbo.mp_profileSecCodes AS psc
			INNER JOIN dbo.mp_secCodes AS sc ON sc.secCodeID = psc.secCodeID
			WHERE psc.profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.profileID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.lstSupportedSECCodes = valueList(local.qrySupportedSECCodes.secCode)>
		<cfset local.isSEC_CCD = listFindNoCase(local.lstSupportedSECCodes,"CCD")>
		<cfset local.isSEC_PPD = listFindNoCase(local.lstSupportedSECCodes,"PPD")>
		<cfset local.isSEC_WEB = listFindNoCase(local.lstSupportedSECCodes,"WEB")>

		<cfif local.isSEC_CCD>
			<cfset local.strAllowedTypes.business = true>
		</cfif>
		<cfif arguments.adminForm AND local.isSEC_PPD>
			<cfset local.strAllowedTypes.personal = true>
		</cfif>
		<cfif NOT arguments.adminForm AND local.isSEC_WEB>
			<cfset local.strAllowedTypes.personal = true>
		</cfif>

		<cfreturn local.strAllowedTypes>
	</cffunction>

</cfcomponent>