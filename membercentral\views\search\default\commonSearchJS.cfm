<cfsavecontent variable="local.searchAppJS">
	<cfoutput>
	<style type="text/css">
		<!--- common styles --->
		##bk_content .bk-text-light {color:##fff !important;}
		##bk_content .bk-text-danger {color:##f83245!important}
		##bk_content .bk-text-dim {color:##808080!important}
		##bk_content .bk-text-center {text-align:center !important;}
		##bk_content .bk-text-right {text-align:right !important;}
		##bk_content .bk-w-100 {width:100% !important;}
		##bk_content .bk-h-100 {height:100% !important;}
		##bk_content .bk-d-flex {display:flex !important;}
		##bk_content .bk-flex-wrap {flex-wrap:wrap !important;}
		##bk_content .bk-align-self-center{align-self:center !important;}
		##bk_content .bk-col {flex-basis:0;flex-grow:1;max-width:100%;padding-right:5px;padding-left:5px;}
		##bk_content .bk-pt-0 {padding-top:0!important;}
		##bk_content .bk-pt-1 {padding-top:0.5rem!important;}
		##bk_content .bk-pb-2 {padding-bottom:.5em!important;}
		##bk_content .bk-pr-0 {padding-right:0!important;}
		##bk_content .bk-p-2 {padding:.5em!important;}
		##bk_content .bk-p-3 {padding:1em!important;}
		##bk_content .bk-pl-3 {padding-left:1em!important;}
		##bk_content .bk-pr-3 {padding-right:1em!important;}
		##bk_content .bk-pl-5 {padding-left:2em!important;}
		##bk_content .bk-m-0 {margin:0!important;}
		##bk_content .bk-mt-0 {margin-top:0!important;}
		##bk_content .bk-mt-2 {margin-top:.5em!important;}
		##bk_content .bk-mt-3 {margin-top:1em!important;}
		##bk_content .bk-mt-4 {margin-top:1.5em!important;}
		##bk_content .bk-mt-5 {margin-top:2em!important;}
		##bk_content .bk-mb-0 {margin-bottom:0!important;}
		##bk_content .bk-mb-1 {margin-bottom:.25em!important;}
		##bk_content .bk-mb-2 {margin-bottom:.5em!important;}
		##bk_content .bk-mb-3 {margin-bottom:1em!important;}
		##bk_content .bk-mb-4 {margin-bottom:1.5em!important;}
		##bk_content .bk-mb-5 {margin-bottom:2em!important;}
		##bk_content .bk-ml-2 {margin-left:.5em!important;}
		##bk_content .bk-mt-auto {margin-top:auto!important;}
		##bk_content .bk-mr-1 {margin-right:.25em!important;}
		##bk_content .bk-mr-2 {margin-right:.5em!important;}
		##bk_content .bk-mr-3 {margin-right:1em!important;}
		##bk_content .bk-mr-4 {margin-right:1.5em!important;}
		##bk_content .bk-mr-5 {margin-right:2em!important;}
		##bk_content .bk-font-size-sm { font-size:.85em; }
		##bk_content .bk-font-size-md {font-size:.95em}
		##bk_content .bk-font-weight-bold {font-weight:bold;}
		##bk_content .bk-ml-auto {margin-left:auto !important;}
		##bk_content .bk-mr-auto {margin-right:auto !important;}
		##bk_content .bk-mx-auto {margin-left:auto !important;margin-right:auto !important;}
		##bk_content .bk-border-gray {border-color:gray;}
		##bk_content .bk-align-top {vertical-align:top;}
		##bk_content .bk-flex-align-center {align-items:center !important;}
		##bk_content .bk-border-1 {border-width:1px!important;border-style:solid!important;}
		##bk_content .bk-border-2 {border-width:2px !important;border-style:solid!important;}
		##bk_content .bk-border-bottom {border-bottom: 1px solid ##eeeff8 !important;}
		##bk_content .bk-price-card-selected { border-color: rgb(60, 115, 205) !important;border-width: 2px !important; }
		##bk_content .bk-card-box { position: relative; display: -webkit-box; display: -ms-flexbox; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal;
			-ms-flex-direction: column;flex-direction: column; min-width: 0; word-wrap: break-word; background-color: ##fff; background-clip: border-box; 
			border: 0 solid rgba(122, 123, 151, 0.3); border-radius: 0.65em; box-shadow: 0 .46875em 2.1875em rgba(0,0,0,.03),0 .9375em 1.40625em rgba(0,0,0,.03),0 .25em .53125em rgba(0,0,0,.05),0 .125em .1875em rgba(0,0,0,.03); }
		##bk_content .bk-card-header { padding:.75em;margin-bottom:0; }
		##bk_content .bk-card-body {padding:1.25em;}
		##bk_content .bk-card-selected { border-color:rgb(214, 214, 214) !important;border-width:3px !important;}
		##bk_content .bk-bg-secondary {background-color: ##f8f9ff !important;}
		##bk_content .bk-shadow-none {-webkit-box-shadow: none !important;box-shadow: none !important;}
		##bk_content .bk-price-badge {position:absolute;top:-12px;right:10px;background-color:##0073f7;border: 1px solid ##e0e0e0;border-radius: 999px;padding:4px 14px;font-size:10px !important;
			font-weight:500;color:white;box-shadow:0 2px 6px rgba(0, 0, 0, 0.1);font-size:10px;}

		##bk_content .bk-summary-circle { background-color:##4472c4; height:90px; width:90px; border-radius:50%; display:flex; align-items:center; justify-content:center; font-size:20px; text-align:center; line-height:24px; text-transform:uppercase; }
		##bk_content .bk-bg-chartblue { fill:##4472c4; }
		##bk_content .state{ fill: none; stroke: ##a9a9a9; stroke-width: 1;	}
		##bk_content .state:hover{ fill-opacity:0.5; }
		##bk_content ##MCSearchSummaryContainer {position:relative!important;}
		##bk_content ##bk_tooltip { position: absolute; text-align: center; padding: 20px; margin: 10px; font: 12px sans-serif; background-color: ##ffffff; border-radius: 2px; pointer-events: none;
							border: 1px solid ##ccc; border: 1px solid rgba(0, 0, 0, 0.2); border-radius:6px; width:auto; padding:4px; opacity:0; }
		##bk_content .bk-similar-search-circle {width:280px;}
		##bk_content .bk-list-search-circle {width:320px;}
		##bk_content .bk-alert-error { background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; }
		##bk_content .bk-alert-success { background:##f1ffed url(/assets/common/images/tick.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border:2px solid ##00bf0f; }
	</style>
	<script type="text/javascript">
		function gotoBKContainer(bkContainerID) {
			let bkContainer = $('##'+bkContainerID);
			if(bkContainer.length && bkContainer.is(':visible')) {
				$('html, body').animate({
					scrollTop: bkContainer.offset().top - 300
				}, 750);
			}
		}
		function bk_slideToggle(id,p,el) {
			$('##'+id).slideToggle(500);
			gotoBKContainer(id);
			$(el).find('.mc-arrow-down').toggle();
			$(el).find('.mc-arrow-up').toggle();
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#ReReplace(local.searchAppJS,'\s{2,}',' ','ALL')#">