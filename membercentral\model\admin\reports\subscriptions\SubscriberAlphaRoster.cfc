<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);
		
		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>		
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfset local.strSubsWidgetData = {	title='Define Subscription Filter',
				description='Filter the subscriptions appearing on this report using the defined criteria below.',
				gridext="#this.siteResourceID#_1", gridwidth=660, gridheight=150, gridClassList='mb-5 stepDIV', initGridOnLoad=true,
				controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID, 
				filterMode=1, excludeSteps='', showIcons=1 }>
			<!--- hide icons if unable to change report --->
			<cfif NOT hasReportEditRights(event=arguments.event)>
				<cfset local.strSubsWidgetData.showIcons = 0>
			</cfif>
			<cfset local.strSubscriptionWidget = createObject("component","model.admin.common.modules.subscriptionWidget.subscriptionWidget").renderWidget(strWidgetData=local.strSubsWidgetData)>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				#local.strSubscriptionWidget.js#
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFrequencies">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select frequencyID, frequencyName
				from dbo.sub_frequencies
				where siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
				and status = 'A'
				order by frequencyName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			</cfquery>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.fFreq = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/ffreq/text())")>
					<cfset local.frmView = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmview/text())")>
					<cfif NOT len(local.frmView)>
						<cfset local.frmView = 1>
					</cfif>

					<cfform name="frmReport" id="frmReport" method="post">
						<cfinput type="hidden" name="reportAction" id="reportAction" value="">
						#local.strSubscriptionWidget.html#
						#showStepMemberCriteria(event=arguments.event, title="Define Optional Member Filter", desc="Optionally filter the members appearing on this report using the defined criteria below.")#
						
						<div class="mb-5 stepDIV">
							<h5>Define Extra Options</h5>
							<div class="row mt-2">
								<div class="col-sm-12">
									<div class="form-group row">
										<label for="fFreq" class="col-md-4 col-sm-12 col-form-label">Frequency</label>
										<div class="col-md-8 col-sm-12">
											<select name="fFreq" id="fFreq" class="form-control form-control-sm">
												<option value="0">All</option>
												<cfloop query="local.qryFrequencies">
												<option value="#local.qryFrequencies.frequencyID#" <cfif local.fFreq eq local.qryFrequencies.frequencyID>selected</cfif>>#local.qryFrequencies.frequencyName#</option>
												</cfloop>
											</select>	
										</div>
									</div>
									<div class="form-group row">
										<label for="frmView" class="col-md-4 col-sm-12 col-form-label">Report View</label>
										<div class="col-md-8 col-sm-12">
											<cfselect name="frmView" id="frmView" class="form-control form-control-sm">
												<option value="1" <cfif local.frmView eq 1>selected</cfif>>Group by Member</option>
												<option value="2" <cfif local.frmView eq 2>selected</cfif>>Group by Member and Subscription Tree</option>
												<option value="3" <cfif local.frmView eq 3>selected</cfif>>Group by Subscription</option>
											</cfselect>
										</div>
									</div>
								</div>
							</div>
						</div>

						#showStepFieldsets(event=arguments.event)#
						#showButtonBar(event=arguments.event)#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(local.mc_siteInfo.orgcode) & "/memberphotosth/">

			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfset local.fFreq = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/ffreq/text())")>
			<cfset local.frmView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmview/text())")>
			<cfset local.frmShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@img)")>
			<cfset local.frmShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmShowCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					<cfif len(local.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>
					
					IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
						DROP TABLE ##tmpSubscribers;
					IF OBJECT_ID('tempdb..##tmpSubscribersInv') IS NOT NULL
						DROP TABLE ##tmpSubscribersInv;
					IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
						DROP TABLE ##mcSubscribersForAcct;
					IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
						DROP TABLE ##mcSubscriberTransactions;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;
					CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

					CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
					CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
						invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
						amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
						assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
						creditGLAccountID int, INDEX IDX_mcSubscriberTransactions_subscriberID_transactionID (subscriberID, transactionID));					
					
					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">, @outputFieldsXML xml;

					-- subscriber data
					select s.subscriberID, m.memberid, m.lastname, m.firstname, m.membernumber, m.company, stypes.typeName,
						sub.subscriptionName, subrate.rateName, s.subStartDate, s.subEndDate, 
						substatus.statusCode as SubscriptionStatusCode, substatus.statusName as SubscriptionStatus, 
						subpaystatus.statusName as PaymentStatus, s.subscriberPath,
						freq.frequencyID, freq.frequencyName, freq.frequencyShortName,
						s.PCFree, s.modifiedRate, s.glAccountID, s.lastPrice, s.rootSubscriberID, 
						cast(0 as decimal(18,2)) as amtBilled, cast(0 as decimal(18,2)) as amtDue, cast(0 as decimal(18,2)) as amtPaid
					into ##tmpSubscribers
					from dbo.sub_subscribers as s
					inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
					inner join dbo.sub_types stypes on stypes.typeID = sub.typeID
					inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID
					inner join dbo.sub_paymentstatuses as subpaystatus on subpaystatus.statusID = s.paymentStatusID
					inner join dbo.sub_rateFrequencies subrf on subrf.RFID = s.RFID
					inner join dbo.sub_frequencies freq on freq.frequencyID = subrf.frequencyID
						<cfif val(local.fFreq)> and freq.frequencyID = <cfqueryparam value="#local.fFreq#" cfsqltype="CF_SQL_INTEGER"></cfif>
					inner join dbo.sub_rates subrate on subrate.rateID = subrf.rateID
					inner join dbo.ams_members as m2 on m2.memberid = s.memberID and m2.orgID = @orgID
					inner join dbo.ams_members as m on m.memberid = m2.activeMemberID and m.isProtected = 0
					<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					where m.orgID = @orgID
					and m.status <> 'D';

					CREATE NONCLUSTERED INDEX IX_memberid ON ##tmpSubscribers (memberID);
					CREATE NONCLUSTERED INDEX IX_subscriberid ON ##tmpSubscribers (subscriberID);

					-- get existing invoices and populate mcSubscriberTransactions
					INSERT INTO ##mcSubscribersForAcct (subscriberID)
					SELECT distinct subscriberID
					FROM ##tmpSubscribers;

					EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select distinct st.invoiceID, st.subscriberID
					INTO ##tmpSubscribersInv
					from ##mcSubscriberTransactions as st;

					-- billed (R and O) have no transactions - amtBilled and AmtDue are the same
					-- other with no invoices - amtBilled is here but amtDue is 0
					update tmp
					set tmp.amtBilled = 
							case 
							when tmp.PCFree = 1 then 0
							when tmp.modifiedRate is not null then tmp.modifiedRate + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(tmp.glAccountID,tmp.modifiedRate,getdate(),ma.stateID)),0)
							else tmp.lastPrice + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(tmp.glAccountID,tmp.lastPrice,getdate(),ma.stateID)),0)
							end
					from ##tmpSubscribers as tmp
					left outer join dbo.ams_memberAddresses as ma
						inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
							AND matag.memberID = ma.memberID 
							and matag.addressTypeID = ma.addressTypeID
						inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
							and matagt.addressTagTypeID = matag.addressTagTypeID
							and matagt.addressTagType = 'Billing'
						on ma.orgID = @orgID
						and ma.memberID = tmp.memberID
					where tmp.SubscriptionStatusCode in ('R','O')
					OR NOT EXISTS (select 1 from ##tmpSubscribersInv where subscriberID = tmp.subscriberID);

					update ##tmpSubscribers
					set amtDue = amtBilled
					where SubscriptionStatusCode in ('R','O');

					-- other when there are invoices for the subs
					update tmp
					set tmp.amtBilled = innertmp.amtBilled,
						tmp.amtDue = innertmp.amtDue,
						tmp.amtPaid = innertmp.amtPaid
					from ##tmpSubscribers as tmp
					inner join (
						select tmp.subscriberID, 
							sum(tsFull.cache_amountAfterAdjustment) as amtBilled, 
							sum(tsFull.cache_amountAfterAdjustment)-sum(tsFull.cache_activePaymentAllocatedAmount) as amtDue,
							sum(tsFull.cache_activePaymentAllocatedAmount) as amtPaid
						from ##tmpSubscribers as tmp
						inner join ##mcSubscriberTransactions as st on st.subscriberID = tmp.subscriberID
						cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,st.transactionID) as tsFull
						where tmp.SubscriptionStatusCode not in ('R','O')
						AND EXISTS (select 1 from ##tmpSubscribersInv where subscriberID = tmp.subscriberID)
						group by tmp.subscriberID
					) as innertmp on innertmp.subscriberID = tmp.subscriberID;
										
					-- get members fieldset data and set back to snapshot because proc ends in read committed
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
						@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
						@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
						@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
						@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
						@membersTableName='##tmpSubscribers', @membersResultTableName='##tmpMembersFS', 
						@linkedMembers=0, @mode='report', @outputFieldsXML=@outputFieldsXML OUTPUT;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT *, CASE WHEN rowNum = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
					FROM (
						SELECT tm.subscriberID, tm.lastname, tm.firstname, tm.membernumber, tm.company, 
							tm.typeName, tm.subscriptionName, tm.rateName, tm.subStartDate, tm.subEndDate, tm.rootSubscriberID, 
							tm.SubscriptionStatusCode, tm.SubscriptionStatus, tm.PaymentStatus, tm.subscriberPath, tm.frequencyID, tm.frequencyName, 
							tm.frequencyShortName, isnull(tm.amtBilled,0) as amtBilled, isnull(tm.amtDue,0) as amtDue, isnull(tm.amtPaid,0) as amtPaid, m.*, 
							ROW_NUMBER() OVER (
								<cfif local.frmView eq 2>
									ORDER BY tm.lastName, tm.firstName, tm.memberNumber, tm.rootSubscriberID, tm.subscriberPath
								<cfelseif local.frmView eq 3>
									ORDER BY tm.typeName, tm.subscriptionName, tm.lastName, tm.firstName, tm.memberNumber, tm.rootSubscriberID, tm.subscriberPath
								<cfelse>
									ORDER BY tm.lastName, tm.firstName, tm.memberNumber, tm.typeName, tm.subscriptionName, tm.subStartDate
								</cfif>) AS rowNum
						FROM ##tmpSubscribers as tm
						INNER JOIN ##tmpMembersFS AS m ON tm.memberID = m.memberID
					) AS finalData
					ORDER BY rowNum;
					
					<cfif len(local.strSQLPrep.ruleSQL)>
						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
					</cfif>
					IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
						DROP TABLE ##tmpSubscribers;
					IF OBJECT_ID('tempdb..##tmpSubscribersInv') IS NOT NULL
						DROP TABLE ##tmpSubscribersInv;
					IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
						DROP TABLE ##mcSubscribersForAcct;
					IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
						DROP TABLE ##mcSubscriberTransactions;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>	

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfif local.qryData.recordcount>
				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.qryData.mc_outputFieldsXML)>
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					select *
					from [local].qryOutputFields
					where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
				</cfquery>
			</cfif>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					<cfif arguments.reportAction eq "screen">
						<style type="text/css">
							##screenreport div.ind { margin-left:15px; }
							##screenreport div.indtree1 { margin-left:0px; }
							##screenreport div.indtree2 { margin-left:15px; }
							##screenreport div.indtree3, ##screenreport div.indtree4, ##screenreport div.indtree5 { margin-left:30px; }
						</style>
					</cfif>
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>

				<cfif local.qryData.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfquery name="local.qrySubCount" dbtype="query">
						select distinct subscriberID 
						from [local].qryData
					</cfquery>
					<cfquery name="local.qryMemberCount" dbtype="query">
						select distinct memberID
						from [local].qryData
					</cfquery>

					<cfoutput>
						<div>#local.qrySubCount.recordCount# subscriptions for #local.qryMemberCount.recordCount# members</div><br/>
					</cfoutput>

					<cfoutput><table class="table table-sm table-borderless"></cfoutput>
					<cfif listFind("1,2",local.frmView)>
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left" <cfif local.frmShowPhotos is 1>colspan="2"</cfif>>Member </th>
							<th class="text-left">Term</th>
							<th class="text-right">Billed</th>
							<th class="text-right">Paid</th>
							<th class="text-right">Due</th>
						</tr>
						</thead>
						<tbody>
						</cfoutput>
						<cfoutput query="local.qryData" group="memberid">
							<tr>
								<cfif local.frmShowPhotos is 1>
									<td class="align-top" style="width:80px;">
										<cfif local.qryData.hasMemberPhotoThumb is 1>
											<cfif arguments.reportAction eq "screen">
												<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.qryData.MemberNumber)#.jpg">
											<cfelse>
												<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.qryData.MemberNumber)#.jpg">
											</cfif>
										<cfelse>
											<cfif arguments.reportAction eq "screen">
												<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
											<cfelse>
												<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
											</cfif>
										</cfif>
									</td>
								</cfif>
								<td colspan="5" class="align-top <cfif local.frmShowPhotos is 1>pl-2</cfif>">
									<cfif arguments.reportAction eq "screen">
										<a href="#local.memberLink#&memberid=#local.qryData.memberid#" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.qryData["Extended MemberNumber"]#<cfelse>#local.qryData["Extended Name"]#</cfif></a><br/>
									<cfelse>
										<b><cfif local.frmShowMemberNumber is 1>#local.qryData["Extended MemberNumber"]#<cfelse>#local.qryData["Extended Name"]#</cfif></b><br/>
									</cfif>
									<cfif local.frmShowCompany is 1 AND len(local.qryData.company)>#local.qryData.company#<br/></cfif>
									<cfloop query="local.qryOutputFields">
										<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
											<cfset local.AddrToShow = local.qryData[local.qryOutputFields.dbfield][local.qryData.currentrow]>
											<cfloop condition="Find(', , ',local.AddrToShow)">
												<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
											</cfloop>
											<cfif len(local.AddrToShow)>
												#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
											</cfif>
										</cfif>
									</cfloop>
									<cfloop query="local.qryOutputFieldsForLoop">
										<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
											#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow])#<br/>
										<cfelseif len(local.qryData[local.qryOutputFieldsForLoop.fieldLabel][local.qryData.currentrow])>
											<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
												#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow], "m/d/yyyy")#<br/>
											<cfelse>
												#local.qryOutputFieldsForLoop.fieldlabel#: #local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow]#<br/>
											</cfif>
										</cfif>
									</cfloop>
								</td>
							</tr>

							<cfif local.frmView eq 2>
								<cfoutput group="rootSubscriberID">
									<cfset local.totalAmtBilled = 0>
									<cfset local.totalAmtPaid = 0>
									<cfset local.totalAmtDue = 0>
									<cfoutput>
										<tr>
											<cfif local.frmShowPhotos is 1>
												<td></td>
											</cfif>
											<td class="align-top pl-4">
												#local.qryData.subscriptionName# / #local.qryData.rateName# (#local.qryData.frequencyName# - #local.qryData.SubscriptionStatus#)
											</td>
											<td class="align-top" nowrap>#dateFormat(local.qryData.subStartDate,'m/d/yy')# - #dateFormat(local.qryData.subEndDate,'m/d/yy')#</td>
											<td class="align-top text-right">#DollarFormat(local.qryData.amtBilled)#</td>
											<td class="align-top text-right">#DollarFormat(local.qryData.amtPaid)#</td>
											<td class="align-top text-right">#DollarFormat(local.qryData.amtDue)#</td>
										</tr>
										<cfset local.totalAmtBilled = local.totalAmtBilled + local.qryData.amtBilled>
										<cfset local.totalAmtPaid = local.totalAmtPaid + local.qryData.amtPaid>
										<cfset local.totalAmtDue = local.totalAmtDue + local.qryData.amtDue>
									</cfoutput>
									<tr>
										<td <cfif local.frmShowPhotos is 1>colspan="2"</cfif>></td>
										<td class="align-top font-weight-bold">Total</td>
										<td class="align-top text-right font-weight-bold">#DollarFormat(local.totalAmtBilled)#</td>
										<td class="align-top text-right font-weight-bold">#DollarFormat(local.totalAmtPaid)#</td>
										<td class="align-top text-right font-weight-bold">#DollarFormat(local.totalAmtDue)#</td>
									</tr>
									<tr><td colspan="4">&nbsp;</td></tr>
								</cfoutput>
							<cfelse>
								<cfset local.prevSubType = "">
								<cfoutput>
									<cfif local.prevSubType neq local.qryData.typeName>
										<cfset local.prevSubType = local.qryData.typeName>
										<tr>
											<cfif local.frmShowPhotos is 1>
												<td></td>
											</cfif>
											<td class="align-top" colspan="5">
												#local.qryData.typeName#
											</td>
										</tr>
									</cfif>
									<tr>
										<cfif local.frmShowPhotos is 1>
											<td></td>
										</cfif>
										<td class="align-top pl-4">
											#local.qryData.subscriptionName# / #local.qryData.rateName# (#local.qryData.frequencyName# - #local.qryData.SubscriptionStatus#)
										</td>
										<td class="align-top" nowrap>#dateFormat(local.qryData.subStartDate,'m/d/yy')# - #dateFormat(local.qryData.subEndDate,'m/d/yy')#</td>
										<td class="align-top text-right">#DollarFormat(local.qryData.amtBilled)#</td>
										<td class="align-top text-right">#DollarFormat(local.qryData.amtPaid)#</td>
										<td class="align-top text-right">#DollarFormat(local.qryData.amtDue)#</td>
									</tr>
								</cfoutput>
								<tr><td colspan="4">&nbsp;</td></tr>
							</cfif>
						</cfoutput>
						<cfoutput>
						</tbody>
						</cfoutput>
					<cfelseif local.frmView eq 3>
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left" <cfif local.frmShowPhotos is 1>colspan="2"</cfif>>Subscription / Member </th>
							<th class="text-left">Term</th>
							<th class="text-right">Billed</th>
							<th class="text-right">Paid</th>
							<th class="text-right">Due</th>
						</tr>
						</thead>
						<tbody>
						</cfoutput>
						<cfif local.frmShowPhotos is 1>
							<cfset local.colspan = 5>
						<cfelse>
							<cfset local.colspan = 4>
						</cfif>

						<cfoutput query="local.qryData" group="typeName">
							<tr>
								<td colspan="#local.colspan#"><b>#local.qryData.typeName#</b></td>
							</tr>
							<cfoutput group="subscriptionName">
								<tr>
									<td colspan="#local.colspan#" class="pl-3 font-weight-bold">#local.qryData.subscriptionName#</td>
								</tr>
								<cfset local.totalAmtBilled = 0>
								<cfset local.totalAmtPaid = 0>
								<cfset local.totalAmtDue = 0>
								<cfoutput group="memberid">
									<tr>
										<cfif local.frmShowPhotos is 1>
											<td class="align-top pl-3" style="width:80px;">
												<cfif local.qryData.hasMemberPhotoThumb is 1>
													<cfif arguments.reportAction eq "screen">
														<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.qryData.MemberNumber)#.jpg">
													<cfelse>
														<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.qryData.MemberNumber)#.jpg">
													</cfif>
												<cfelse>
													<cfif arguments.reportAction eq "screen">
														<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
													<cfelse>
														<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
													</cfif>
												</cfif>
											</td>
										</cfif>
										<td class="align-top pl-3" <cfif local.frmShowPhotos is 1>colspan="#local.colspan - 1#"<cfelse>colspan="#local.colspan#"</cfif>>
											<cfif arguments.reportAction eq "screen">
												<a href="#local.memberLink#&memberid=#local.qryData.memberid#" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.qryData["Extended MemberNumber"]#<cfelse>#local.qryData["Extended Name"]#</cfif></a><br/>
											<cfelse>
												<b><cfif local.frmShowMemberNumber is 1>#local.qryData["Extended MemberNumber"]#<cfelse>#local.qryData["Extended Name"]#</cfif></b><br/>
											</cfif>
											<cfif local.frmShowCompany is 1 AND len(local.qryData.company)>#local.qryData.company#<br/></cfif>
											<cfloop query="local.qryOutputFields">
												<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
													<cfset local.AddrToShow = local.qryData[local.qryOutputFields.dbfield][local.qryData.currentrow]>
													<cfloop condition="Find(', , ',local.AddrToShow)">
														<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
													</cfloop>
													<cfif len(local.AddrToShow)>
														#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
													</cfif>
												</cfif>
											</cfloop>
											<cfloop query="local.qryOutputFieldsForLoop">
												<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
													#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow])#<br/>
												<cfelseif len(local.qryData[local.qryOutputFieldsForLoop.fieldLabel][local.qryData.currentrow])>
													<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
														#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow], "m/d/yyyy")#<br/>
													<cfelse>
														#local.qryOutputFieldsForLoop.fieldlabel#: #local.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryData.currentrow]#<br/>
													</cfif>
												</cfif>
											</cfloop>
										</td>
									</tr>
									<cfoutput>
										<tr>
											<cfif local.frmShowPhotos is 1>
												<td></td>
											</cfif>
											<td class="pl-5">
												#local.qryData.rateName# (#local.qryData.frequencyName# - #local.qryData.SubscriptionStatus#)
											</td>
											<td nowrap>#dateFormat(local.qryData.subStartDate,'m/d/yy')# - #dateFormat(local.qryData.subEndDate,'m/d/yy')#</td>
											<td class="text-right">#DollarFormat(local.qryData.amtBilled)#</td>
											<td class="text-right">#DollarFormat(local.qryData.amtPaid)#</td>
											<td class="text-right">#DollarFormat(local.qryData.amtDue)#</td>
										</tr>
										<cfset local.totalAmtBilled = local.totalAmtBilled + local.qryData.amtBilled>
										<cfset local.totalAmtPaid = local.totalAmtPaid + local.qryData.amtPaid>
										<cfset local.totalAmtDue = local.totalAmtDue + local.qryData.amtDue>
									</cfoutput>
								</cfoutput>
								<tr>
									<td class="align-top font-weight-bold pl-3" colspan="<cfif local.frmShowPhotos is 1>3<cfelse>2</cfif>">Subscription Total</td>
									<td class="align-top text-right font-weight-bold">#DollarFormat(local.totalAmtBilled)#</td>
									<td class="align-top text-right font-weight-bold">#DollarFormat(local.totalAmtPaid)#</td>
									<td class="align-top text-right font-weight-bold">#DollarFormat(local.totalAmtDue)#</td>
								</tr>
								<tr><td colspan="#local.colspan#">&nbsp;</td></tr>
							</cfoutput>
						</cfoutput>
						<cfoutput>
						</tbody>
						</cfoutput>
					</cfif>
					<cfoutput></table></cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.qryData", strQryResult=local.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>
			
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>		
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.tempTableName = "rpt#getTickCount()#">
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>
			
			<cfset local.fFreq = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/ffreq/text())")>
			<cfset local.frmView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmview/text())")>

			<cfif local.frmView eq 2>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubCount">
					set nocount on;

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">;
					
					<cfif len(local.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>
					
					select subRowCount = case when max(subrow) is null then 0 else max(subrow) end
					from (
						select row_number() over (partition by m.memberID, s.rootSubscriberID ORDER BY s.rootSubscriberID) as subRow
						from dbo.sub_subscribers as s
						inner join dbo.ams_members as m2 on m2.memberid = s.memberID and m2.orgID = @orgID
						inner join dbo.ams_members as m on m.memberid = m2.activeMemberID
						<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where m.orgID = @orgID
						and m.isProtected = 0
						and m.status <> 'D'
					) tmp;

					<cfif len(local.strSQLPrep.ruleSQL)>
						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
					</cfif>
				</cfquery>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					<cfif len(local.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>
					
					IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
						DROP TABLE ##tmpSubscribers;
					IF OBJECT_ID('tempdb..##tmpSubscribersInv') IS NOT NULL
						DROP TABLE ##tmpSubscribersInv;
					IF OBJECT_ID('tempdb..##tmpSubscribers_subGrandTotal') IS NOT NULL
						DROP TABLE ##tmpSubscribers_subGrandTotal;
					IF OBJECT_ID('tempdb..##tmpInnerSubscribers') IS NOT NULL
						DROP TABLE ##tmpInnerSubscribers;
					IF OBJECT_ID('tempdb..##tmpSubscribersFinal') IS NOT NULL
						DROP TABLE ##tmpSubscribersFinal;
					IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
						DROP TABLE ##mcSubscribersForAcct;
					IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
						DROP TABLE ##mcSubscriberTransactions;
					IF OBJECT_ID('tempdb..##tmpSubsMembership') IS NOT NULL
						DROP TABLE ##tmpSubsMembership;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;

					CREATE TABLE ##tmpSubscribersInv (invoiceID int, subscriberID int);
					CREATE TABLE ##mcSubscribersForAcct (subscriberID int);
					CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
						invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
						amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
						assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
						creditGLAccountID int);
					CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">, 
						@outputFieldsXML xml;

					-- subscriber data
					select s.subscriberID, m.memberid, m.lastname as [Last Name], m.firstname as [First Name], m.membernumber as MemberNumber, m.company as Company, 
						stypes.typeName, sub.subscriptionID, sub.subscriptionName as Subscription, subrate.rateName as Rate, s.subStartDate, 
						dateadd(ms,-(datepart(ms,s.subenddate)),s.subenddate) as subEndDate, 
						dateadd(ms,-(datepart(ms,s.graceEndDate)),s.graceEndDate) as graceEndDate,
						substatus.statusCode as SubscriptionStatusCode, substatus.statusName as SubscriptionStatus, 
						subpaystatus.statusName as PaymentStatus, s.subscriberPath, s.RFID, 
						freq.frequencyID, freq.frequencyName as Frequency, freq.frequencyShortName,
						s.PCFree, s.modifiedRate, s.glAccountID, s.lastPrice, s.rootSubscriberID, 
						cast(0 as decimal(18,2)) as amtBilled, cast(0 as decimal(18,2)) as amtDue, cast(0 as decimal(18,2)) as amtPaid
					into ##tmpSubscribers
					from dbo.sub_subscribers as s
					inner join dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
					inner join dbo.sub_types stypes on stypes.typeID = sub.typeID
					inner join dbo.sub_statuses as substatus on substatus.statusID = s.statusID
					inner join dbo.sub_paymentstatuses as subpaystatus on subpaystatus.statusID = s.paymentStatusID
					inner join dbo.sub_rateFrequencies subrf on subrf.RFID = s.RFID
					inner join dbo.sub_frequencies freq on freq.frequencyID = subrf.frequencyID
						<cfif val(local.fFreq)> and freq.frequencyID = <cfqueryparam value="#local.fFreq#" cfsqltype="CF_SQL_INTEGER"></cfif>
					inner join dbo.sub_rates subrate on subrate.rateID = subrf.rateID
					inner join dbo.ams_members as m2 on m2.memberid = s.memberID and m2.orgID = @orgID
					inner join dbo.ams_members as m on m.memberid = m2.activeMemberID and m.isProtected = 0
					<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					where m.orgID = @orgID
					and m.status <> 'D';

					CREATE NONCLUSTERED INDEX IX_memberid ON ##tmpSubscribers (memberID);
					CREATE NONCLUSTERED INDEX IX_subscriberid ON ##tmpSubscribers (subscriberID);

					-- get existing invoices and populate mcSubscriberTransactions
					INSERT INTO ##mcSubscribersForAcct (subscriberID)
					SELECT distinct subscriberID
					FROM ##tmpSubscribers;

					EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					INSERT INTO ##tmpSubscribersInv (invoiceID, subscriberID)
					select distinct st.invoiceID, st.subscriberID
					from ##mcSubscriberTransactions as st;

					-- billed (R and O) have no transactions - amtBilled and AmtDue are the same
					-- other with no invoices - amtBilled is here but amtDue is 0
					update tmp
					set tmp.amtBilled = 
							case 
							when tmp.PCFree = 1 then 0
							when tmp.modifiedRate is not null then tmp.modifiedRate + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(tmp.glAccountID,tmp.modifiedRate,getdate(),ma.stateID)),0)
							else tmp.lastPrice + isnull((select sum(taxAmount) from dbo.fn_tr_getTaxForUncommittedSale(tmp.glAccountID,tmp.lastPrice,getdate(),ma.stateID)),0)
							end
					from ##tmpSubscribers as tmp
					left outer join dbo.ams_memberAddresses as ma
						inner join dbo.ams_memberAddressTags as matag on matag.orgID = @orgID 
							AND matag.memberID = ma.memberID 
							and matag.addressTypeID = ma.addressTypeID
						inner join dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID
							and matagt.addressTagTypeID = matag.addressTagTypeID
							and matagt.addressTagType = 'Billing'
						on ma.orgID = @orgID
						and ma.memberID = tmp.memberID
					where tmp.SubscriptionStatusCode in ('R','O')
					OR NOT EXISTS (select 1 from ##tmpSubscribersInv where subscriberID = tmp.subscriberID);

					update ##tmpSubscribers
					set amtDue = amtBilled
					where SubscriptionStatusCode in ('R','O');

					-- other when there are invoices for the subs
					update tmp
					set tmp.amtBilled = innertmp.amtBilled,
						tmp.amtDue = innertmp.amtDue,
						tmp.amtPaid = innertmp.amtPaid
					from ##tmpSubscribers as tmp
					inner join (
						select tmp.subscriberID, 
							sum(tsFull.cache_amountAfterAdjustment) as amtBilled, 
							sum(tsFull.cache_amountAfterAdjustment)-sum(tsFull.cache_activePaymentAllocatedAmount) as amtDue,
							sum(tsFull.cache_activePaymentAllocatedAmount) as amtPaid
						from ##tmpSubscribers as tmp
						inner join ##mcSubscriberTransactions as st on st.subscriberID = tmp.subscriberID
						cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,st.transactionID) as tsFull
						where tmp.SubscriptionStatusCode not in ('R','O')
						AND EXISTS (select 1 from ##tmpSubscribersInv where subscriberID = tmp.subscriberID)
						group by tmp.subscriberID
					) as innertmp on innertmp.subscriberID = tmp.subscriberID;

					<cfif local.frmView eq 2>
						-- calc grand totals
						select rootSubscriberID, sum(amtBilled) as grandTotalBilled, sum(amtDue) as grandTotalDue, sum(amtPaid) as grandTotalPaid
						into ##tmpSubscribers_subGrandTotal
						from ##tmpSubscribers
						group by rootSubscriberID;

						select memberID, [First Name], [Last Name], MemberNumber, Company, subscriberID, rootSubscriberID, 
							'Subscription' + cast(row_number() over (partition by memberID, rootSubscriberID ORDER BY rootSubscriberID) as varchar(10)) as subRow
						into ##tmpInnerSubscribers
						from ##tmpSubscribers;

						select memberID, [First Name], [Last Name], MemberNumber, Company, rootSubscriberID
							<cfloop from="1" to="#local.qrySubCount.subRowCount#" index="local.loopCount">
								, [Subscription#local.loopCount#]
							</cfloop>
						into ##tmpSubsMembership
						from ##tmpInnerSubscribers
						pivot(min(subscriberID) for subRow in (
							<cfif val(local.qrySubCount.subRowCount)>
								<cfloop from="1" to="#local.qrySubCount.subRowCount#" index="local.loopCount">
									[Subscription#local.loopCount#] <cfif local.loopCount lt local.qrySubCount.subRowCount>, </cfif>
								</cfloop>
							<cfelse>
								[0]
							</cfif>
						)) pvt;
						
						-- get members fieldset data and set back to snapshot because proc ends in read committed
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
							@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
							@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
							@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
							@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
							@membersTableName='##tmpSubsMembership', @membersResultTableName='##tmpMembersFS', 
							@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						-- preparing final table
						select tmp.[Last Name], tmp.[First Name], tmp.MemberNumber, tmp.[Company],
							<cfloop from="1" to="#local.qrySubCount.subRowCount#" index="local.loopCount">
								[sub#local.loopCount#].subscription as [Subscription#local.loopCount#_Subscription],
								[sub#local.loopCount#].subStartDate as [Subscription#local.loopCount#_StartDate],
								[sub#local.loopCount#].subEndDate as [Subscription#local.loopCount#_EndDate],
								[sub#local.loopCount#].graceEndDate as [Subscription#local.loopCount#_GraceEndDate],
								[sub#local.loopCount#].SubscriptionStatus as [Subscription#local.loopCount#_SubscriptionStatus],
								[sub#local.loopCount#].PaymentStatus as [Subscription#local.loopCount#_PaymentStatus],
								[sub#local.loopCount#].Frequency as [Subscription#local.loopCount#_Frequency],
								[sub#local.loopCount#].amtBilled as [Subscription#local.loopCount#_BilledAmount],
								[sub#local.loopCount#].amtDue as [Subscription#local.loopCount#_DueAmount],
								[sub#local.loopCount#].amtPaid as [Subscription#local.loopCount#_PaidAmount],
							</cfloop>
							sgt.grandTotalBilled as [Total Billed],
							sgt.grandTotalDue as [Total Due],
							sgt.grandTotalPaid as [Total Paid], m.*
						into ##tmpSubscribersFinal
						from ##tmpSubsMembership as tmp
						inner join ##tmpMembersFS as m on tmp.memberID = m.memberID
						inner join ##tmpSubscribers_subGrandTotal as sgt on sgt.rootSubscriberID = tmp.rootSubscriberID
						<cfloop from="1" to="#local.qrySubCount.subRowCount#" index="local.loopCount">
							left outer join ##tmpSubscribers as [sub#local.loopCount#] on [sub#local.loopCount#].subscriberID = [Subscription#local.loopCount#]
						</cfloop>;
					<cfelse>
						-- get members fieldset data and set back to snapshot because proc ends in read committed
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
							@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
							@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
							@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
							@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
							@membersTableName='##tmpSubscribers', @membersResultTableName='##tmpMembersFS', 
							@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
						
						-- final results with member data						
						select tm.[Last Name], tm.[First Name], tm.MemberNumber, tm.[Company],
							tm.typeName as [Subscription Type], tm.Subscription, tm.Rate, 
							tm.subStartDate as [Start Date], tm.subEndDate as [End Date], tm.graceEndDate as [Grace Date],
							tm.SubscriptionStatus as [Subscription Status], tm.PaymentStatus as [Payment Status],
							tm.Frequency, isnull(tm.amtBilled,0) as [Amt Billed], isnull(tm.amtDue,0) as [Amt Due],
							isnull(tm.amtPaid,0) as [Amt Paid], m.*
						into ##tmpSubscribersFinal
						from ##tmpSubscribers as tm
						inner join ##tmpMembersFS AS m on tm.memberID = m.memberID;
					</cfif>

					#generateFinalBCPTable(tblName="##tmpSubscribersFinal", dropFields="memberID")#
					
					<cfif len(local.strSQLPrep.ruleSQL)>
						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
					</cfif>
					IF OBJECT_ID('tempdb..##tmpSubscribers') IS NOT NULL
						DROP TABLE ##tmpSubscribers;
					IF OBJECT_ID('tempdb..##tmpSubscribersInv') IS NOT NULL
						DROP TABLE ##tmpSubscribersInv;
					IF OBJECT_ID('tempdb..##tmpSubscribers_subGrandTotal') IS NOT NULL
						DROP TABLE ##tmpSubscribers_subGrandTotal;
					IF OBJECT_ID('tempdb..##tmpInnerSubscribers') IS NOT NULL
						DROP TABLE ##tmpInnerSubscribers;
					IF OBJECT_ID('tempdb..##tmpSubscribersFinal') IS NOT NULL
						DROP TABLE ##tmpSubscribersFinal;
					IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
						DROP TABLE ##mcSubscribersForAcct;
					IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
						DROP TABLE ##mcSubscriberTransactions;
					IF OBJECT_ID('tempdb..##tmpSubsMembership') IS NOT NULL
						DROP TABLE ##tmpSubsMembership;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfscript>
			local.arrInitialReportSort = arrayNew(1);
			if (local.frmView eq 2) {
				local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			} else if (local.frmView eq 3) {
				local.strTemp = { field='Subscription Type', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='Subscription', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			} else {
				local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='Subscription Type', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='Subscription', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='Start Date', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			}
			local.strReportQry = { qryReportFields=local.qryData, strQryResult=local.qryDataResult };
			local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML);
			</cfscript>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		
		local.strFields = structNew();
		local.strFields.ffreq = { label="Frequency", value=arguments.event.getValue('fFreq','') };
		local.strFields.frmview = { label="Report View", value=arguments.event.getValue('frmView','') };
		
		reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
		return returnAppStruct('','echo');
		</cfscript>
	</cffunction>

</cfcomponent>