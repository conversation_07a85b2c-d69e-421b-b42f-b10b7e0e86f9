<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = "controller" />
	<cfset variables.applicationReservedURLParams = "sa,itemID,cat,storeid,rateid,memberID,formatid,Quantity,regaction,start,ordernumber" />

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.strData = structNew();			
			arguments.event.paramValue('sa','');
			local.objCart = CreateObject('component', 'model.store.shoppingCart');
			local.objStore = CreateObject('component', 'model.admin.store.store');
			local.objLocator = CreateObject("component","model.system.user.accountLocater");
			local.strData.storeInfo = getStoreInfo(appInstanceID=this.appInstanceID);
			local.strData.storeSearchBucketID = createObject("component","model.search.search").getBucketIDByType(local.strData.storeInfo.siteID,"store");
			local.strData.storeSearchBucketLink = '/?pg=search&bid=#local.strData.storeSearchBucketID#';

			
			// setup order number if not defined
			local.objCart.paramOrderNumber(this.appInstanceID);
			local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={});
			
			if (arguments.event.valueExists("shippingOption"))
			{
				local.store[this.appInstanceID].shippingOption = arguments.event.getValue("shippingOption");
				application.mcCacheManager.sessionSetValue(keyname='store', value=local.store);
			}
			arguments.event.paramValue('itemID',0);
			arguments.event.paramValue('mainurl','');
			arguments.event.paramValue('mainregurl','');
			arguments.event.paramValue('storeID',0);
			
			if ((application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)){
				arguments.event.setValue('viewDirectory', 'responsive');
				local.viewDirectory = "responsive";
			}
			else {
				arguments.event.setValue('viewDirectory', 'default');
				local.viewDirectory = "default";
			}
		</cfscript>

		<cfset arguments.event.setValue('mainurl','/?pg=store')>
		<cfset arguments.event.setValue('mainregurl','/?pg=store&itemID=#arguments.event.getValue('itemID')#&sa=regStoreUser')>
		<cfset arguments.event.setValue('locatorurl','/?event=cms.showResource&resID=#this.siteResourceID#&itemID=#arguments.event.getValue('itemID')#&sa=regStoreUser&regaction=locator&mode=stream')>
		<cfset arguments.event.setValue('storeID',local.strData.storeInfo.storeID)>

		<cfset local.strData.mainurl = arguments.event.getValue('mainurl')>

		<cfset local.strData.arrAllCategories = local.objStore.getStoreCategoryListing(storeID =local.strData.storeInfo.storeID)>
		<cfif NOT arrayLen(local.strData.arrAllCategories)>
			<cfreturn returnAppStruct("No Store Categories found.","echo")>
		</cfif>
		<cfset local.strData.strCategoryRootPaths = local.objStore.getStoreCategoryRootPaths(storeID =local.strData.storeInfo.storeID)>

		<cfset local.strData.theCategory = val(arguments.event.getValue('cat','0'))>
		<cfif structKeyExists(local.strData.strCategoryRootPaths, local.strData.theCategory)>
			<cfset local.strData.selectedCategoryRootPath = local.strData.strCategoryRootPaths[local.strData.theCategory]>
		<cfelse>
			<cfset local.strData.selectedCategoryRootPath = "">
		</cfif>

		<cfset local.strData.arrSubCategories = arrayNew(1)>
		<cfset local.strData.strParentCategory = structnew()>

		<cfif local.strData.theCategory eq 0>
			<cfset local.strData.isRootCategory = true>
			<cfset local.strData.strCategory = local.strData.arrAllCategories[1]>
		<cfelse>
			<cfset local.strData.isRootCategory = false>
			<cfloop index="local.x" from="1" to="#arrayLen(local.strData.arrAllCategories)#">
				<cfif local.strData.arrAllCategories[local.x].CategoryID eq local.strData.theCategory>
					<cfset local.strData.strCategory = local.strData.arrAllCategories[local.x]>
					<cfbreak>
				</cfif>
			</cfloop>
		</cfif>

		<!--- Hotfix-8451165 Invalid category supplied. Set to first category in list --->
		<cfif NOT structKeyExists(local.strData, "strCategory")>
			<cfset local.strData.isRootCategory = true>
			<cfset local.strData.strCategory = local.strData.arrAllCategories[1]>
		</cfif>

		<cfloop index="local.x" from="1" to="#arrayLen(local.strData.arrAllCategories)#">
			<cfif not local.strData.isRootCategory>
				<cfif local.strData.arrAllCategories[local.x].CategoryID eq local.strData.strCategory.parentCategoryID>
					<cfset local.strData.strParentCategory = local.strData.arrAllCategories[local.x]>
				</cfif> 
				<cfif local.strData.arrAllCategories[local.x].CategoryID eq local.strData.theCategory>
					<cfset local.strData.strCategory = local.strData.arrAllCategories[local.x]>
				</cfif>
				<!--- Populate subcategory array when we are NOT working with the root category --->
				<cfif local.strData.arrAllCategories[local.x].parentCategoryID eq local.strData.theCategory>
					<cfset arrayAppend(local.strData.arrSubCategories, local.strData.arrAllCategories[local.x] )>
				</cfif> 
			<cfelse>
				<!--- Populate subcategory array when we are working with the root category --->
				<cfif not len(local.strData.arrAllCategories[local.x].parentCategoryID)>
					<cfset arrayAppend(local.strData.arrSubCategories, local.strData.arrAllCategories[local.x] )>
				</cfif> 
			</cfif>
		</cfloop>

		<!--- dont show categories for selected actions --->
		<cfswitch expression="#arguments.event.getValue('sa','')#">
			<cfcase value="view,viewReceipt,regStoreUser,myPurchases,downloadAffirmations">
				<!--- hide categories - they are out of context here --->
				<cfset local.strData.showCategories = false>
			</cfcase>
			<cfdefaultcase>
				<cfset local.strData.showCategories = true>
			</cfdefaultcase>
		</cfswitch>

		<td class="tsAppBodyText"> 
			<div class="tsAppBodyText" style="margin-top:6px;">

		<cfswitch expression="#arguments.event.getValue('sa','')#">
			<cfcase value="view">
				<cfif session.cfcUser.memberData.memberID gt 0>
					<cfset local.objCart.updateMemberID(this.appInstanceID,session.cfcUser.memberData.memberID,local.store[this.appInstanceID].orderNumber)>
				</cfif>
				<cfset local.strData.cart = local.objCart.getCartData(storeid=local.strData.storeInfo.storeid, orderNumber=local.store[this.appInstanceID].orderNumber, appInstanceID=this.appInstanceID)>
				<cfset local.strData.qryItemsTotal = local.objCart.totalCartItems(local.strData.storeInfo.storeid,local.strData.cart)>
				
				<cfset local.strData.shippingOption = local.store[this.appInstanceID].shippingOption>
				
				<cfset local.strData.qryPurchaser = application.objMember.getMemberInfo(val(local.store[this.appInstanceID].CrtMemberID))>							

				<cfset local.strData.showCurrencyType =  arguments.event.getValue('mc_siteinfo.showCurrencyType')>							
				<cfset local.strData.defaultCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>							
				<cfset local.strData.appInstanceID = this.appInstanceID>							
				
				<cfset local.viewToUse = "store/#local.viewDirectory#/dsp_cart">
			</cfcase>
			<cfcase value="ViewCategory">
				<!--- show currency types --->
				<cfset local.strData.displayedCurrencyType = "">
				<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1>
					<cfset local.strData.displayedCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>
				</cfif>
				
				<cfif (NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcuser.memberData.IdentifiedAsMemberID)>
					<cfset local.strData.useMID = session.cfcuser.memberData.IdentifiedAsMemberID>
				<cfelseif val(local.store[this.appInstanceID].CrtMemberID)>
					<cfset local.strData.useMID = local.store[this.appInstanceID].CrtMemberID>
				<cfelse>
					<cfset local.strData.useMID = session.cfcuser.memberData.memberID>
				</cfif>

				<!---Set the default values for start and finish--->
				<cfset arguments.event.paramValue('start',1)>
				<cfset arguments.event.paramValue('disp',local.strData.storeInfo.maxRecords)>
				<cfset arguments.event.setValue('start',int(val(arguments.event.getValue('start'))))>
				<cfset arguments.event.setValue('disp',int(val(arguments.event.getValue('disp'))))>
				<cfset local.strData.start = arguments.event.getValue('start')>
				<cfset local.strData.disp = arguments.event.getValue('disp')>
				<cfset local.strData.end = arguments.event.getValue('start') + arguments.event.getValue('disp')>
				<cfset local.strData.pageName = arguments.event.getValue('mc_pageDefinition.pageName')>

				<cfset local.strData.cat = val(arguments.event.getValue('cat',0))>		
				<cfif NOT isValid('integer',local.strData.cat)>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
				</cfif>

				<cfquery name="local.strData.qryProductsInCategory" Datasource="#application.dsn.memberCentral.dsn#">
					set nocount on;
					
					declare @storeID int, @siteID int, @categoryID int;
					SET @storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strData.storeInfo.storeID#">;
					SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strData.storeInfo.siteID#">;
					SET @categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.event.getValue('cat',0))#">;
					
					IF OBJECT_ID('tempdb..##tmpItemCats') IS NOT NULL 
						DROP TABLE ##tmpItemCats;
					IF OBJECT_ID('tempdb..##tmpInnerProducts') IS NOT NULL 
						DROP TABLE ##tmpInnerProducts;
					IF OBJECT_ID('tempdb..##tmpProducts') IS NOT NULL 
						DROP TABLE ##tmpProducts;
					CREATE TABLE ##tmpItemCats (itemID int, categoryID int);
					CREATE TABLE ##tmpInnerProducts (itemID int, categoryID int, CategoryName varchar(200), productID varchar(50), summaryContentID int, productDate datetime);
					CREATE TABLE ##tmpProducts (row int, itemID int, categoryID int, categoryName varchar(200), categoryList varchar(max), productID varchar(50), 
						contenttitle varchar(max), rawContent varchar(201));

					INSERT INTO ##tmpItemCats (itemID, categoryID)
					select distinct spf.itemid, lpc.categoryID
					from dbo.store_Products as sp
					inner join dbo.store_ProductCategoryLinks lpc on lpc.itemid = sp.itemid
					inner join dbo.store_ProductFormats spf on lpc.itemid = spf.itemid and spf.status = 'A'
					inner join dbo.store_rates sr on spf.formatid = sr.formatid
					inner join dbo.cms_siteResources c on c.siteID = @siteID
						and c.siteResourceStatusID = 1
						and sr.siteResourceID = c.siteResourceID 
					inner join dbo.cms_siteResourceRights srr on srr.siteID = @siteID 
						and sr.siteResourceID = srr.resourceID
					inner join dbo.ams_groups g on g.groupid = srr.groupid
					where sp.storeID = @storeID
					and sp.[status] = 'A'
					and sp.showAvailable = 1
					and (sr.startDate is null or CAST(sr.startDate as date) <= CAST(getDate() as date))
					and (sr.endDate is null or CAST(sr.endDate as date) > CAST(getDate() as date));

					INSERT INTO ##tmpInnerProducts (itemID, categoryID, CategoryName, productId, summaryContentID, productDate)
					select sp.itemID, isnull(l.categoryID,0) as categoryID, sc.CategoryName, sp.productID, sp.summaryContentID, sp.productDate
					FROM dbo.store_Products as sp
					INNER JOIN dbo.store_ProductCategoryLinks as l on l.itemid = sp.itemid
						and l.categoryid = @categoryID
						and l.itemid in (	
							select itemid
							from ##tmpItemCats
							where categoryID = l.categoryID
						)
					inner join dbo.store_categories sc ON sc.storeID = @storeID
						AND sc.CategoryID = l.CategoryID	
					WHERE sp.storeID = @storeID
					and sp.[status] = 'A'
					and sp.showAvailable = 1;

					insert into ##tmpProducts (row, itemID, categoryID, categoryName, categoryList, productID, contentTitle, rawContent)
					select ROW_NUMBER() OVER( 
						ORDER BY 
						<cfswitch expression="#local.strData.storeInfo.defaultSortOrder#">
							<cfcase value="prodTitle">isnull(prodcontent.contenttitle,'') asc</cfcase>
							<cfcase value="prodDateAsc">innerProducts.productDate asc, isnull(prodcontent.contenttitle,'') asc</cfcase>
							<cfcase value="prodDateDesc">innerProducts.productDate desc, isnull(prodcontent.contenttitle,'') asc</cfcase>
						</cfswitch>, innerProducts.itemID) as row, 
						innerProducts.itemID, innerProducts.categoryID, innerProducts.categoryName, 
						STUFF((SELECT ', ' + c.thePathExpanded
							FROM dbo.fn_getRecursiveStoreCategories(@storeID, null, innerProducts.categoryID) AS c
							INNER JOIN dbo.store_ProductCategoryLinks AS cl ON cl.categoryID = c.categoryID
							WHERE cl.itemID = innerProducts.itemID
							AND c.visibility = 'N'
							AND c.categoryID <> innerProducts.categoryID  -- Exclude current category
							FOR XML PATH('')
						), 1, 1, '') AS categoryList,
						innerProducts.productID, isnull(prodcontent.contenttitle,'') as contenttitle, left(prodcontent.rawcontent,201) as rawcontent
					from ##tmpInnerProducts as innerProducts
					cross apply dbo.fn_getContent(innerProducts.summaryContentID,1) as prodcontent;

					declare @ttlCount int;
					select @ttlcount = count(*) from (select DISTINCT CategoryID, ContentTitle, ItemID, ProductID from ##tmpProducts) as internalQuery;
					
					select @ttlcount as totalCount, *
					from ##tmpProducts
					where row >= #arguments.event.getValue('start')# and row < #local.strData.end#
					order by row;

					IF OBJECT_ID('tempdb..##tmpItemCats') IS NOT NULL 
						DROP TABLE ##tmpItemCats;
					IF OBJECT_ID('tempdb..##tmpInnerProducts') IS NOT NULL 
						DROP TABLE ##tmpInnerProducts;
					IF OBJECT_ID('tempdb..##tmpProducts') IS NOT NULL 
						DROP TABLE ##tmpProducts;
				</cfquery>

				<cfset local.viewToUse = "store/#local.viewDirectory#/storeViewCategory">
			</cfcase>

			<cfcase value="ViewDetails">
				<cfset local.strData.qryProduct = getProducts(local.strData.storeInfo.storeID,arguments.event)>
				<cfif local.strData.qryProduct.recordcount is not 1>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
				<cfelse>
					<cfif StructKeyExists(local.strData.qryProduct,"categoryID") AND LEN(local.strData.qryProduct.categoryID)>
						<cfloop index="local.x" from="1" to="#arrayLen(local.strData.arrAllCategories)#">
							<cfif local.strData.arrAllCategories[local.x].CategoryID eq local.strData.qryProduct.categoryID>
								<cfset local.strData.strCategory = local.strData.arrAllCategories[local.x]>
								<cfbreak>
							</cfif>
						</cfloop>
					</cfif>
				</cfif>

				<cfset local.currentPageTitle = arguments.event.getValue('mc_pageDefinition.pagetitle')>
				<cfset arguments.event.getCollection()['mc_pageDefinition']['pagetitle'] = '#local.strData.qryProduct.contenttitle# - #local.currentPageTitle#'>

				<!--- show currency types --->
				<cfset local.strData.displayedCurrencyType = "">
				<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1>
					<cfset local.strData.displayedCurrencyType = arguments.event.getValue('mc_siteinfo.defaultCurrencyType')>
				</cfif>
				
				<!--- determine memberid --->
				<cfif (NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcuser.memberData.IdentifiedAsMemberID)>
					<cfset local.useMID = session.cfcuser.memberData.IdentifiedAsMemberID>
				<cfelseif val(local.store[this.appInstanceID].CrtMemberID)>
					<cfset local.useMID = local.store[this.appInstanceID].CrtMemberID>
				<cfelse>	 
					<cfset local.useMID = session.cfcuser.memberData.memberID>
				</cfif>	

				<!--- get product pricing --->
				<cfset local.strData.qryProductFormats = getFormats(arguments.event,local.strData.qryProduct.itemid)>
				<cfset local.rfid = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="StoreProductRate", functionName="qualify")>

				<cfquery name="local.strData.qryProductPricing" datasource="#application.dsn.membercentral.dsn#">
					set nocount on;

					DECLARE @siteID int;
					SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strData.storeInfo.siteID#">;
					<cfif local.useMID gt 0>
						declare @FID int, @groupPrintID int;
						select @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rfid#">;
						select @groupPrintID = groupPrintID from dbo.ams_members where memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.useMID#">
					</cfif>
					
					select distinct pf.formatID, pf.name, pf.quantity, sr.rateID, sr.rateName, pf.isAffirmation, pf.OfferAffirmations,
						coalesce(dbo.fn_store_getBestRateOverride(sr.rateid, sr.formatid),sr.rate) as rate, pf.formatOrder
					from dbo.store_ProductFormats as pf
					inner join dbo.store_rates as sr on sr.formatID = pf.formatID
					inner join dbo.cms_siteResources as c on sr.siteResourceID = c.siteResourceID and c.siteID = @siteID
					inner join dbo.cms_siteResourceRights as srr on c.siteResourceID = srr.resourceID and srr.siteID =@siteID
					inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = c.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
					<cfif local.useMID gt 0>
						inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
							and srfrp.siteresourceID = sr.siteResourceID
							and srfrp.functionID = @FID
						inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
							and gprp.rightPrintID = srfrp.rightPrintID
							and gprp.groupPrintID = @groupPrintID
					</cfif>
					where pf.itemid = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strData.qryProduct.itemId#">
					and pf.status = 'A'
					and (sr.startDate is null or CAST( CONVERT( char(8), sr.startDate, 112) AS datetime) <= CAST( CONVERT( char(8), getDate(), 112) as datetime))
					and (sr.endDate is null or DATEADD (d , 1, CAST( CONVERT( char(8), sr.endDate, 112) AS datetime) ) > getdate())
					order by pf.formatOrder, pf.name, sr.rateName, sr.rateID;
				</cfquery>

				<cfif NOT local.strData.qryProductPricing.RecordCount GT 0>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
				</cfif>
				<cfset local.strData.strProductPricing = arrayNew(1)>
				<cfset arrayResize(local.strData.strProductPricing,local.strData.qryProductPricing.formatOrder[local.strData.qryProductPricing.recordCount])>

				<cfloop index="idx" from="1" to="#arrayLen(local.strData.strProductPricing)#">
					<cfscript>
						local.strData.strProductPricing[idx] = structNew();
						local.strData.strProductPricing[idx].formatID = '';
						local.strData.strProductPricing[idx].isAffirmation = '';
						local.strData.strProductPricing[idx].Name = '';
						local.strData.strProductPricing[idx].offerAffirmations = '';
						local.strData.strProductPricing[idx].Quantity = '';
						local.strData.strProductPricing[idx].Rates = arrayNew(1);
					</cfscript>
				</cfloop>

				<cfloop query="local.strData.qryProductPricing">
					<cfscript>
						local.strData.strProductPricing[formatOrder].formatID = formatID;
						local.strData.strProductPricing[formatOrder].isAffirmation = isAffirmation;
						local.strData.strProductPricing[formatOrder].Name = Name;
						local.strData.strProductPricing[formatOrder].offerAffirmations = offerAffirmations;
						local.strData.strProductPricing[formatOrder].Quantity = Quantity;

						local.thisRate = structNew();
						local.thisRate.rateID = rateID;
						local.thisRate.rateName = rateName;
						local.thisRate.Rate = Rate;
						arrayAppend(local.strData.strProductPricing[formatOrder].Rates,local.thisRate);
					</cfscript>
				</cfloop>

				<cfset local.strData.qryPurchasedProducts = getMyPurchasedProducts(memberID=local.useMID)>
				<cfset local.strData.cart = local.objCart.getCartData(local.strData.storeInfo.storeID,local.store[this.appInstanceID].orderNumber,this.appInstanceID)>
			
				<cfset local.strData.offerAddToCart = false>
				<cfif local.strData.qryProductPricing.recordcount gt 0>
					<cfset local.strData.offerAddToCart = true>
				</cfif>
								
				<!--- Move controller logic from view --->
				<cfset local.strData.electronicDeliveryInfo = "">
				<cfloop query="local.strData.qryProductFormats">				
					<cfset local.qryDocuments = local.objStore.getStoreDocuments(formatID=local.strData.qryProductFormats.formatid)>
					<cfif local.qryDocuments.recordCount>
						<cfsavecontent variable="local.eInfo">
							<cfoutput>
							<div class="tsAppBodyText" style="margin-bottom:6px;">
								Documents available with #local.strData.qryProductFormats.name# purchase:<br/>
								<cfloop query="local.qryDocuments">
									<div style="margin-left:15px;">#local.qryDocuments.docTitle#</div>
								</cfloop>														
							</div>
							</cfoutput>
						</cfsavecontent>					
						<cfset local.strData.electronicDeliveryInfo = local.strData.electronicDeliveryInfo & local.eInfo>					
					</cfif>
				</cfloop>														

				<!--- credits --->
				<cfset local.strData.strFormatCredits = structNew()>
				<cfif local.strData.storeInfo.displayCredit is 1>
					<cfoutput query="local.strData.qryProductFormats" group="Formatid">
						<cfset local.tmpCreditInfo = { formatname=local.strData.qryProductFormats.name, creditInfo=getCreditInfoForFormat(formatID=local.strData.qryProductFormats.formatid) }>
						<cfset structInsert(local.strData.strFormatCredits,local.strData.qryProductFormats.formatid,local.tmpCreditInfo)>
					</cfoutput>
				</cfif>

				<cfset local.strData.cat = val(arguments.event.getValue('cat',0))>
				<cfset local.viewToUse = "store/#local.viewDirectory#/dsp_productDetails">
			</cfcase>
			<cfcase value="UpdateCart">	
				<cfset local.updateProductList = []>					
				<cfloop list="#arguments.event.getValue('fieldNames','')#" index="local.formItem">
					<cfif listFirst(local.formItem,"_") EQ "Quantity">
						<cfset local.currentID = listLast(local.formItem,"_")>
						<cfset local.currentQuantity = evaluate('form.' & local.formItem)>
						<cfif local.currentQuantity gt 0>
							<cfset local.objCart.updateQuantity(local.currentID,local.currentQuantity)>
						</cfif>
					</cfif>
					<cfset local.prevQuantity = 0>
					<cfif listFirst(local.formItem,"_") EQ "QuantityPrev">
						<cfset local.prevQuantity = evaluate('form.' & local.formItem)>	
					</cfif>
					<cfset local.strAction = "" >
					<cfset local.updatedQuantity = 0>
					<cfif local.currentQuantity GT local.prevQuantity>
						<cfset local.strAction  ="add">
						<cfset local.updatedQuantity = local.currentQuantity - local.prevQuantity >
					<cfelseif local.currentQuantity LT local.prevQuantity>
					 	<cfset local.strAction  ="remove">
						<cfset local.updatedQuantity = local.prevQuantity - local.currentQuantity>
					</cfif>
					<cfif local.formItem EQ "shippingOption" and len(trim(evaluate('form.' & local.formItem)))>
						<cfset local.store[this.appInstanceID].shippingOption = evaluate('form.' & local.formItem)>
					</cfif>	
					<cfif LEN(local.strAction) AND local.prevQuantity GT 0>
						<cfset arrayAppend(local.updateProductList, {
							"strAction":local.strAction,
							"updatedQuantity": local.updatedQuantity,
							"cartItemID": local.currentID
						})>	
					</cfif>							
				</cfloop>
				<cfset application.mcCacheManager.sessionSetValue(keyname='updateProductList', value=local.updateProductList)>
				<cfset local.checkCart = local.objCart.checkQuantity(local.store[this.appInstanceID].orderNumber)>
				<cfset local.shipKey = local.store[this.appInstanceID].shippingOption>
				<cfset local.objCart.updateOrder(local.strData.storeInfo.storeID,local.store[this.appInstanceID].orderNumber,local.shipKey,this.appInstanceID)>				
				<cflocation url="#arguments.event.getValue('mainurl')#&sa=View" addtoken="no">
			</cfcase>
			<cfcase value="AddToCart">
				<cfset local.data = addCartItem(local.strData.storeInfo.storeID,arguments.event)>
				<cfset local.shipKey = local.store[this.appInstanceID].shippingOption>
				<cfset local.objCart.updateOrder(local.strData.storeInfo.storeID,local.store[this.appInstanceID].orderNumber,local.shipKey,this.appInstanceID)>
				<cfreturn returnAppStruct(local.data,"echo")>
			</cfcase>
			<cfcase value="regStoreUser">
				<cfset local.data = CreateObject("component","storeReg").doStoreReg(event=arguments.event,appInstanceID=this.appInstanceID )>
				<cfreturn returnAppStruct(local.data,"echo")>
			</cfcase>						
			<cfcase value="remove">
				<cfif NOT isValid('integer', arguments.event.getValue('ItemID', 0))>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
				</cfif>
				<cfset local.checkCart = local.objCart.removeCartItem(arguments.event.getValue('ItemID',0))>
				<cfset local.shipKey = local.store[this.appInstanceID].shippingOption>
				<cfset local.objCart.updateOrder(local.strData.storeInfo.storeID,local.store[this.appInstanceID].orderNumber,local.shipKey,this.appInstanceID)>
				<cflocation url="#arguments.event.getValue('mainurl')#&sa=View" addtoken="no">
			</cfcase>
			<cfcase value="downloadAffirmations">
				<cfsetting requesttimeout="500">

				<cfset local.orderInfo = local.objCart.getAppInstanceIDFromOrderNumber(arguments.event.getValue('orderNumber'))>
				<cfset local.maxAffirmations = 500>				
				<cfset local.orderID = local.orderInfo.orderID>	
				
				<!--- generate affirmation PDF per format --->
				<cfquery name="local.qryAffirmations" datasource="#application.dsn.membercentral.dsn#">
					select co.ASID, aff.affirmationID, aff.productFormatID, aff.affirmationCode, cas.affirmationFileName, pCon.contentTitle,
						aff.dateClaimed, pf.name as formatName
					from dbo.crd_affirmations as aff
					inner join dbo.crd_affirmationTypes cat on cat.affirmationTypeID = aff.affirmationTypeID and cat.affirmationType = 'paper'
					inner join dbo.crd_offerings as co on co.offeringID = aff.offeringID
					inner join dbo.crd_authoritySponsors as cas on cas.ASID = co.ASID
					inner join dbo.store_ProductFormats as pf on pf.Formatid = aff.productFormatID
					inner join dbo.store_products as p on p.storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strData.storeInfo.storeID#"> and p.itemID = pf.itemID
					inner join dbo.cms_contentLanguages as pCon on pCon.contentID = p.productContentID and pCon.languageID = 1
					where aff.orderid = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.orderID#">
					and aff.status = 'A'
					order by co.ASID, aff.productFormatID, aff.affirmationCode
				</cfquery>
				
				<cfif local.qryAffirmations.recordCount and local.qryAffirmations.recordCount lt local.maxAffirmations>
					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="affbundle")>

					<cftry>
						<cfloop query="local.qryAffirmations">
							<cfset local.affirmationID = local.qryAffirmations.affirmationID>
							<cfset local.affirmationCode = local.qryAffirmations.affirmationCode>
							<cfset local.productTitle = local.qryAffirmations.contentTitle>
							<cfset local.productFormatName = local.qryAffirmations.formatName>
							<cfset local.dateClaimed = local.qryAffirmations.dateClaimed>
							<cfif fileExists(ExpandPath('/assets/common/affirmations/#local.qryAffirmations.affirmationFileName#.cfm'))>
								<cfinclude template="/assets/common/affirmations/#local.qryAffirmations.affirmationFileName#.cfm">										
							</cfif>										
						</cfloop>
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
						</cfcatch>
					</cftry>
					<cftry>
						<!--- merge and encrypt pdfs --->
						<cfdirectory action="LIST" directory="#local.strFolder.folderPath#" name="local.qryPDFs" filter="*_affirmation.pdf" sort="name">
						<cfif not local.qryPDFs.recordcount>
							<cfthrow message="No Affirmation PDFs found to zip">
						</cfif>
						<cfset application.objCommon.mergePDFs(local.strFolder.folderPath,valuelist(local.qryPDFs.name),"affirmationBundle.pdf")>
						
						<cfset local.dlResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/affirmationBundle.pdf", displayName="affirmationBundle.pdf", deleteSourceFile=1)>
						<cfif NOT local.dlResult>
							<cfsavecontent variable="local.data">
								<cfoutput>	
									<script>
									alert("File not found!");
									window.close();	
									</script>						
								</cfoutput>
							</cfsavecontent>	
						</cfif>
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage='Failure generating/merging affirmations. Make sure that this credit sponsor has an affirmation template') />
							<cfrethrow/>
						</cfcatch>
					</cftry>

					<cfreturn returnAppStruct(local.data,"echo")>								

				<cfelse>
					
					<cfsavecontent variable="local.data">
						<cfoutput>	
							<script>
							alert("File not found!");
							window.close();	
							</script>						
						</cfoutput>
					</cfsavecontent>
					
					<cfreturn returnAppStruct(local.data,"echo")>									
				
				</cfif>
			</cfcase>				
			<cfcase value="viewReceipt">
				<cfset local.objAdminStore = CreateObject('component', 'model.admin.store.store')>
				<cfset local.orderInfo = local.objCart.getAppInstanceIDFromOrderNumber(arguments.event.getValue('orderNumber'))>

				<cfset arguments.event.setValue('storeID',val(local.strData.storeInfo.storeID))>
				<cfset arguments.event.setValue('orderID',val(local.orderInfo.orderID))>
				<cfset local.qryOrder = local.objAdminStore.getOrder(storeID=arguments.event.getValue('storeid'), orderID=arguments.event.getValue('orderID'))>
				<cfif local.qryOrder.recordcount is 0>
					<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="false">
				</cfif>

				<cfset local.objStoreReg = CreateObject("component","model.store.storeReg")>
				<cfif local.viewDirectory eq "default">
					<cfset local.strData.emailContent = local.objStoreReg.orderReceiptForEmail(siteCode=arguments.event.getValue('mc_siteinfo.sitecode'),
															storeID=local.strData.storeInfo.storeID, orderID=local.orderInfo.orderID,
															onlineReceipt=1, notes='')>
				<cfelse>	
					<cfset local.strData.emailContent = local.objStoreReg.orderReceiptForMobile(siteCode=arguments.event.getValue('mc_siteinfo.sitecode'),
															storeID=local.strData.storeInfo.storeID, orderID=local.orderInfo.orderID,
															onlineReceipt=1, notes='')>
				</cfif>

				<cfset local.viewToUse = "store/#local.viewDirectory#/store_receipt">							
			</cfcase>
			<cfcase value="myPurchases">
				<cfscript>
					local.objAdminStore = CreateObject('component', 'model.admin.store.store');
					if (NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser))
						local.strData.useMID = 0;
					else
						local.strData.useMID = session.cfcuser.memberData.memberID;

					local.strData.qryMyStorePurchases = getMyStorePurchases(arguments.event.getValue('mc_siteinfo.siteid'));
					local.strData.siteCode = arguments.event.getValue('mc_siteinfo.sitecode');
				</cfscript>
				<cfset local.viewToUse = "store/#local.viewDirectory#/dsp_myPurchases">							
			</cfcase>
			<cfcase value="playStream">
				<cfscript>
					if (NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser))
						local.strData.useMID = 0;
					else
						local.strData.useMID = session.cfcuser.memberData.memberID;

					local.orderDetailID = arguments.event.getValue('odetailID',0);
					local.orderDetailStreamID = arguments.event.getValue('odetailStreamID',0);
					local.formatID = arguments.event.getValue('fid',0);

					local.strData.streamDetails = getStreamInfo(orderDetailID=local.orderDetailID, orderDetailStreamID=local.orderDetailStreamID, formatID=local.formatID);

					if (local.strData.streamDetails.success) {
						local.providerObj = createObject("component","model.system.platform.streamProfiles.#local.strData.streamdetails.providerCode#");
						local.providerObj.initProfileSettings(local.strData.streamDetails.profileFields);
						local.providerObj.initUsageSettings(local.strData.streamDetails.usageFields);

						local.strData.playerCode = local.providerObj.play();
						local.logkey = "#getTickCount()#|#local.strData.streamDetails.logid#|#local.strData.useMID#|#randrange(1,1000000)#";
						local.encryptString = encrypt(local.logkey ,"TRiaL_SMiTH", "CFMX_COMPAT", "Hex");
					}
				</cfscript>
				<cfsavecontent variable="local.strData.loggingSystemJS">
					<cfoutput>
						<script type="text/javascript">
							var thisStreamingLogID = '#local.encryptString#';
							var streamingLogReady = false;
							var streamingLogHoldingArray = [];
							var timeString = "#dateformat(now(),"mm/dd/yyyy")# #timeFormat(now(), "HH:mm:ss")#";
							var currentTime = moment(timeString,'MM/DD/YYYY HH:mm:ss');
							var clockInterval = setInterval(function(){
								currentTime.add(1,'s');
							},1000);

							MCPromises.BackendPlatformServices.then(function() {
								MCBackendPlatformServices.MCLoggingService.addLoggingSession(thisStreamingLogID);
								streamingLogReady = true;
								if (streamingLogHoldingArray.length){
									MCBackendPlatformServices.MCLoggingService.addLogEntry(thisStreamingLogID,streamingLogHoldingArray);
								}
							}).catch(error => {
								let msg = 'Store failed to access logging service';
								if (MCJSErrorReporting && MCJSErrorReporting.promiseRejectionHandler)
									MCJSErrorReporting.promiseRejectionHandler(msg)
								else 
									console.error(`MCJSErrorReporting.promiseRejectionHandler not defined, falling back to console message: ${msg}`);
							});

							var streamingLogAddEntry = function(logline){
								var modifiedLogline = currentTime.format("MM/DD/YYYY HH:mm:ss") + '\t{merge_ipaddress}\t' + logline;
								if (streamingLogReady) {
									MCBackendPlatformServices.MCLoggingService.addLogEntry(thisStreamingLogID,modifiedLogline);
								} else {
									streamingLogHoldingArray.push(modifiedLogline);
								}
							};
							
						</script>
					</cfoutput>
				</cfsavecontent>
				<cfset local.viewToUse = "store/#local.viewDirectory#/dsp_playStream">							
			</cfcase>

			<cfcase value="Checkout">
				<cfscript>
				//getCartData
				local.cart = local.objCart.getCartData(local.strData.storeInfo.storeID,local.store[this.appInstanceID].orderNumber,this.appInstanceID);

				// if cart is empty then redirect back to cart page to show cart is empty
				if (NOT local.cart.recordCount) application.objCommon.redirect('#arguments.event.getValue('mainurl')#&sa=View');

				// set cart totals
				local.totals = local.objCart.totalCartItems(local.strData.storeInfo.storeid,local.cart);
				local.shipKey = local.store[this.appInstanceID].shippingOption;
				local.totalShipping = (local.totals.shippingOptions[local.shipKey].PerShipment + local.totals.shippingOptions[local.shipKey].totalItems);
				
				arguments.event.setValue('totalProduct',local.totals.totalRate);
				arguments.event.setValue('totalShipping',local.totalShipping);
				arguments.event.setValue('shippingKey',local.shipKey);
				arguments.event.setValue('totalTax','0');

				// check to see if order already exists. create if doesnt exist.
				if (NOT local.objCart.doesOrderExist(local.store[this.appInstanceID].orderNumber))
					local.objCart.createOrder(local.strData.storeInfo.storeID,local.store[this.appInstanceID].orderNumber,arguments.event);
				else
					local.objCart.updateOrder(local.strData.storeInfo.storeID,local.store[this.appInstanceID].orderNumber,local.shipKey,this.appInstanceID);
				
				// redirect to buy now
				application.objCommon.redirect('/?pg=buyNow&item=store-#local.strData.storeInfo.storeID#|#local.store[this.appInstanceID].orderNumber#');
				</cfscript>	
			</cfcase>
			<cfcase value="search">
				<!--- Need searchterms converted to --->
				<cfset local.strData.qryProduct = getProducts(local.strData.storeInfo.storeID,arguments.event)>
				<cfset local.strData.searchQuery = arguments.event.getValue('searchQuery','')>

				<cfif local.viewDirectory eq 'default'>
					<cflocation url="#local.strData.storeSearchBucketLink#" addtoken="false">
				</cfif>

				<cfset local.viewToUse = "store/#local.viewDirectory#/dsp_storeSearch">
			</cfcase>
			<cfdefaultcase>
				<cfset local.viewToUse = "store/#local.viewDirectory#/dsp_storeHome">
			</cfdefaultcase>
		</cfswitch>

		<!--- record app hit --->
		<cfset application.objPlatformStats.recordAppHit(appname="store",appsection="")>
		<cfset application.mcCacheManager.sessionSetValue(keyname='store', value=local.store)>		

		<!--- return the app struct --->
		<cfreturn returnAppStruct(local.strData,local.viewToUse)>
		
	</cffunction>
		
	<cffunction name="addCartItem" access="private" output="false" returntype="void">
		<cfargument name="storeid" type="numeric" required="yes">
		<cfargument name="Event" type="any" required="yes">
		
		<cfscript>
		var local = structNew();
		local.objCart = CreateObject('component', 'model.store.shoppingCart');
		local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={});
		</cfscript>

		<cfloop collection="#arguments.event.getCollection()#" item="local.key">
			<cfif left(local.key,9) eq "Quantity_">
				<cfset local.formatID = GetToken(local.key,2,'_')>
				<cfset local.rateID = GetToken(local.key,3,'_')>
				<cfif int(val(arguments.event.getValue(local.key))) gt 0>
					<cfset local.cartItem = local.objCart.getCartItem(this.appInstanceID,arguments.event.getValue('ItemID'),local.formatID)>
					<cfif local.cartItem.recordCount>
						<cfset local.newQuantity = arguments.event.getValue(local.key) + local.cartItem.quantity>
						<cfset local.objCart.updateQuantity(local.cartItem.cartItemID,local.newQuantity)>
					<cfelse>
						<cfset local.objCart.insertCartItem(storeID=arguments.storeid, orderNumber=local.store[this.appInstanceID].orderNumber, 
								ItemID=arguments.event.getValue('ItemID'), formatID=local.formatID, rateID=local.rateID, Quantity=arguments.event.getValue(local.key))>
						<cfset local.cartItem = local.objCart.getCartItem(this.appInstanceID,arguments.event.getValue('ItemID'),local.formatID)>
					</cfif>
					<cfset application.mcCacheManager.sessionSetValue(keyname='cartItemID', value=local.cartItem.cartItemID)>
				</cfif>
			</cfif>
		</cfloop>		
		<cflocation addtoken="no" url="#arguments.event.getValue('mainurl')#&sa=View">
	</cffunction>
	
	<cffunction name="getStoreInfo" access="public" output="FALSE" returntype="query">
		<cfargument name="appInstanceID" type="numeric" required="yes">

		<cfset var qryStore = "">
	
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryStore">
			select s.storeid, s.siteid, s.applicationInstanceID, s.mainContentID, s.emailRecipient, s.EndOrderSubj, s.ShowProductID, s.MaxRecords, 
				s.showCartThumbnails, storecontent.rawContent as mainContent, s.GLAccountID, '' as GLAccountPath, s.OfferAffirmations,
				s.OfferStreams, s.displayCredit, s.defaultSortOrder, s.emailReplyTo, oi.organizationName as supportProviderName, 
				oi.phone as supportProviderPhone, oi.email as supportProviderEmail
			from dbo.store as s
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = s.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID and sr.siteResourceStatusID = 1
			INNER JOIN dbo.orgIdentities as oi ON oi.orgIdentityID = s.orgIdentityID
			cross apply dbo.fn_getContent(s.mainContentID,1) as storecontent
			where s.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.appInstanceID#">
		</cfquery>
		
		<cfreturn qryStore>
	</cffunction>

	<cffunction name="getOrderDate" access="private" output="FALSE" returntype="string">
		<cfargument name="orderID" type="numeric" required="yes">

		<cfset var qryStore = "">
	
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryStore">
			SELECT dateOfOrder
			from dbo.store_orders
			where orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
		</cfquery>
		
		<cfreturn qryStore.dateOfOrder>
	</cffunction>
	
	<cffunction name="getProducts" access="public" output="FALSE" returntype="query">
		<cfargument name="storeid" type="numeric" required="yes">
		<cfargument name="Event" type="any">

		<cfset var qryProducts = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryProducts">
			SET NOCOUNT ON;

			DECLARE @storeID int = <cfqueryparam value="#arguments.storeID#" cfsqltype="CF_SQL_VARCHAR">;

			SELECT p.ItemID, p.storeID, p.productID, p.productContentID,
				p.showQuantity, p.status, p.showAvailable, p.productDate, p.GLAccountID, storecontent.contenttitle,
				storecontent.rawcontent, summarycontent.rawcontent as summary,
				(
					select top 1 c.categoryID
					from dbo.store_categories c
					inner join dbo.store_ProductCategoryLinks pcl on pcl.categoryID = c.categoryID
						and pcl.itemID = p.itemID 
					where c.storeID = @storeID
					order by c.categoryID
				) as categoryID,
				(
					select top 1 c.CategoryName
					from dbo.store_categories c
					inner join dbo.store_ProductCategoryLinks pcl on pcl.categoryID = c.categoryID
						and pcl.itemID = p.itemID 
					where c.storeID = @storeID
					ORDER BY c.categoryID
				) as categoryName
			FROM dbo.store_Products p
			cross apply dbo.fn_getContent(p.productContentID, 1) as storecontent
			cross apply dbo.fn_getContent(p.summaryContentID, 1) as summarycontent				
			where p.storeID = @storeID
			<cfif len(arguments.event.getValue('itemid')) and NOT arguments.event.valueExists('SearchQuery') AND isValid('integer',arguments.event.getValue('itemid'))>
 				and p.ItemID = <cfqueryparam value="#val(arguments.event.getValue('ItemID'))#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			<cfif arguments.event.valueExists('TheItemID') AND isValid('integer',arguments.event.getValue('TheItemID'))>
				and p.ItemID = <cfqueryparam value="#val(arguments.event.getValue('TheItemID'))#" cfsqltype="CF_SQL_INTEGER">
			</cfif>
			<cfif arguments.event.valueExists('SearchQuery') and len(arguments.event.getValue('SearchQuery'))>
				and (
					p.ProductID LIKE <cfqueryparam value="%#arguments.event.getValue('SearchQuery')#%" cfsqltype="CF_SQL_VARCHAR">
					OR 
					storeContent.contentTitle LIKE <cfqueryparam value="%#arguments.event.getValue('SearchQuery')#%" cfsqltype="CF_SQL_VARCHAR">
				)
			</cfif>
			AND p.status = 'A'
			AND p.showAvailable = 1
			ORDER BY p.ProductContentID;
		</cfquery>

		<cfreturn qryProducts>
	</cffunction>

	<cffunction name="getMyStorePurchases" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
			
		<cfset var qryOrders = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryOrders">
			SELECT o.memberID, o.orderID, o.orderNumber, mActive.lastname + ', ' + mActive.firstname as membername, o.DateOfOrder, o.totalProduct, o.totalTax, o.totalShipping, 
				ROW_NUMBER() OVER (ORDER BY DateOfOrder desc) as row 
			FROM dbo.store_orders as o
			INNER JOIN dbo.store as s on s.storeID = o.storeID 
				and s.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteId#">
			INNER JOIN dbo.ams_members as m on m.memberid = o.memberID 
			INNER JOIN dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				and mActive.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
			WHERE o.orderCompleted = 1	
		</cfquery>

		<cfreturn qryOrders>
	</cffunction>

	<cffunction name="getMyPurchasedProducts" access="private" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
			
		<cfset var qryProducts = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryProducts">
			select od.productItemID
			from dbo.store_orders as o
			inner join dbo.store_orderDetails as od on od.orderID = o.orderID
			INNER JOIN dbo.ams_members as m on m.memberid = o.memberID 
			INNER JOIN dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				and mActive.memberID = <cfqueryparam value="#arguments.memberID#" cfsqltype="CF_SQL_INTEGER">
			where o.orderCompleted = 1
		</cfquery>
		
		<cfreturn qryProducts>
	</cffunction>

	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">

		<cfscript>
			var local = structNew();
			// SET EVENT SPECIFICATION ----------------------------------------------
			variables.isCommunityReady = FALSE;
			variables.isMultiInstanceReady 	= FALSE;
			arguments.event.paramValue('appTypeID','0');
			// LOAD OBJECTS ---------------------------------------------------------
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			//queries ---------------------------------------------------------------
			local.appInfo = arguments.appInfo;
			// call the contruct to do all the page validation and form params ------
			contructAppInstanceForm(arguments.event,local.appInfo);
		</cfscript>
		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					set nocount on;

					declare @siteID int, @languageID int, @sectionID int, @isVisible bit, @pageName varchar(50), @pageTitle varchar(200), @pagedesc varchar(400),
						@zoneID int, @pageTemplateID int, @pageModeID int, @pgResourceTypeID int, @allowReturnAfterLogin bit, @applicationInstanceName varchar(100),
						@applicationInstanceDesc varchar(200), @applicationInstanceID int, @siteResourceID int, @pageID int;

					SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					SET @languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('lid')#">;
					SET @isVisible = 1;
					SET @pageName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageName')#">;
					SET @pageTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageTitle')#">;
					SET @pagedesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageDesc')#">;
					SET @zoneID = dbo.fn_getZoneID('Main');
					SET @pageTemplateID = NULL;
					SET @pageModeID = <cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('pageModeID')#"></cfif>;
					SET @allowReturnAfterLogin = 1;
					SET @applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceName')#">;
					SET @applicationInstanceDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceDesc')#">;
					SET @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sectionID')#">;
					SET @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage');

					EXEC dbo.cms_createApplicationInstanceStore @siteid = @siteid, @languageID = @languageID, @sectionID = @sectionID, @isVisible = @isVisible, @pageName = @pageName,
						@pageTitle = @pageTitle, @pagedesc = @pagedesc, @zoneID = @zoneID, @pageTemplateID = @pageTemplateID, @pageModeID = @pageModeID, @pgResourceTypeID = @pgResourceTypeID, 
						@defaultGLAccountID = <cfqueryparam value="#arguments.event.getValue('GLAccountID',0)#" cfsqltype="cf_sql_integer">,
						@allowReturnAfterLogin = @allowReturnAfterLogin, @applicationInstanceName = @applicationInstanceName, @applicationInstanceDesc = @applicationInstanceDesc,
						@applicationInstanceID = @applicationInstanceID OUTPUT, @siteResourceID = @siteResourceID OUTPUT, @pageID = @pageID OUTPUT;

					select @applicationInstanceID as applicationInstanceID, @siteResourceID as siteResourceID, @pageID as pageID;
				</cfquery>
				<cfset local.message = 1>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.message = 2>
				</cfcatch>
			</cftry>
			<cfoutput>
				<script language="javascript">
					top.reloadPageTable();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		<cfelse>
			<cfoutput>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getMode">
					select dbo.fn_getmodeId('Full') as pageModeID
				</cfquery>
				<cfset arguments.event.setValue('pageModeID',local.getMode.pageModeID)>
				<cfset showAppInstanceForm(arguments.event,local.appInfo)>
			</cfoutput>
		</cfif>
	</cffunction>
	
	<cffunction name="getFormats" access="public" output="FALSE" returntype="query" hint="get Formats">
		<cfargument name="Event" type="any" />
		<cfargument name="itemid" type="numeric" required="true" />

		<cfset var qryFormats = "" />

		<cfquery name="qryFormats" datasource="#application.dsn.memberCentral.dsn#">
			select distinct pf.Formatid, pf.Itemid, pf.Name, pf.GLAccountID, pf.status, pf.offerAffirmations,
				pf.shippingGLAccountID, pf.isAffirmation, pf.quantity, pf.formatOrder
			from dbo.store_ProductFormats as pf
			inner join dbo.store_rates as sr on sr.formatID = pf.formatID
			inner join dbo.cms_siteResources as c on sr.siteResourceID = c.siteResourceID 
			inner join dbo.cms_siteResourceRights as srr on c.siteResourceID = srr.resourceID and srr.siteID = c.siteID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = c.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where pf.itemid = <cfqueryparam value="#arguments.itemid#" cfsqltype="cf_sql_integer" />
			and pf.status = 'A'
			and (sr.startDate is null or CAST( CONVERT( char(8), sr.startDate, 112) AS datetime) <= CAST( CONVERT( char(8), getDate(), 112) as datetime))
			and (sr.endDate is null or DATEADD (d , 1, CAST( CONVERT( char(8), sr.endDate, 112) AS datetime) ) > getdate())						
			order by pf.formatOrder
		</cfquery>

		<cfreturn qryFormats />
	</cffunction>	
	
	<cffunction name="getFormatRates" access="public" output="FALSE" returntype="query" hint="get Rates">
		<cfargument name="Event" type="any" />
		<cfargument name="formatidList" type="string" required="true" />
		<cfargument name="memberid" type="numeric" required="false" />

		<cfset var local = structNew()>
		<cfset local.rfid = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="StoreProductRate", functionName="qualify")>

		<cfquery name="local.qryFormatRates" datasource="#application.dsn.memberCentral.dsn#">
			set nocount on;
			
			declare @FID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.rfid#">;
			declare @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteID')#" cfsqltype="cf_sql_integer">;

			<cfif isDefined("arguments.memberid") and arguments.memberid>
				declare @groupPrintID int;
				select @groupPrintID = groupPrintID from dbo.ams_members where memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberid#">
			</cfif>

			select distinct sr.rateID, sr.formatID, sr.rateName, sr.rate, sr.startDate,
				sr.endDate, sr.rateOrder, sr.siteResourceID, sr.GLAccountID, sr.reportCode
			from dbo.store_rates sr
			inner join dbo.cms_siteResources c on sr.siteResourceID = c.siteResourceID and c.siteID = @siteID
			inner join dbo.cms_siteResourceRights srr on c.siteResourceID = srr.resourceID and srr.siteID = @siteID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = c.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.store_ProductFormats spf on sr.formatID = spf.formatID
			<cfif isDefined("arguments.memberid") and arguments.memberid>
				inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
					and srfrp.siteresourceID = sr.siteResourceID
					and srfrp.functionID = @FID
				inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
					and gprp.rightPrintID = srfrp.rightPrintID
					and gprp.groupPrintID = @groupPrintID
			</cfif>
			where sr.formatID in (<cfqueryparam value="0#arguments.formatidList#" cfsqltype="cf_sql_integer" list="true">)
			order by sr.rate;
		</cfquery>

		<cfreturn local.qryFormatRates>
	</cffunction>
	
	<cffunction name="getProductRates" access="public" output="false" returntype="query" hint="get Product Rates">
		<cfargument name="Event" type="any" />
		<cfargument name="formatid" type="numeric" required="true" />
		<cfargument name="rateid" type="numeric" required="true" />
		<cfargument name="formatRates" type="query" required="true" />

		<cfset var qryProductRates = "" />

		<cfquery name="qryProductRates" dbtype="query">
			select rateID, formatID, rateName, rate, startDate, endDate, rateOrder,
				siteResourceID, GLAccountID, reportCode, rateoverride
			from [arguments].formatRates
			where formatID = <cfqueryparam value="#arguments.formatid#" cfsqltype="cf_sql_integer" />
			and rateID = <cfqueryparam value="#arguments.rateid#" cfsqltype="cf_sql_integer" />
			order by rate
		</cfquery>

		<cfreturn qryProductRates />
	</cffunction>
	
	<cffunction name="getBestRateOverride" access="public" output="false" returntype="query" hint="get Product Rates">
		<cfargument name="formatid" type="string" required="true" />

		<cfset var qryRateOverrides = "" />

		<cfquery name="qryRateOverrides" datasource="#application.dsn.memberCentral.dsn#">
			select r.rateID, r.formatID, r.rateName, r.rate, r.startDate, r.endDate,
				r.rateOrder, r.siteResourceID, r.GLAccountID, r.reportCode,
				dbo.fn_store_getBestRateOverride(r.rateid, r.formatid) as rateoverride
			from dbo.store_rates r
			where r.formatID in (<cfqueryparam value="#arguments.formatid#" cfsqltype="cf_sql_integer" list="true" />)
			order by r.rate
		</cfquery>

		<cfreturn qryRateOverrides>
	</cffunction>

	<cffunction name="getAvailableProductFormats" access="public" output="false" returntype="query">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="itemid" type="numeric" required="true">
		<cfargument name="memberid" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="StoreProductRate", functionName="Qualify")>
		
		<cfstoredproc procedure="store_getAvailableProductFormatsForCart" datasource="#application.dsn.memberCentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.storeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.itemid#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberid#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">
			<cfprocresult name="local.qryFormats" resultset="1">
		</cfstoredproc>
		
		<cfreturn local.qryFormats>
	</cffunction>	

	<cffunction name="getStreamInfo" access="private" output="false" returntype="struct">
		<cfargument name="orderDetailID" type="numeric" required="true">
		<cfargument name="orderDetailStreamID" type="numeric" required="true">
		<cfargument name="formatID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = false>
		
		<cfquery name="local.verifyStream" datasource="#application.dsn.memberCentral.dsn#">
			select pro.providerID, pro.providerCode, pr.profileName, pr.profileID, pu.streamName, ods.streamUsageUID, 
				ods.orderDetailStreamID, ods.logid, ods.dateExpires
			FROM dbo.store_orders as so
			INNER JOIN dbo.store on store.storeID = so.storeID
			INNER JOIN dbo.store_orderDetails AS sod on sod.orderID = so.orderID
				and sod.orderDetailID = <cfqueryparam value="#arguments.orderDetailID#" cfsqltype="cf_sql_integer">
				and sod.formatID = <cfqueryparam value="#arguments.formatID#" cfsqltype="cf_sql_integer">
			INNER JOIN dbo.store_ProductFormats AS spf ON spf.formatID = sod.formatID
			INNER JOIN dbo.store_orderDetailStreams ods on sod.orderDetailID = ods.orderDetailID 
				and ods.orderDetailStreamID = <cfqueryparam value="#arguments.orderDetailStreamID#" cfsqltype="cf_sql_integer">
			INNER JOIN dbo.store_ProductFormatsStreamUsages pfsu on pfsu.uid = ods.streamUsageUID
			INNER JOIN dbo.stream_profileUsages pu on pu.usageID = pfsu.usageID
			INNER JOIN dbo.stream_profiles pr on pr.profileID = pu.profileID and pr.isactive = 1
			INNER JOIN dbo.stream_providers pro on pro.providerID = pr.providerID and pro.isactive = 1
		</cfquery>
		
		<cfif local.verifyStream.recordCount>
			<cfset local.returnStruct.success = true>
			<cfset local.returnStruct.profileFields = structNew()>
			<cfset local.returnStruct.usageFields = structNew()>
			<cfset local.returnStruct.providerID = local.verifyStream.providerID>
			<cfset local.returnStruct.providerCode = local.verifyStream.providerCode>
			<cfset local.returnStruct.profileName = local.verifyStream.profileName>
			<cfset local.returnStruct.profileID = local.verifyStream.profileID>
			<cfset local.returnStruct.streamName = local.verifyStream.streamName>
			<cfset local.returnStruct.streamUsageUID = local.verifyStream.streamUsageUID>
			<cfset local.returnStruct.orderDetailStreamID = local.verifyStream.orderDetailStreamID>
			<cfset local.returnStruct.logid = local.verifyStream.logid>
			<cfset local.returnStruct.dateExpires = local.verifyStream.dateExpires>

			<cfquery name="local.getProfileFields" datasource="#application.dsn.memberCentral.dsn#">
				select f.fieldName, pf.value
				from dbo.stream_profiles p
				inner join dbo.stream_profileFields pf on pf.profileID = p.profileID
					and p.profileID = <cfqueryparam value="#local.verifyStream.profileID#" cfsqltype="cf_sql_integer">
				inner join dbo.stream_providerFields f on f.providerFieldID = pf.providerFieldID
				inner join dbo.stream_providerFieldLevels pfl on pfl.fieldLevelID = f.fieldLevelID and pfl.fieldLevel = 'provider'
			</cfquery>

			<cfquery name="local.getUsageFields" datasource="#application.dsn.memberCentral.dsn#">
				select f.fieldName, puf.value
				from dbo.stream_profileUsages pu
				inner join dbo.stream_profiles p on p.profileID = pu.profileID
					and p.profileID = <cfqueryparam value="#local.verifyStream.profileID#" cfsqltype="cf_sql_integer">
					and pu.usageID = <cfqueryparam value="#local.verifyStream.streamUsageUID#" cfsqltype="cf_sql_integer">
				inner join dbo.stream_profileUsageFields puf on puf.usageID = pu.usageID
				inner join dbo.stream_providerFields f on f.providerFieldID = puf.providerFieldID
				inner join dbo.stream_providerFieldLevels pfl on pfl.fieldLevelID = f.fieldLevelID and pfl.fieldLevel = 'usage'
			</cfquery>

			<cfloop query="local.getProfileFields">
				<cfset local.returnStruct.profileFields[local.getProfileFields.fieldname] = local.getProfileFields.value>
			</cfloop>
			<cfloop query="local.getUsageFields">
				<cfset local.returnStruct.usageFields[local.getUsageFields.fieldname] = local.getUsageFields.value>
			</cfloop>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="showAppInstanceForm" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="appInfo" type="query">

		<cfset var local = structNew()>

		<cfset local.appInfo = arguments.appInfo>
		<cfset local.allow = FALSE>
		<cfif local.appInfo.instancesCreated EQ 0>
			<cfset local.allow = TRUE>
		<cfelse>
			<cfif variables.isMultiInstanceReady AND (local.appInfo.maxInstancesPerSite - local.appInfo.instancesCreated) GT 0>
				<cfset local.allow = TRUE>
			</cfif>
		</cfif>

		<cfif local.allow>
			<cfscript>				
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			local.objSection = CreateObject("component","model.system.platform.section");
			local.objPageAdmin = CreateObject("component","model.admin.pages.pageAdmin");
			local.qryCommunities = local.objAppCreation.getCommunities(arguments.event.getValue('mc_siteInfo.siteID'));
			local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryModes = local.objPageAdmin.getAvailableModes();
			local.qryLanguages = local.objPageAdmin.getAvailableLanguages();
			if( local.appInfo.recordCount ) variables.allowPageNameChange = local.appInfo.allowPageNameChange;

			local.strProductRevenueGLAcctWidgetData = {
				label="Default Product Revenue Account",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=arguments.event.getValue('GLAccountID',0),
				pathFldValue= len(arguments.event.getValue('GLAccountPath','')) ? arguments.event.getValue('GLAccountPath') : "",
				pathNoneTxt="(No account selected)"
			};
			local.strProductRevenueGLAcctWidget = CreateObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strProductRevenueGLAcctWidgetData);

			</cfscript>
			
			<cfsavecontent variable="local.js">
				<cfoutput>
				<cfif structKeyExists(local, "strProductRevenueGLAcctWidget")>
					#local.strProductRevenueGLAcctWidget.js#
				</cfif>
				<script language="javascript">
					function doDeployToComm() {
						var thisForm = document.forms["frmCreateApp"];
						var deployValue = 0;
						for (var i=0; i < thisForm.deployToComm.length; i++){
							if (thisForm.deployToComm[i].checked) deployValue = thisForm.deployToComm[i].value;
						}
						if( deployValue == 1){
							document.getElementById('showCommunities').style.display = '';
							document.getElementById('showSections').style.display = 'none';
						} else {
							document.getElementById('showCommunities').style.display = 'none';
							document.getElementById('showSections').style.display = '';
						}					
					}		

					<!--- gl account selector functions --->
					function closeBox() { $.colorbox.close(); }
					function selectGLAccount(a) {
						$.colorbox( {onCleanup:closeBox, innerWidth:660, innerHeight:470, href:'#CreateObject("component","model.admin.admin").buildLinkToTool(toolType="GLAccountSelector",mca_ta="showSelector")#&mode=direct&selectFN=parent.selectGLAccountResult'+a+'&glatid=3', iframe:true, overlayClose:false} );
					}
					function selectGLAccountResultp(objGL) { selectGLAccountResult('p',objGL); }
					function selectGLAccountResults(objGL) { selectGLAccountResult('s',objGL); }
					function selectGLAccountResult(a,objGL) {
						if (objGL.thepathexpanded.length > 0) {
							$('##'+(a=='p'?'':'Shipping')+'GLAccountPath').html(objGL.thepathexpanded + '&nbsp; &nbsp; (<span class="tsAppBodyTextImportant">Remember to save!</span>)');
							$('##'+(a=='p'?'':'Shipping')+'GLAccountID').val(objGL.glaccountid);
							closeBox();
						} else { 
							var msg = '<div style="margin:10px;">
								<h4>Error selecting GL Account</h4>
								<div>There was a problem selecting the Default ' + (a=='p'?'Product':'Shipping') + ' Revenue Account.<br/>
								Try again; if the issue persists, contact MemberCentral for assistance.</div>
								</div>';
							$.colorbox( {onCleanup:closeBox, html:msg, overlayClose:false} );
						}
					}
					function setDuplicateMessage(boxEl, messageEl, iconEl, success, message){
						iconEl.toggleClass('fa-circle-check', success).toggleClass('fa-circle-exclamation', !success);
						messageEl.html(message);
						boxEl.toggleClass('text-green', success).toggleClass('text-danger', !success).removeClass('d-none');
					}
					function doesPageExist(pageName) {
						var boxEl = $('##pageBox');
						var messageEl = $('##pageText');
						var iconEl = $('##pageImg');
						var re = /[^a-zA-Z0-9\-_]/;
						mca_hideAlert('err_createapp');

						var existsResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true'){
								setDuplicateMessage(boxEl, messageEl, iconEl, !r.pageexists, r.pageexists ? 'Page Name already used!' : 'Passed!');
							} else {
								boxEl.addClass('d-none');
								mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
							}
						};

						if (pageName.length > 0) {
							if(re.test(pageName)){
								setDuplicateMessage(boxEl, messageEl, iconEl, false, 'Only letters, numbers, underscores, and dashses are allowed');
							}
							else {
								checkPageExists(pageName,existsResult);
							}
						}
						else {
							boxEl.addClass('d-none');
							return false;
						}
					}
					function checkPageExists(pageName,callback) {
						var objParams = { pageID:#val(arguments.event.getValue('pageID',0))#, pageName:pageName };
						TS_AJX('PAGE','pageExists',objParams,callback,callback,10000,callback);
					}
					function validatePageForm() {
						toggleFinishButton(false);
						mca_hideAlert('err_createapp');
						var thisForm = document.forms["frmCreateApp"];	
						var arrPromises = [];					
						var arrReq = new Array();

						if($('##pageName').length && $.trim($('##pageName').val()).length == 0 && $('##pageName').is(':visible')) {
							arrReq[arrReq.length]	= 'Enter a valid name for this page.';
						}
						if($('##pageTitle').length && $.trim($('##pageTitle').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a page title.';
						}
						if($('##pageDesc').length && $.trim($('##pageDesc').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a page description.';
						}
						if($('##appInstanceName').length && $.trim($('##appInstanceName').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a name for this #local.appInfo.applicationTypeDesc#.';
						}
						if($('##appInstanceDesc').length && $.trim($('##appInstanceDesc').val()).length == 0) {
							arrReq[arrReq.length]	= 'Enter a description for this #local.appInfo.applicationTypeDesc#.';
						}
						<cfif variables.isCommunityReady>
							if($('input[name=deployToComm]').length && $.trim($('input[name=deployToComm]:checked').val()) == 1 && $('##commSRID').length  && $.trim($('##commSRID').val()) == 0) {
								arrReq[arrReq.length]	= 'You must select an e-Community.';
							}
						</cfif>

						var GLAccountRegEx = new RegExp("[0-9]*\.?[0-9]*[1-9]", "gi");

						if($('##GLAccountID').length && $.trim($('##GLAccountID').val()).length == 0) {
							arrReq[arrReq.length]	= 'Select a GL Account';
						}else if(!(GLAccountRegEx.test($.trim($('##GLAccountID').val())))){
							arrReq[arrReq.length]	= 'Select a GL Account';
						}
							
						if (arrReq.length > 0) {							
							mca_showAlert('err_createapp', arrReq.join('<br/>'), true);
							toggleFinishButton(true);
							return false;
						}
						<cfif variables.allowPageNameChange>
						arrPromises.push(
							new Promise(function(resolve, reject) {
								var checkPageNameResult = function(r) {
									if (r.success && r.success.toLowerCase() == 'true'){
										if(r.pageexists == true) {
											setDuplicateMessage($('##pageBox'), $('##pageText'), $('##pageImg'), false, 'Page Name already used!');
											mca_showAlert('err_createapp', 'Page Name already used.', true);
											toggleFinishButton(true);
											reject();
										}
										else resolve();
									} else {
										mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
										toggleFinishButton(true);
										reject();
									}
								};
								if($.trim($('##pageName').val()).length) {
									checkPageExists($.trim($('##pageName').val()),checkPageNameResult);
								}
								else resolve();
							})
						);	
						</cfif>					
						Promise.all(arrPromises).then(function(){
							thisForm.submit();
						}).catch((error) => {
							return false;
						});			
						return false;
					}						
				</script>			
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
			
			<cfoutput>
			<div class="mt-4">
				<div id="err_createapp" class="alert alert-danger mb-4 mt-2 d-none"></div>

				<cfform action="/?#cgi.QUERY_STRING#" method="POST" name="frmCreateApp" id="frmCreateApp" onsubmit="return validatePageForm();">
					<cfinput type="hidden" name="lid"  id="lid" value="#arguments.event.getValue('lid')#">
					<cfinput type="hidden" name="pageTemplateID"  id="pageTemplateID" value="#arguments.event.getValue('pageTemplateID')#">
					<cfinput type="hidden" name="allowReturnAfterLogin"  id="allowReturnAfterLogin" value="#arguments.event.getValue('allowReturnAfterLogin')#">

					<cfif len(trim(arguments.event.getValue('error.errorMessage')))>
						<div class="alert alert-danger mb-2">
							Correct the following errors:<br/>
							<cfloop list="#arguments.event.getValue('error.errorMessage')#" delimiters="|" index="local.currentMessage">
								- #local.currentMessage#<br />
							</cfloop>
						</div>
					</cfif>	

					<div>
						<div class="form-group">
							<div class="form-label-group">
								<cfif variables.allowPageNameChange>
									<input type="text" name="pageName"  id="pageName" class="form-control" value="#arguments.event.getValue('pageName')#" onblur="doesPageExist(this.value);" maxlength="50">
								<cfelse>
									<input type="text" name="pageName" id="pageName" value="#local.appInfo.suggestedPageName#" class="form-control" readOnly>
								</cfif>						
								<label for="pageName">Page Name*:</label>
							</div>
							<cfif variables.allowPageNameChange>
								<div id="pageBox" class="form-text small mb-2 d-none">
									<i class="fa-solid" id="pageImg"></i> <span id="pageText"></span>
								</div>
							</cfif>
						</div>

						<div id="showSections">
							<div class="form-group">
								<div class="form-label-group">
									<select name="sectionID" id="sectionID" class="custom-select">
										<cfloop query="local.getSections">
											<option value="#local.getSections.sectionID#"<cfif arguments.event.getValue('sectionID') EQ local.getSections.sectionID> SELECTED</cfif>>#local.getSections.thePathExpanded#</option>
										</cfloop>
									</select>
									<label for="sectionID">Section:</label>
								</div>
							</div>							
						</div>

						<div class="form-group mb-4">
							<div class="form-label-group">
								<select name="pageModeID" id="pageModeID" class="custom-select">
									<option value="0">No Override</option>
									<cfloop query="local.qryModes">
										<option value="#local.qryModes.modeID#"<cfif arguments.event.getValue('pageModeID') EQ local.qryModes.modeID> SELECTED</cfif>>#local.qryModes.modeName#</option>
									</cfloop>
								</select>
								<label for="pageModeID">Mode Override:</label>
							</div>
						</div>

						<div class="form-group">
							<div class="form-label-group">
								<input type="text" class="form-control" name="pageTitle"  id="pageTitle" value="#arguments.event.getValue('pageTitle')#">
								<label for="pageTitle">Page Title*:</label>
							</div>
						</div>
				
						<div class="form-group mb-4">
							<div class="form-label-group">
								<textarea name="pageDesc" id="pageDesc" class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('pageDesc')#</textarea>
								<label for="pageDesc">Page Description*:</label>
							</div>
						</div>
				
						<div class="form-group">
							<div class="form-label-group">
								<input type="text" class="form-control" name="appInstanceName"  id="appInstanceName"  value="#arguments.event.getValue('appInstanceName')#">
								<label for="appInstanceName" >#local.appInfo.applicationTypeDesc# Name*:</label>
							</div>
						</div>
						
						<div class="form-group mb-4">
							<div class="form-label-group">
								<textarea name="appInstanceDesc" id="appInstanceDesc"  class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('appInstanceDesc')#</textarea>
								<label for="appInstanceDesc">#local.appInfo.applicationTypeDesc# Description*:</label>
							</div>
						</div>

						<cfif variables.isCommunityReady>
							<div class="form-group row">
								<label for="disabled" class="col-sm-7 col-form-label-sm font-size-md">Will this application be used within an e-community?:</label>
								<div class="col-sm mt-1">				
										<div class="form-check-inline">
										<cfinput class="form-check-input" type="radio" name="deployToComm"  id="deployToCommYes" value="1" onClick="doDeployToComm();" checked="#iif(arguments.event.getValue('deployToComm', 0) EQ 1,de('1'),de('0'))#">
										<label class="form-check-label" for="inlineCheckbox1">Yes </label>
									</div>
									<div class="form-check-inline">
										<cfinput  class="form-check-input" type="radio" name="deployToComm"  id="deployToCommNo" value="0" onClick="doDeployToComm();" checked="#iif(arguments.event.getValue('deployToComm', 0) NEQ 1,de('1'),de('0'))#">
										<label class="form-check-label" for="inlineCheckbox2">No</label>
									</div>		
								</div>
							</div>
				
							<div class="form-group" id="showCommunities">
								<div class="form-label-group">
									<select name="commSRID"  id="commSRID" class="custom-select">
										<option value="0">Choose an e-Community</option>
										<cfloop query="local.qryCommunities">
											<option value="#local.qryCommunities.siteResourceID#"<cfif arguments.event.getValue('commSRID') EQ local.qryCommunities.siteResourceID> SELECTED</cfif>>#local.qryCommunities.communityName#</option>
										</cfloop>
									</select>
									<label for="commSRID">E-Community*:</label>
								</div>
							</div>				
						</cfif>

						<div class="form-row">
							<div class="col-sm-12">
								#local.strProductRevenueGLAcctWidget.html#
							</div>
						</div>

						<div class="form-group text-right">
							<button type="submit" name="btnSaveEventDetails" id="btnSaveEventDetails" class="btn btn-sm btn-primary d-none">Save Information</button>
						</div>
					</div>
				</cfform>	
			</div>			
			
			<cfif variables.isCommunityReady>
				<script>doDeployToComm();</script>
			</cfif>
			</cfoutput>
		<cfelse>
			<cfoutput>
			<div class="alert alert-warning">
				<h4>Unable to add #local.appInfo.applicationTypeName#</h4>
				<p>You may not add this application to your website at this time.</p>
			</div>	
			</cfoutput>
		</cfif>	
	</cffunction>

	<cffunction name="getCreditInfoForFormat" access="public" returntype="struct" output="no">
		<cfargument name="formatID" type="numeric" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.retStruct = StructNew()>
		<cfset local.qryProductCredits = CreateObject("component","model.admin.credit.credit").getCreditOfferedGrid("store",arguments.formatID)>

		<cfquery name="local.qryProductCreditsApproved" dbtype="query">
			select AuthorityName, offeredCreditTypes, statementAppProvider, creditMessage
			from [local].qryProductCredits	
			where status = 'Approved'
			order by AuthorityName
		</cfquery>
		<cfset local.retStruct.creditCount = local.qryProductCreditsApproved.recordcount>

		<cfsavecontent variable="local.retStruct.detail">
			<cfoutput query="local.qryProductCreditsApproved">
				<cfset local.arrTypes = xmlSearch(local.qryProductCreditsApproved.offeredCreditTypes,"/creditTypes/ect")>
				<b><u>#local.qryProductCreditsApproved.AuthorityName#</u></b><br/><br/>
				Credit has been approved with the #local.qryProductCreditsApproved.authorityName# for 
				<cfloop from="1" to="#arrayLen(local.arrTypes)#" index="local.thisTypeNum">
					<b>#local.arrTypes[local.thisTypeNum].xmlAttributes.creditValue# #local.arrTypes[local.thisTypeNum].xmlAttributes.creditType# credit<cfif local.arrTypes[local.thisTypeNum].xmlAttributes.creditValue is not 1>s</cfif></b><cfif local.thisTypeNum is arrayLen(local.arrTypes)>. <cfelse><cfif arrayLen(local.arrTypes) is 2> and <cfelse><cfif local.thisTypeNum is arrayLen(local.arrTypes) - 1>, and <cfelse>, </cfif></cfif></cfif>
				</cfloop>
				#local.qryProductCreditsApproved.statementAppProvider#
				<cfif len(local.qryProductCreditsApproved.creditMessage)><br/><br/>#local.qryProductCreditsApproved.creditMessage#</cfif>
				<br/><br/>
			</cfoutput>		
		</cfsavecontent>
		
		<cfreturn local.retStruct>
	</cffunction>

</cfcomponent>
