<cfoutput>
#showHeader(bucketName=local.qryBucketInfo.bucketName, viewDirectory=arguments.viewDirectory)#
<cfif arrayLen(local.arrExpert) EQ 0>
	<div class="s_rnfne">#showCommonNotFound(bucketID=arguments.bucketID,searchID=arguments.searchID,bucketName=local.qryBucketInfo.bucketName,viewDirectory=arguments.viewDirectory)#</div>
<cfelse>
	#showSearchResultsPaging(topOrBottom='top', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.totalChallengeCount, itemWord='challenge', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#

	<!--- results table --->
	<cfloop array="#local.strExpertResults.arrexperts#" item="local.thisExpert" index="local.index">
		<div id="row#local.thisExpert.id#" class="tsAppBodyText bk_expertcard bk-card-box bk-shadow-none bk-p-3 bk-mt-1 bk-mb-3 bk-w-sm-100 bk-border-1 bk-border-gray" style="box-sizing:border-box;">
			<div id="row#local.thisExpert.id#summary" class="bk_expertsummary">
				<div class="bk-d-flex">
					<strong>#local.thisExpert.name#</strong><cfif len(local.thisExpert.location)>, #local.thisExpert.location#</cfif>
					<cfif arrayLen(local.strExpertResults.arrexperts) gt 1><button class="exptogglebtn tsAppBodyButton bk-ml-auto" onClick="toggleDCSExpert(#local.thisExpert.id#);">View <i class="icon-chevron-right"></i></button></cfif>
				</div>
				<div class="bk-font-size-md">
					<cfif arrayLen(local.thisExpert.arrdisciplines)>
						#arrayToList(local.thisExpert.arrdisciplines,"<br/>")#
					</cfif>
					<div class="bk-mt-2"><strong>#local.thisExpert.totalline#</strong></div>
				</div>
			</div>

			<div id="row#local.thisExpert.id#detail" class="bk_expertdetail bk-mt-4" <cfif arrayLen(local.strExpertResults.arrexperts) gt 1>style="display:none;"</cfif>>
				<div class="bk-d-flex" style="gap:10px;">
					<cfloop array="#local.arrReportTypes#" item="local.thisReportType">
						<div class="bk-card-box bk-col <cfif len(local.thisReportType.badge)>bk-price-card-selected<cfelse>bk-border-1</cfif>">
							<div class="bk-card-body bk-font-size-md">
								<cfif len(local.thisReportType.badge)>
									<div class="bk-price-badge">#local.thisReportType.badge#</div>
								</cfif>
								<div class="bk-d-flex">
									<strong>#local.thisReportType.name#</strong>
									<strong class="bk-ml-auto bk-font-size-lg">#local.thisReportType.cost#</strong>
								</div>
								<div class="bk-mt-2 bk-mb-2">#local.thisReportType.desc#</div>
							</div>
							<div class="bk-mt-auto bk-pb-2">
								<cfif local.thisReportType.keyExists("sampbtntxt")>
									<div class="bk-pl-3 bk-text-left" style="float:left;">
										<button class="tsAppBodyButton" onclick="viewDCSSampleReport()">#local.thisReportType.sampbtntxt#</button>
									</div>
								</cfif>
								<div class="bk-pr-3 bk-text-right">
									<button class="tsAppBodyButton" onclick="orderDCSReport('#local.thisReportType.btnact#')">#local.thisReportType.btntxt#</button>
								</div>
							</div>
						</div>
					</cfloop>
				</div>
				<div class="bk-mt-3 bk-font-size-xs bk-text-center bk-text-dim">Complete satisfaction guarantee with your first order or full refund.</div>
				<cfif local.thisExpert.numchallenges+local.thisExpert.numcases gt 0>
					<div class="bk-mt-5 bk-mb-3 bk-font-size-md bk-text-center bk-border-bottom bk-pb-2">Our last analysis of #local.thisExpert.name#</div>
					<div class="bk-d-flex" style="overflow-y:auto;">
						<cfif local.thisExpert.numchallenges gt 0>
							<div class="bk-col" id="mc-ExpertGroundsOfChallengeChart-#local.thisExpert.id#"></div>
						</cfif>
						<cfif local.thisExpert.numcases gt 0>
							<div class="bk-col" id="mc-ExpertStatewiseCasesChart-#local.thisExpert.id#"></div>
						</cfif>
					</div>
				</cfif>
			</div>
		</div>
	</cfloop>
	<div id="bk_tooltip"></div>

	#showSearchResultsPaging(topOrBottom='bottom', searchID=arguments.searchID, startRow=arguments.startrow, numTotalPages=local.NumTotalPages, NumCurrentPage=local.NumCurrentPage, itemCount=local.totalChallengeCount, itemWord='challenge', itemWordSuffix='s', viewDirectory=arguments.viewDirectory)#
</cfif>
</cfoutput>