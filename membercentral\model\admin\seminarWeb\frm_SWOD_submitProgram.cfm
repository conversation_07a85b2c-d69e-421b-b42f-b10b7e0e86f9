<cfset local.downloadableFilesExtList = "mp3,mp4,pdf,doc,docx,ppt,pptx,xls,xlsx,rtf,txt,zip">
<cfset local.customEvalFilesExtList = "doc,docx,xls,xlsx,rtf,txt,pdf,csv">
<cfset local.customExamFilesExtList = "doc,docx,xls,xlsx,rtf,txt,pdf,csv">
<cfset local.ftdImageFilesExtList = "jpg,jpeg,png,gif">

<cfsavecontent variable="local.submitSWODJS">
	<cfoutput>
	<link href="/assets/common/javascript/jQueryAddons/plupload/3.1.2/jquery.plupload.queue/css/jquery.plupload.queue.css" rel="stylesheet" type="text/css" />
 	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/plupload/3.1.2/plupload.full.min.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/plupload/3.1.2/jquery.plupload.queue/jquery.plupload.queue.min.js"></script>
	<script type="text/javascript">
		var submissionID = #local.submissionID#;
		var sf_seminarID = 0;
		var sf_readonly = #BooleanFormat(local.readOnly)#;
		var sf_customEvalUploader;
		var sf_customEvalUploaderHasFile = false;
		var sf_customExamUploader;
		var sf_customExamUploaderHasFile = false;
		var sf_programMaterialsUploader;
		var sf_programMaterialsUploaderHasFile = false;
		var sf_featuredImageUploader;
		var sf_featuredImageUploaderHasFile = false;
		var sf_arrErrMsg = [];

		var sf_maxObjectiveID = 0;
		var sf_maxLinkID = 0;
		var sf_maxRateID = 0;
		var sf_maxSyndPriceID = 0;
		var sf_maxCreditID = 0;

		var #ToScript(local.manageCopyRatesLink,'link_manageCopyRates')#;
		var sw_seminarid = sf_seminarID;
		var sw_itemtype = 'SWOD';

		function sf_validateSubmitSWODStepChange(event, currentIndex, newIndex) {
			var currentStep = currentIndex + 1;
			mca_hideAlert('err_submitprogram');
			var arrReq = [];

			/* Always allow step back to the previous step even if the current step is not valid! */
			if (currentIndex > newIndex) {
				return true;
			}

			switch(currentStep) {
				case 1:
					if($('##sf_seminarName').val().trim().length == 0)
						arrReq[arrReq.length] = 'Enter the seminar name.';
					
					var seminarDesc = "";
					if(CKEDITOR.instances['sf_seminarDescEditor'] != null)
						seminarDesc = CKEDITOR.instances['sf_seminarDescEditor'].getData().trim();
					else seminarDesc = $('textarea[name="sf_seminarDescEditor"]').val().trim();

					if(seminarDesc.length == 0)
						arrReq[arrReq.length] = 'Enter the seminar description.';
					if($('##sf_dateOrigPublished').val().trim().length == 0)
						arrReq[arrReq.length] = 'Enter the originally published date.';
					var seminarLength = $('##sf_seminarLength').val();
					if(seminarLength == '' || !mca_validateInteger(seminarLength))
						arrReq[arrReq.length] = 'Enter a valid estimated time to complete this seminar.';
					if($('##sf_addLearningObjectives').is(':checked')){
						var arrObjectives = [];
						$("textarea.sf_learnObjective").each(function(){
							if($(this).val().length) arrObjectives.push($(this).val());
						});
						if(arrObjectives.length == 0)
							arrReq[arrReq.length] = 'Enter at least one learning objective or toggle this option off.';
						else if($("textarea.sf_learnObjective").length != arrObjectives.length)
							arrReq[arrReq.length] = 'Enter at least one learning objective or toggle this option off.';
					}
					break;
				case 3:
					if($('##sf_offerEval').is(':checked')){
						var selectedCustomEvalCount = $('tbody.sf_tbodyEvaluations tr :checkbox:checked').length;
						<cfif local.submissionID gt 0>
							if(selectedCustomEvalCount == 0 && $('div##sf_uploadedFiles_customEval .downloadLink').length == 0)
								arrReq[arrReq.length] = 'No custom evaluations are selected for this seminar.';
						<cfelse>
							if(selectedCustomEvalCount == 0 && !sf_customEvalUploaderHasFile)
								arrReq[arrReq.length] = 'No custom evaluations are selected for this seminar.';
						</cfif>
					}
					break;
				case 5:
					if($('##sf_allowCatalog').is(':checked')){
						if($('##sf_dateCatalogStart').val().trim().length == 0)
							arrReq[arrReq.length] = 'Enter the starting catalog availability date.';
						if($('##sf_dateCatalogEnd').val().trim().length == 0)
							arrReq[arrReq.length] = 'Enter the ending catalog availability date.';
						if($('tbody.sf_tbodyProgramRates tr').length == 0)
							arrReq[arrReq.length] = 'Enter rates for your program by clicking "Add Rate".';
					}
					var xmlsynd = '<syndication>';
					if($('##sf_allowSyndication').is(':checked') && !$('##sf_createOwnPricingToOptIns').is(':checked')){
						$('input[name="sf_syndPriceID"]').each(function (index) {
							var id = $(this).val();
							if($('##sf_syndGroup_' + id) && $('##sf_syndGroup_' + id).val().trim() != '' && $('##sf_syndPrice_' + id).val().trim() == ''){
								arrReq[arrReq.length] = 'Enter valid syndication prices.';
								return false;
							} else if ($('##sf_syndGroup_' + id) && $('##sf_syndGroup_' + id).val().trim() == '' && formatCurrency($('##sf_syndPrice_' + id).val()) == '0.00') {
								$('##sf_syndPrice_' + id).val('');
							} else if ($('##sf_syndGroup_' + id) && $('##sf_syndGroup_' + id).val().trim() != '' && $('##sf_syndPrice_' + id).val().trim() != '') {
								xmlsynd = xmlsynd + '<pricegroup><group>' + $('##sf_syndGroup_' + id).val().trim() + '</group><price>' + formatCurrency($('##sf_syndPrice_' + id).val()).replace(/,/g,'') + '</price></pricegroup>';
							}
						});
					}
					xmlsynd = xmlsynd + '</syndication>';
					$('##sf_syndXML').val(xmlsynd);
					break;
				case 6:
					if(!$('##sf_editCECreditFormContainer').hasClass('d-none')){
						let creditAction = $('##sf_CECredit_actionMode').val();
						arrReq[arrReq.length] = 'Your credit information is not saved. Click the "'+ (creditAction == 'Add' ? 'Add' : 'Save') +'" button before proceeding to Step 7.';
					}
					break;
			}
			
			if(arrReq.length) {
				mca_showAlert('err_submitprogram', arrReq.join('<br/>'));
				return false;
			}
			else return true;
		}
		function sf_saveSubmitSWODProgram(event, currentIndex) {
			$('##submitSWODProgramWizard ul[aria-label=Pagination] li a[href="##finish"],##submitSWODProgramWizard ul[aria-label=Pagination] li a[href="##previous"]').remove();
			$('##submitSWODProgramWizard h3.current').hide();

			var seminarDesc = "";
			if(CKEDITOR.instances['sf_seminarDescEditor'] != null)
				seminarDesc = CKEDITOR.instances['sf_seminarDescEditor'].getData().trim();
			else seminarDesc = $('textarea[name="sf_seminarDescEditor"]').val().trim();
			$('##sf_seminarDesc').val(seminarDesc);;
			
			$('##divFinalNotesSection').addClass('d-none');
			$('##divSubmitProgramLoading').removeClass('d-none');
			var fd = $('##frmSubmitSWODProgram').serializeArray();
			$("##divSubmitProgramFormArea").load('#local.formLink#', fd, function(r){
				let response = JSON.parse(r);
				if(response.success && response.success == true) {
					submissionID = response.submissionid;
					<cfif local.submissionID gt 0>
						sf_seminarID = response.seminarid;
						sf_saveSubmitSWODProgram_continue('convertToOnDemand');
						doFilterSWODSubmissions();
					<cfelse>
						sf_saveSubmitSWODProgram_continue('saveFormData');
					</cfif>
				}
				else {
					var errMsg = '<div class="alert alert-danger">An error occured while submitting the form data.'+ (response && response.errmsg ? '<br/>'+response.errmsg : '') +'</div>';
					$('.submitSWODProgramFormContainer').html(errMsg);
				}
			});
			return false;
		}
		function sf_saveSubmitSWODProgram_continue(currentAction) {
			var nextAction = '';
			switch(currentAction) {
				case 'saveFormData': nextAction = 'saveCustomEvalFile'; break;
				case 'saveCustomEvalFile': nextAction = 'saveCustomExamFile'; break;
				case 'saveCustomExamFile': nextAction = 'saveProgramMaterialFiles'; break;
				case 'saveProgramMaterialFiles': nextAction = 'saveFeaturedImage'; break;
				case 'saveFeaturedImage': nextAction = 'final'; break;
				case 'convertToOnDemand': nextAction = 'final'; break;
			}

			switch(nextAction) {
				case 'saveCustomEvalFile':
					if($('##sf_offerEval').is(':checked') && sf_customEvalUploaderHasFile) sf_customEvalUploader.start();
					else sf_saveSubmitSWODProgram_continue(nextAction);
					break;
				case 'saveCustomExamFile':
					if($('##sf_offerExam').is(':checked') && sf_customExamUploaderHasFile) sf_customExamUploader.start();
					else sf_saveSubmitSWODProgram_continue(nextAction);
					break;
				case 'saveProgramMaterialFiles':
					if(sf_programMaterialsUploaderHasFile) sf_programMaterialsUploader.start();
					else sf_saveSubmitSWODProgram_continue(nextAction);
					break;
				case 'saveFeaturedImage':
					if($('##sf_addNewProgramImg').is(':checked') && sf_featuredImageUploaderHasFile) sf_featuredImageUploader.start();
					else sf_saveSubmitSWODProgram_continue(nextAction);
					break;
				case 'final':
					var htmlString = '';
					var errMsgs = '';
					if(sf_arrErrMsg.length){
						errMsgs = '<div class="text-danger mt-2">But the following issues happened while saving data:<br/>' + sf_arrErrMsg.join('<br/>') + '</div>';
					}
					<cfif local.submissionID gt 0>
						htmlString = '<div class="alert alert-success"><div>The submission has been converted to an OnDemand Program successfully!</div>';
						htmlString += '<a href="'+mca_getSiteToolLink('#local.participantOrgCode#','SeminarWebAdmin','listSWOD','editSWODProgram','pid='+sf_seminarID)+'" target="_swodPrg'+sf_seminarID+'" class="btn btn-link px-0"><i class="fa-solid fa-up-right-from-square"></i> View Seminar</a>';
						htmlString += errMsgs + '</div>';
					<cfelse>
						htmlString = $('##sf_alertsubmitsuccess').html();
						htmlString = htmlString.replace('{{errormessages}}', errMsgs);
					</cfif>
					$('.submitSWODProgramFormContainer').html(htmlString);
					break;
			}

			return false;
		}
		function renderHTMLFromTemplate(templateSourceElementID, obj){
			var templateSource = $('##'+templateSourceElementID).html();
			var template = Handlebars.compile(templateSource);
			return template(obj);
		}
		function sf_resetGridRowNumbering(tbodyClass){
			$('tbody.'+tbodyClass+' tr td.indexVal').each(function(index) {
				$(this).html(index+1);
			});
			var rowCount = $('tbody.'+tbodyClass+' tr').length;
			$('tbody.'+tbodyClass+'_noRecords').toggleClass('d-none', rowCount != 0);
			if(sf_readonly) $('tbody.'+tbodyClass+' tr .sf-grid-action').addClass('d-none');
		}
		function sf_getMaxAutoID(arrdata){
			return Math.max(...arrdata.map(o => o.autoid), 0) || 0
		}
		<!--- program objectives --->
		function sf_getProgramObjectives() {
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if(r.arrobjectives.length) $('##sf_addLearningObjectives').prop('checked', true).trigger('change');
					var renderedHTML = renderHTMLFromTemplate('sf_programObjective_rows',  { arrobjectives: r.arrobjectives});
					$('tbody.sf_tbodyLearningObjectives').html(renderedHTML);
					sf_resetGridRowNumbering('sf_tbodyLearningObjectives');
					sf_maxObjectiveID = sf_getMaxAutoID(r.arrobjectives);
				}
				else alert('We were unable to load the Objectives.');
				if(!sf_readonly) $('##sf_btnAddObjective').prop('disabled',false);
			};
			
			$('##sf_btnAddObjective').prop('disabled',true);
			$('tbody.sf_tbodyLearningObjectives').html('<tr><td class="p-2" colspan="4">'+mca_getLoadingHTML('Loading..')+'</td></tr>');
			var objParams = { submissionID:submissionID };
			TS_AJX('ADMINSWOD','getSubmissionObjectives',objParams,getResult,getResult,20000,getResult);
		}
		function sf_addProgramObjective() {
			var obj = { arrobjectives:[{ autoid: ++sf_maxObjectiveID, objective:'' }]};
			var renderedHTML = renderHTMLFromTemplate('sf_programObjective_rows',obj);
			$('tbody.sf_tbodyLearningObjectives').append(renderedHTML);
			sf_resetGridRowNumbering('sf_tbodyLearningObjectives');
		}
		function sf_removeObjective(id){
			$('tbody.sf_tbodyLearningObjectives tr##sf_trObj_'+id).remove();
			sf_resetGridRowNumbering('sf_tbodyLearningObjectives');
		}
		<!--- program speakers/authors --->
		function sf_getSWAuthors(authorType) {
			var tbodyClass = (authorType == 'Moderator' ? 'sf_tbodyModerators' : 'sf_tbodySpeakers');
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					var renderedHTML = renderHTMLFromTemplate((authorType == 'Moderator' ? 'sf_moderator_rows' : 'sf_speaker_rows'), { arrauthors: r.arrauthors});
					$('tbody.' + tbodyClass).html(renderedHTML);
					sf_resetGridRowNumbering(tbodyClass);
				}
				else alert('We were unable to load the '+ authorType +'s.');
			};
			
			$('tbody.' + tbodyClass).html('<tr><td class="p-2" colspan="4">'+mca_getLoadingHTML('Loading..')+'</td></tr>');
			var objParams = { submissionID:submissionID, authorType:authorType };
			TS_AJX('ADMINSWOD','getSubmissionAuthors',objParams,getResult,getResult,20000,getResult);
		}
		function sf_addSWAuthor(authorType) {
			var fieldName = (authorType == 'Moderator' ? 'sf_moderator_authorID' : 'sf_speaker_authorID' );
			var arrAuthorID = $('input[name="' + fieldName + '"').map(function(){return $(this).val();}).get();
			var result = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					var optionsHTML = '<option value="">Choose '+ authorType +'</option><optgroup label="Create a New '+ authorType +'"><option value="0">New '+ authorType +'</option></optgroup>';
					if (r.arrorgauthors.length) {
						optionsHTML += '<optgroup label="Previously Saved '+ authorType +'s">';
						for (var i=0; i<r.arrorgauthors.length; i++) {
							if($.inArray(r.arrorgauthors[i].authorid.toString(), arrAuthorID) < 0){
								optionsHTML += '<option value="'+r.arrorgauthors[i].authorid+'">'+r.arrorgauthors[i].authorname+'</option>';
							}
						}
						optionsHTML += '</optgroup>';
					}
					$('##' + (authorType == 'Moderator' ?'sf_SWModerator' : 'sf_SWSpeaker')).html(optionsHTML);
				} else {
					alert('An error occured while loading '+ authorType +'s.');
				}
			};

			MCModalUtils.showModal({
				isslideout: true,
				size: 'md',
				title: 'Choose ' + authorType + 's for this Program',
				strmodalbody: {
					content: $('##' + (authorType == 'Moderator' ?'sf_moderator_add' : 'sf_speaker_add')).html(),
				},
				strmodalfooter : {
					classlist: 'd-flex',
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttonlabel: 'Continue',
					extrabuttononclickhandler: (authorType == 'Moderator' ? 'sf_doAddSWModerator' : 'sf_doAddSWSpeaker')
				}
			});

			var objParams = { orgcode:'#local.participantOrgCode#', programID:0, programType:'SWOD', authorType:authorType };
			TS_AJX('ADMINSWAUTHOR','getAvailableSWAuthorsToAdd',objParams,result,result,10000,result);
		}
		<!--- speakers --->
		function sf_doAddSWSpeaker() {
			mca_hideAlert('err_addswspeaker');

			var authorID = $('##sf_SWSpeaker').val();
			if (authorID == "") {
				mca_showAlert('err_addswspeaker', 'Select a Speaker.');
				return false;
			} else if (authorID == 0) {
				MCModalUtils.hideModal();
				$('##MCModal').on('hidden.bs.modal', function() { sf_editSpeaker(0); });
			} else {
				var authorObj = {
					action:'insert',
					authorid:authorID,
					authorname:$( "##sf_SWSpeaker option:selected" ).text()
				};
				sf_onSaveSpeakerSuccess(authorObj);
			}
		}
		function sf_removeSpeaker(id){
			$('tbody.sf_tbodySpeakers tr##trAuthor_'+id).remove();
			sf_resetGridRowNumbering('sf_tbodySpeakers');
		}
		function sf_editSpeaker(aid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: { backdrop: 'static', keyboard: false },
				size: 'lg',
				title: (aid > 0 ? 'Edit ' : 'Add ') + 'Speaker',
				iframe: true,
				contenturl: '#local.editAuthorLink#&swtype=SWTL&aid=' + aid + '&onSaveRetFunction=sf_onSaveSpeakerSuccess&overrideEditAuthorFn=sf_editSpeaker',
				strmodalfooter: {
					classlist: 'd-flex',
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmAuthorDetails :submit").click',
					extrabuttonlabel: 'Save',
				}
			});
		}
		function sf_onSaveSpeakerSuccess(obj){
			if(obj.action == 'insert') {
				var obj = { arrauthors:[{ authorid:obj.authorid, authorname:obj.authorname }]};
				var renderedHTML = renderHTMLFromTemplate('sf_speaker_rows',obj);
				$('tbody.sf_tbodySpeakers').append(renderedHTML);
				sf_resetGridRowNumbering('sf_tbodySpeakers');
			}
			else if(obj.action == 'update') {
				$('tbody.sf_tbodySpeakers tr##trAuthor_'+obj.authorid+' td.authorname').html(obj.authorname);
			}
			MCModalUtils.hideModal();
		}
		<!--- moderators --->
		function sf_doAddSWModerator() {
			mca_hideAlert('err_addswmoderator');

			var authorID = $('##sf_SWModerator').val();
			if (authorID == "") {
				mca_showAlert('err_addswmoderator', 'Select a Moderator.');
				return false;
			} else if (authorID == 0) {
				MCModalUtils.hideModal();
				$('##MCModal').on('hidden.bs.modal', function() { sf_editModerator(0); });
			} else {
				var authorObj = {
					action:'insert',
					authorid:authorID,
					authorname:$( "##sf_SWModerator option:selected" ).text()
				};
				sf_onSaveModeratorSuccess(authorObj);
			}
		}
		function sf_removeModerator(id){
			$('tbody.sf_tbodyModerators tr##trAuthor_'+id).remove();
			sf_resetGridRowNumbering('sf_tbodyModerators');
		}
		function sf_editModerator(aid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: { backdrop: 'static', keyboard: false },
				size: 'lg',
				title: (aid > 0 ? 'Edit ' : 'Add ') + 'Moderator',
				iframe: true,
				contenturl: '#local.editAuthorLink#&swtype=SWOD&aid=' + aid + '&onSaveRetFunction=sf_onSaveModeratorSuccess&overrideEditAuthorFn=sf_editModerator&atype=Moderator',
				strmodalfooter: {
					classlist: 'd-flex',
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmAuthorDetails :submit").click',
					extrabuttonlabel: 'Save',
				}
			});
		}
		function sf_onSaveModeratorSuccess(obj){
			if(obj.action == 'insert') {
				var obj = { arrauthors:[{ authorid:obj.authorid, authorname:obj.authorname }]};
				var renderedHTML = renderHTMLFromTemplate('sf_moderator_rows',obj);
				$('tbody.sf_tbodyModerators').append(renderedHTML);
				sf_resetGridRowNumbering('sf_tbodyModerators');
			}
			else if(obj.action == 'update') {
				$('tbody.sf_tbodyModerators tr##trAuthor_'+obj.authorid+' td.authorname').html(obj.authorname);
			}
			MCModalUtils.hideModal();
		}
		<!--- program links --->
		function sf_getExternalLinks() {
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if(r.arrlinks.length) $('##sf_includeLinks').prop('checked', true).trigger('change');
					var renderedHTML = renderHTMLFromTemplate('sf_externalLink_rows', { arrlinks: r.arrlinks});
					$('tbody.sf_tbodyExternalLinks').html(renderedHTML);
					sf_resetGridRowNumbering('sf_tbodyExternalLinks');
					sf_maxLinkID = sf_getMaxAutoID(r.arrlinks);
				}
				else alert('We were unable to load the external site links.');
				if(!sf_readonly) $('##sf_btnAddLink').prop('disabled',false);
			};

			$('##sf_btnAddLink').prop('disabled',true);
			$('tbody.sf_tbodyExternalLinks').html('<tr><td class="p-2" colspan="4">'+mca_getLoadingHTML('Loading..')+'</td></tr>');
			var objParams = { submissionID:submissionID };
			TS_AJX('ADMINSWOD','getSubmissionLinks',objParams,getResult,getResult,20000,getResult);
		}
		function sf_editLink(id) {
			var obj = {
				autoid: id,
				linkurl:$('##sf_linkURL_'+id).val() || '',
				linkname:$('##sf_linkName_'+id).val() || ''
			};
			var renderedHTML = renderHTMLFromTemplate('sf_externalLink_add',obj);
			$('##sf_editLinkFormContainer').html(renderedHTML).removeClass('d-none');
		}
		function sf_cancelLink(){
			$('##sf_editLinkFormContainer').html('').addClass('d-none');
		}
		function sf_removeLink(id){
			$('tbody.sf_tbodyExternalLinks tr##sf_trLink_'+id).remove();
			sf_resetGridRowNumbering('sf_tbodyExternalLinks');
		}
		function sf_saveLink(id){
			mca_hideAlert('sf_err_link');
			var linkurl = $('##sf_linkURL').val().trim();
			var linkname = $('##sf_linkName').val().trim();
			var urlRegEx = new RegExp("#application.regEx.url#", "gi");
			var arrReq = [];

			if(linkurl.length > 0 && !/^((http|https):\/\/)/.test(linkurl)){
				linkurl = "http://" + linkurl;
				$('##sf_linkURL').val(linkurl);
			}

			if (linkurl.length == 0 || !(urlRegEx.test(linkurl))) arrReq[arrReq.length] = 'Enter a valid Link URL.';
			if (linkname.length == 0 ) arrReq[arrReq.length] = 'Enter a valid Link Name.';
			if(arrReq.length){
				mca_showAlert('sf_err_link', arrReq.join('<br/>'));
				return false;
			}
			
			var obj = { arrlinks:[{ autoid: id == 'x' ? ++sf_maxLinkID : id, linkurl:linkurl, linkname:linkname }] };
			var renderedHTML = renderHTMLFromTemplate('sf_externalLink_rows',obj);
			if(id != 'x') {
				var thisRow = $('tbody.sf_tbodyExternalLinks ##sf_trLink_'+id);
				$(renderedHTML).insertAfter(thisRow);
				thisRow.remove();
			}
			else $('tbody.sf_tbodyExternalLinks').append(renderedHTML);
			sf_cancelLink();
			sf_resetGridRowNumbering('sf_tbodyExternalLinks');
		}
		<!--- program evaluations --->
		function sf_getSWEvaluations() {
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if(r.arrorgforms.length){
						var renderedHTML = renderHTMLFromTemplate('sf_evaluation_rows', { arrorgforms: r.arrorgforms });
						$('tbody.sf_tbodyEvaluations').html(renderedHTML);
						$('tbody.sf_tbodyEvaluations_noRecords').addClass('d-none');
						if(sf_readonly) $('tbody.sf_tbodyEvaluations tr :checkbox').prop('disabled',true);
					}
					else $('tbody.sf_tbodyEvaluations').html('');
				}
				else alert('We were unable to load the custom evaluations.');
			};
			
			$('tbody.sf_tbodyEvaluations').html('<tr><td class="p-2" colspan="2">'+mca_getLoadingHTML('Loading..')+'</td></tr>');
			var objParams = { submissionID:submissionID, format:'S' };
			TS_AJX('ADMINSWOD','getSubmissionForms',objParams,getResult,getResult,20000,getResult);
		}
	<cfif local.submissionID eq 0>	
		function sf_initCustomEvaluationUploader(obj) {
			sf_customEvalUploader = new plupload.Uploader({
				runtimes : 'html5',
				browse_button : 'btnSelectCustomEvaluationFile',
				multi_selection: false,
				url : obj.uploadurl,
				multipart : true,
				init : {
					PostInit: function() {
						$('##selectedCustomEvaluationFileName').val('');
					},
					FilesAdded: function(up, files) {
						plupload.each(files, function(file) {
							$('##selectedCustomEvaluationFileName').html('<div id="' + file.id + '"><b>' + file.name + ' (' + plupload.formatSize(file.size) + ')</b></div>');
						});
						$('##btnSelectCustomEvaluationFile').prop('disabled',true);
						sf_customEvalUploaderHasFile = true;
					},
					BeforeUpload: function(up, file) {
						var contentType = "application/octet-stream";
						var ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
						var filename = submissionID + '-customeval' + ext;
						var key = obj.objectkey + filename;
						
						up.settings.multipart_params = {
							'Filename': filename,
							'key': key,
							'AWSAccessKeyId': obj.accesskey,
							'acl': 'public-read',
							'content-type': contentType,
							'policy': obj.policy,
							'signature': obj.policysignature
						};
						up.setOption('params', up.settings.multipart_params);
					},
					UploadComplete: function(up, files) {
						sf_onUploadCompleteEvaluationFile();
					},
					Error: function(up, error) {
						if (error.message != 'File extension error.'){
							sf_arrErrMsg.push('We ran into an issue while uploading custom evaluation file. ' + error.message);
							sf_onUploadCompleteEvaluationFile();
						}
						else alert(error.message);
					}
				},
				filters : {
					max_file_size : '2048mb',
					mime_types: [ {title:"Downloadable files", extensions:"#local.customEvalFilesExtList#"} ]
				}
			});
			sf_customEvalUploader.init();
		}
		function sf_onUploadCompleteEvaluationFile(){
			sf_saveSubmitSWODProgram_continue('saveCustomEvalFile');
		}
		function sf_initCustomExamUploader(obj) {
			sf_customExamUploader = new plupload.Uploader({
				runtimes : 'html5',
				browse_button : 'btnSelectCustomExamFile',
				multi_selection: false,
				url : obj.uploadurl,
				multipart : true,
				init : {
					PostInit: function() {
						$('##selectedCustomExamFileName').val('');
					},
					FilesAdded: function(up, files) {
						plupload.each(files, function(file) {
							$('##selectedCustomExamFileName').html('<div id="' + file.id + '"><b>' + file.name + ' (' + plupload.formatSize(file.size) + ')</b></div>');
						});
						$('##btnSelectCustomExamFile').prop('disabled',true);
						sf_customExamUploaderHasFile = true;
					},
					BeforeUpload: function(up, file) {
						var contentType = "application/octet-stream";
						var ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
						var filename = submissionID + '-customexam' + ext;
						var key = obj.objectkey + filename;
						
						up.settings.multipart_params = {
							'Filename': filename,
							'key': key,
							'AWSAccessKeyId': obj.accesskey,
							'acl': 'public-read',
							'content-type': contentType,
							'policy': obj.policy,
							'signature': obj.policysignature
						};
						up.setOption('params', up.settings.multipart_params);
					},
					UploadComplete: function(up, files) {
						sf_onUploadCompleteExamFile();
					},
					Error: function(up, error) {
						if (error.message != 'File extension error.'){
							sf_arrErrMsg.push('We ran into an issue while uploading custom exam file. ' + error.message);
							sf_onUploadCompleteExamFile();
						}
						else alert(error.message);
					}
				},
				filters : {
					max_file_size : '2048mb',
					mime_types: [ {title:"Downloadable files", extensions:"#local.customExamFilesExtList#"} ]
				}
			});
			sf_customExamUploader.init();
		}
		function sf_onUploadCompleteExamFile(){
			sf_saveSubmitSWODProgram_continue('saveCustomExamFile');
		}
		function sf_initProgramMaterialFilesUploader(obj) {
			$("##sf_programMaterialFilesUploader").pluploadQueue({
				runtimes : 'html5',
				url : obj.uploadurl,
				multipart : true,
				file_data_name : 'file', 
				multiple_queues : true,
				preinit: function (uploader) { sf_programMaterialsUploader = uploader; },
				init : {
					PostInit: function() {
						$('##sf_programMaterialFilesUploader .plupload_header_text').html('Add files to the upload queue and proceed to next step.');
						$('##sf_programMaterialFilesUploader .plupload_button.plupload_start').remove();
					},
					
					FilesAdded: function(up, addedFiles) {
						sf_programMaterialsUploaderHasFile = addedFiles.length > 0;
						
						if (addedFiles.length > 0) {
							if(addedFiles[0].size > 1999990000){
								up.removeFile(up.getFile(addedFiles[0].id));
								$('##' + addedFiles[0].id).remove();
								alert('Your file is too large. Please upload a file that is under 2GB');
								return false;
							}

							/*add original name field*/
							up.files.forEach(function(file) {
								if (typeof file.orginalName == 'undefined')
									file.orginalName = file.name;
							});

							/*remove duplicates*/
							var i = 0;
							while (i < addedFiles.length) {
								var dupe = false;
								var j = 0;
								while (j < (up.files.length - addedFiles.length)) {
									if (addedFiles[i].name == up.files[j].orginalName) {
										dupe = true;
										up.removeFile(up.getFile(addedFiles[i].id));
										$('##' + addedFiles[i].id).remove();
										addedFiles.splice(i, 1);
										break;
									}									
									j++;
								}
								if (!dupe) i++;
							}
						}
					},
					BeforeUpload: function(up, file) {
						var contentType = "application/octet-stream";
						var key = obj.objectkey + submissionID + '-materials' + '/' + file.name;
						
						up.settings.multipart_params = {
							'Filename': key,
							'key': key,
							'AWSAccessKeyId': obj.accesskey,
							'acl': 'public-read',
							'content-type': contentType,
							'policy': obj.policy,
							'signature': obj.policysignature
						};
						up.setOption('params', up.settings.multipart_params);
					},
					UploadComplete: function(up, files) {
						sf_onUploadCompleteProgramMaterialFiles();
					},
					Error: function(up, error) {
						if (error.message == 'File size error.'){
							return false;
						}
						if (error.message != 'File extension error.'){
							sf_arrErrMsg.push('We ran into an issue while uploading program material files. ' + error.message);
							sf_onUploadCompleteProgramMaterialFiles();
						}
					}
				},
				filters : {
					max_file_size : '2048mb',
					mime_types: [ {title:"Downloadable files", extensions:"#local.downloadableFilesExtList#"} ]
				}
			});
		}
		function sf_onUploadCompleteProgramMaterialFiles(){
			sf_saveSubmitSWODProgram_continue('saveProgramMaterialFiles');
		}
	</cfif>
		<!--- program rates --->
		function sf_getProgramRates() {
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					var renderedHTML = renderHTMLFromTemplate('sf_programRate_rows', { arrrates: r.arrrates});
					$('tbody.sf_tbodyProgramRates').html(renderedHTML);
					sf_resetGridRowNumbering('sf_tbodyProgramRates');
					sf_maxRateID = sf_getMaxAutoID(r.arrrates);
				}
				else alert('We were unable to load the program rates.');
				if(!sf_readonly) $('##sf_btnAddRate').prop('disabled',false);
			};

			$('##sf_btnAddRate').prop('disabled',true);
			$('tbody.sf_tbodyProgramRates').html('<tr><td class="p-2" colspan="5">'+mca_getLoadingHTML('Loading..')+'</td></tr>');
			var objParams = { submissionID:submissionID };
			TS_AJX('ADMINSWOD','getSubmissionRates',objParams,getResult,getResult,20000,getResult);
		}
		function sf_editRate(id) {
			var obj = {
				autoid: id,
				ratename:$('##sf_rateName_'+id).val() || '',
				rate:$('##sf_rate_'+id).val() || '',
				arrgroupids:($('##sf_rateGroupIDs_'+id).val() || '').split('|'),
				arrdeniedgroupids:($('##sf_deniedRateGroupIDs_'+id).val() || '').split('|')
			};
			var renderedHTML = renderHTMLFromTemplate('sf_programRate_add',obj);
			$('##sf_editRateFormContainer').html(renderedHTML).removeClass('d-none');
			mca_setupSelect2();
			$('##sf_rateGroupID').val(obj.arrgroupids).change();
			$('##sf_deniedRateGroupID').val(obj.arrdeniedgroupids).change();

			/*remove duplicate selection*/
			$("##sf_rateGroupID option:selected").each(function (idx, val) {
				if($("##sf_rateGroupID option[value='" + $(this).val() + "']:selected").length > 1){
					$(this).prop('selected', false);
				}
			});
			$("##sf_deniedRateGroupID option:selected").each(function (idx, val) {
				if($("##sf_deniedRateGroupID option[value='" + $(this).val() + "']:selected").length > 1){
					$(this).prop('selected', false);
				}
			});
			$('##sf_rateGroupID').change();
			$('##sf_deniedRateGroupID').change();

			var isPriceBasedOnActual = $('##sf_isPriceBasedOnActual').is(':checked');
			$('.fs_ratePermissionGroupsSection').toggleClass('d-none',!isPriceBasedOnActual);
		}
		function sf_cancelRate(){
			$('##sf_editRateFormContainer').html('').addClass('d-none');
		}
		function sf_removeRate(id){
			$('tbody.sf_tbodyProgramRates tr##sf_trProgramRate_'+id).remove();
			sf_resetGridRowNumbering('sf_tbodyProgramRates');
		}
		function sf_saveRate(id){
			mca_hideAlert('sf_err_rate');
			var ratename = $('##sf_rateName').val();
			var rate = $('##sf_rate').val();
			var arrReq = [];

			if (ratename == '') arrReq[arrReq.length] = 'Enter the rate name.';
			if (rate == '') arrReq.push('Enter the rate.');
			if(arrReq.length){
				mca_showAlert('sf_err_rate', arrReq.join('<br/>'));
				return false;
			}

			var arrSelGroupIDs = $("##sf_rateGroupID").val() || [];
			var arrSelGroupNames = [];
			$("##sf_rateGroupID option:selected").each(function () {
				if ($(this).length) {
					arrSelGroupNames.push($(this).text());
				}
			});
			var arrSelDeniedGroupIDs = $("##sf_deniedRateGroupID").val() || [];
			var arrSelDeniedGroupNames = [];
			$("##sf_deniedRateGroupID option:selected").each(function () {
				if ($(this).length) {
					arrSelGroupNames.push('Denied: '+$(this).text());
				}
			});
			
			var obj = { arrrates:[{ autoid: id == 'x' ? ++sf_maxRateID : id, ratename:ratename, rate:rate, groupids:arrSelGroupIDs.join('|'), arrgroupnames:arrSelGroupNames, deniedgroupids:arrSelDeniedGroupIDs.join('|'), arrdeniedgroupnames:arrSelDeniedGroupNames }] };
			var renderedHTML = renderHTMLFromTemplate('sf_programRate_rows',obj);
			if(id != 'x') {
				var thisRow = $('tbody.sf_tbodyProgramRates ##sf_trProgramRate_'+id);
				$(renderedHTML).insertAfter(thisRow);
				thisRow.remove();
			}
			else $('tbody.sf_tbodyProgramRates').append(renderedHTML);
			sf_cancelRate();
			sf_resetGridRowNumbering('sf_tbodyProgramRates');
		}
		function loadSWODSubmissionProgramRates(r) {
			if (r.hasrategroup) {
				$('##sf_isPriceBasedOnActual').prop('checked', true);
			} else {
				$('##sf_isPriceBasedOnActual').prop('checked', false);
			}
			var renderedHTML = renderHTMLFromTemplate('sf_programRate_rows', { arrrates: r.arrrates});
			$('tbody.sf_tbodyProgramRates').html(renderedHTML);
			sf_resetGridRowNumbering('sf_tbodyProgramRates');
			sf_maxRateID = sf_getMaxAutoID(r.arrrates);
		}
		<!--- syndicated prices --->
		function sf_getSyndicatedPrices(arrprices) {
			<cfif local.submissionID gt 0 and arrayLen(local.arrSyndPrices)>
				$('##sf_btnAddSyndicatedPrice').prop('disabled',true);
				var #toScript(serializeJSON(local.arrSyndPrices),"arrprices")#;
				arrprices = $.parseJSON(arrprices);
				var renderedHTML = renderHTMLFromTemplate('sf_syndicatedPrice_rows', { arrprices: arrprices});
				$('tbody.sf_tbodySyndicatedPrices').html(renderedHTML);
				sf_resetGridRowNumbering('sf_tbodySyndicatedPrices');
				sf_maxSyndPriceID = sf_getMaxAutoID(arrprices);
				if(!sf_readonly) $('##sf_btnAddSyndicatedPrice').prop('disabled',false);
			</cfif>
		}
		function sf_addSyndicatedPrice() {
			var obj = { arrprices:[{ autoid: ++sf_maxSyndPriceID, pricecategory:'', price:'' }]};
			var renderedHTML = renderHTMLFromTemplate('sf_syndicatedPrice_rows',obj);
			$('tbody.sf_tbodySyndicatedPrices').append(renderedHTML);
			sf_resetGridRowNumbering('sf_tbodySyndicatedPrices');
		}
		function sf_removeSyndicatedPrice(id){
			$('tbody.sf_tbodySyndicatedPrices tr##sf_trSyndicatedPrice_'+id).remove();
			sf_resetGridRowNumbering('sf_tbodySyndicatedPrices');
		}
	<cfif local.submissionID eq 0>
		function sf_initFeaturedImageUploader(obj) {
			sf_featuredImageUploader = new plupload.Uploader({
				runtimes : 'html5',
				browse_button : 'sf_btnSelectFeaturedImage',
				multi_selection: false,
				url : obj.uploadurl,
				multipart : true,
				init : {
					PostInit: function() {
						$('##sf_selectedFeaturedImageFileName').val('');
					},
					FilesAdded: function(up, files) {
						plupload.each(files, function(file) {
							$('##sf_selectedFeaturedImageFileName').html('<div id="' + file.id + '"><b>' + file.name + ' (' + plupload.formatSize(file.size) + ')</b></div>');
						});
						$('##sf_btnSelectFeaturedImage').prop('disabled',true);
						sf_featuredImageUploaderHasFile = true;
					},
					BeforeUpload: function(up, file) {
						var contentType = "application/octet-stream";
						var ext = file.name.substr(file.name.lastIndexOf('.')).toLowerCase();
						var filename = submissionID + '-ftdimg' + ext;
						var key = obj.objectkey + filename;
						
						up.settings.multipart_params = {
							'Filename': filename,
							'key': key,
							'AWSAccessKeyId': obj.accesskey,
							'acl': 'public-read',
							'content-type': contentType,
							'policy': obj.policy,
							'signature': obj.policysignature
						};
						up.setOption('params', up.settings.multipart_params);
					},
					UploadComplete: function(up, files) {
						sf_onUploadCompleteFeaturedImageFile();
					},
					Error: function(up, error) {
						if (error.message != 'File extension error.'){
							sf_arrErrMsg.push('We ran into an issue while uploading featured image. ' + error.message);
							sf_onUploadCompleteFeaturedImageFile();
						}
						else alert(error.message);
					}
				},
				filters : {
					max_file_size : '2048mb',
					mime_types: [ {title:"Image files", extensions:"#local.ftdImageFilesExtList#"} ]
				}
			});
			sf_featuredImageUploader.init();
		}
		function sf_onUploadCompleteFeaturedImageFile(){
			sf_saveSubmitSWODProgram_continue('saveFeaturedImage');
		}
	</cfif>
		function sf_showSubjectAreaForm(){
			$('##sf_newCategoryName').val('');
			$('##sf_divNewSubjectForm').removeClass('d-none');
		}
		function sf_addNewSubjectArea() {
			var categoryName = $('##sf_newCategoryName').val() || '';
			if (categoryName == ''){
				$('##sf_newCategoryName').focus();
				return false;
			}

			var insertResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					var arrSelectedCategoryIDs = $('##sf_categoryID').val() || [];
					arrSelectedCategoryIDs.push(r.categoryid);
					let newOption = '<option value="' + r.categoryid + '">' + categoryName + '</option>';
					$('##sf_categoryID').find('option').end().append(newOption);
					$('##sf_categoryID').val(arrSelectedCategoryIDs);
				}
				else alert(r.errmsg && r.errmsg.length > 0 ? r.errmsg : 'An error occured while trying to add new subject area.');
				sf_cancelSubjectAreaForm();
				$('##sf_btnAddNewCategory').prop('disabled',false);
			};

			$('##sf_btnAddNewCategory').prop('disabled',true);
			var objParams = { participantOrgCode:'#local.participantOrgCode#', categoryID:0, categoryName:categoryName };
			TS_AJX('ADMINSWCATEGORIES','saveSWCategory',objParams,insertResult,insertResult,10000,insertResult);
		}
		function sf_cancelSubjectAreaForm(){
			$('##sf_divNewSubjectForm').addClass('d-none');
		}
		<!--- program credits --->
		function sf_getCECredits() {
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if(r.arrcecredits.length) $('##sf_offerCECredit').prop('checked', true).trigger('change');
					<!--- hide already added credits from dropdown --->
					$.each(r.arrcecredits, function(index,obj){
						sf_toggleCreditDropdownOption(obj.csalinkid, false);
					});
					var renderedHTML = renderHTMLFromTemplate('sf_CECredit_rows', { arrcecredits: r.arrcecredits});
					$('tbody.sf_tbodyCECredits').html(renderedHTML);
					sf_resetGridRowNumbering('sf_tbodyCECredits');
					sf_maxCreditID = sf_getMaxAutoID(r.arrcecredits);
				}
				else alert('We were unable to load the ce credits.');
				if(!sf_readonly) $('##sf_btnAddCECredit').prop('disabled',false);
			};

			$('##sf_btnAddCECredit').prop('disabled',true);
			$('tbody.sf_tbodyCECredits').html('<tr><td class="p-2" colspan="5">'+mca_getLoadingHTML('Loading..')+'</td></tr>');
			var objParams = { submissionID:submissionID };
			TS_AJX('ADMINSWOD','getSubmissionCredits',objParams,getResult,getResult,20000,getResult);
		}
		function sf_editCECredit(id) {
			var csalinkid = $('##sf_CSALinkID_' + id).val() || 0;
			if(csalinkid == 0){
				$('##sf_CSALinkID_x').focus();
				return false;
			}

			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					$.each( r.arrcredittypes, function( index, value ){
						r.arrcredittypes[index].creditvalue = $('##sf_xcredit_'+id+'_'+value.fieldname).val() || '';
					});
					var obj = {
						autoid: id,
						csalinkid: csalinkid,
						authoritycode: r.authoritycode,
						authorityname: r.authorityname,
						sponsorname: r.sponsorname,
						statusid:$('##sf_creditStatusID_' + id).val() || 0,
						offeredstartdate:$('##sf_creditOfferedStartDate_'+id).val() || (id == 'x' ? '#DateFormat(now(),'m/d/yyyy')#' : ''),
						offeredenddate:$('##sf_creditOfferedEndDate_'+id).val() || (id == 'x' ? '#DateFormat(dateAdd("yyyy",1,now()),'m/d/yyyy')#' : ''),
						completebydate:$('##sf_creditCompleteByDate_'+id).val() || (id == 'x' ? '#DateFormat(dateAdd("yyyy",1,now()),'m/d/yyyy')#' : ''),
						iscreditrequired:$('##sf_isCreditRequired_' + id).val() || 0,
						creditapprovalnum:$('##sf_creditApprovalNum_' + id).val() || '',
						arrcredittypes:r.arrcredittypes
					};
					var renderedHTML = renderHTMLFromTemplate('sf_CECredit_add',obj);
					$('##sf_editCECreditFormContainer').html(renderedHTML).removeClass('d-none');
					mca_setupDatePickerRangeFields('sf_creditOfferedStartDate','sf_creditOfferedEndDate');
					mca_setupDatePickerField('sf_creditCompleteByDate');
					mca_setupCalendarIcons('frmSubmitSWODProgram');

					if(id != 'x') $('html,body').animate({scrollTop: $('##sf_editCECreditFormContainer').position().top - 100},500);
				}
				else {
					alert('We were unable to get the credit authority info. Try again.');
					sf_cancelCECredit();
				}
			};

			$('##sf_editCECreditFormContainer').html(mca_getLoadingHTML()).removeClass('d-none');
			$('##sf_addCreditSelectionContainer').addClass('d-none');
			TS_AJX('ADMINSWOD','getCreditSponsorAndAuthorityInfo',{CSALinkID:csalinkid},getResult,getResult,20000,getResult);
		}
		function sf_cancelCECredit(){
			mca_hideAlert('err_submitprogram');
			$('##sf_editCECreditFormContainer').html('').addClass('d-none');
			$('##sf_addCreditSelectionContainer').removeClass('d-none');
		}
		function sf_removeCECredit(id){
			var CSALinkID = $('##sf_CSALinkID_' + id).val();
			$('tbody.sf_tbodyCECredits tr##sf_trCECredit_'+id).remove();
			sf_toggleCreditDropdownOption(CSALinkID, true);
			sf_resetGridRowNumbering('sf_tbodyCECredits');
		}
		function sf_saveCECredit(id){
			mca_hideAlert('err_submitprogram');
			mca_hideAlert('sf_err_credit');
			var arrReq = [];

			var CSALinkID = $('##sf_CSALinkID').val() || 0;
			var statusID = $('##sf_creditStatusID').val();
			var statusName = $( "##sf_creditStatusID option:selected" ).text();
			var creditOfferedStartDate = $('##sf_creditOfferedStartDate').val();
			var creditOfferedEndDate = $('##sf_creditOfferedEndDate').val();
			var creditCompleteByDate = $('##sf_creditCompleteByDate').val();
			var validateFields = $.inArray(statusName, ['Pending','Approved','Self-Submitting']) >= 0;
			var emptyCreditsCount = 0;

			var arrcreditvalues = [];
			var totalCreditFieldsCount = $('form##frmSubmitSWODProgram .sf_credVal').length;
			$('form##frmSubmitSWODProgram .sf_credVal').each(function() {
				if($(this).val().trim() == '' || $(this).val().trim() == 0){
					emptyCreditsCount++;
				}
				else if(isNaN($(this).val().trim())){
					arrReq[arrReq.length] = "Enter a numeric value for " + $(this).data('displayname');
				}
				else if($(this).val().trim() != ''){
					arrcreditvalues.push({
						fieldname:$(this).data('fieldname'),
						value:$(this).val(),
						displayname:$(this).data('displayname')
					});
				}
			});

			if(validateFields){
				if (creditOfferedStartDate.length == 0 ) arrReq[arrReq.length] = 'Enter the credit offered start date.';
				if (creditOfferedEndDate.length == 0 ) arrReq[arrReq.length] = 'Enter the credit offered end date.';
				if (creditCompleteByDate.length == 0 ) arrReq[arrReq.length] = 'Enter the credit complete by date.';
				if(totalCreditFieldsCount > 0 && totalCreditFieldsCount == emptyCreditsCount)
					arrReq[arrReq.length] = totalCreditFieldsCount > 1 ? 'Enter a value for atleast one credit type.' : 'Enter a value for the credit type.';
			}

			if(arrReq.length){
				mca_showAlert('sf_err_credit', arrReq.join('<br/>'));
				return false;
			}
			
			var obj = {
				arrcecredits:[{
					autoid: id == 'x' ? ++sf_maxCreditID : id,
					csalinkid:CSALinkID,
					authoritycode:$('##sf_CECredit_authorityCode').val(),
					authorityname:$('##sf_CECredit_authorityName').val(),
					sponsorname:$('##sf_CECredit_sponsorName').val(),
					statusid:statusID,
					statusname:statusName,
					offeredstartdate:creditOfferedStartDate,
					offeredenddate:creditOfferedEndDate,
					completebydate:creditCompleteByDate,
					iscreditrequired:$('##sf_isCreditRequired').is(':checked') ? 1 : 0,
					creditapprovalnum:$('##sf_creditApprovalNum').val(),
					arrcreditvalues:arrcreditvalues
				}]
			};
			var renderedHTML = renderHTMLFromTemplate('sf_CECredit_rows',obj);
			if(id != 'x') {
				var thisRow = $('tbody.sf_tbodyCECredits ##sf_trCECredit_'+id);
				$(renderedHTML).insertAfter(thisRow);
				thisRow.remove();
			}
			else $('tbody.sf_tbodyCECredits').append(renderedHTML);

			<!--- exclude this newly added option from selection dropdown --->
			if(id == 'x') sf_toggleCreditDropdownOption(CSALinkID, false);

			sf_cancelCECredit();
			sf_resetGridRowNumbering('sf_tbodyCECredits');
		}
		function sf_toggleCreditDropdownOption(csalinkid, f){
			$("##sf_CSALinkID_x option[value='"+ csalinkid +"']").toggleClass('d-none',!f);
			var nextCSALinkID = $("##sf_CSALinkID_x option:not(.d-none):first").val() || '';
			$("##sf_CSALinkID_x").val(nextCSALinkID);
		}
		function sf_autoSelectCreditsCheck(){
			if($('tbody.sf_tbodyCECredits tr').length == 0){
				<!--- auto select credit if single entry --->
				if($('##sf_CSALinkID_x option').length == 1) sf_editCECredit('x');
				<!--- auto add all credit sponsors tied to the site --->
				else autoAddSiteLevelCreditSponsors();
			}
		}
		function autoAddSiteLevelCreditSponsors(){
			<cfif local.qrySiteSponsorsAndAuthorities.recordCount>
				var arrcecredits = [];
				<cfset local.qryDefaultCreditStatus = QueryFilter(local.qryCreditStatuses, function(row) { return arguments.row.status EQ "Not Submitted"; })>
				<cfloop query="local.qrySiteSponsorsAndAuthorities">
					arrcecredits.push({
						autoid: ++sf_maxCreditID,
						csalinkid:#local.qrySiteSponsorsAndAuthorities.CSALinkID#,
						authoritycode:'#JSStringFormat(local.qrySiteSponsorsAndAuthorities.authorityCode)#',
						authorityname:'#JSStringFormat(local.qrySiteSponsorsAndAuthorities.authorityName)#',
						sponsorname:'#JSStringFormat(local.qrySiteSponsorsAndAuthorities.sponsorName)#',
						statusid:#local.qryDefaultCreditStatus.statusID#,
						statusname:'#local.qryDefaultCreditStatus.status#',
						offeredstartdate:'#DateFormat(now(),'m/d/yyyy')#',
						offeredenddate:'#DateFormat(dateAdd("yyyy",1,now()),'m/d/yyyy')#',
						completebydate:'#DateFormat(dateAdd("yyyy",1,now()),'m/d/yyyy')#',
						iscreditrequired:0,
						creditapprovalnum:'',
						arrcreditvalues:[]
					});
				</cfloop>
				var renderedHTML = renderHTMLFromTemplate('sf_CECredit_rows',{ arrcecredits:arrcecredits });
				$('tbody.sf_tbodyCECredits').append(renderedHTML);
				$.each(arrcecredits, function( index, value ){
					sf_toggleCreditDropdownOption(value.csalinkid, false);
				});
				sf_resetGridRowNumbering('sf_tbodyCECredits');
			</cfif>
		}
		function sf_getUploadedFiles() {
			var getResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					var template = Handlebars.compile($('##sf_fileuploads').html());
					$('##sf_uploadedFiles_customEval').html(template({ type:'customeval', header:'Uploaded Custom Evaluation File', linktext:'Download File', nofiletext:'No file uploaded', showfilename:0, arruploads:r.arruploads.filter(v => v.type == "customeval") }));
					$('##sf_uploadedFiles_customExam').html(template({ type:'customexam', header:'Uploaded Custom Exam File', linktext:'Download File', nofiletext:'No file uploaded', showfilename:0, arruploads:r.arruploads.filter(v => v.type == "customexam") }));
					$('##sf_uploadedFiles_materials').html(template({ type:'materials', header:'Uploaded Materials', linktext:'Download File', nofiletext:'No files uploaded', showfilename:1, arruploads:r.arruploads.filter(v => v.type == "materials") }));
					$('##sf_uploadedFiles_ftdImage').html(template({ type:'ftdimage', header:'Uploaded Featured Image', linktext:'Download Image', nofiletext:'No image uploaded', showfilename:0, arruploads:r.arruploads.filter(v => v.type == "ftdimage") }));
					
					if(r.arruploads.filter(v => v.type == "ftdimage").length > 0) {$('##sf_addNewProgramImg').attr('checked',true);$('.sf_divFeaturedImage').removeClass('d-none');}
				}
				else alert('We were unable to load the file uploads.');
			};
			
			$('.sf_uploadedFiles').html(mca_getLoadingHTML('Loading..'));
			var objParams = { submissionID:submissionID, participantID:#val(local.qrySubmissionDetail.participantID)#, participantOrgCode:'#local.participantOrgCode#' };
			TS_AJX('ADMINSWOD','getSubmissionFileUploads',objParams,getResult,getResult,20000,getResult);
		}

		$(function() {
			$("##submitSWODProgramWizard").steps({
				headerTag: "h3",
				bodyTag: "section",
				autoFocus: false,
				titleTemplate: '<span class="number">##index##</span>',
				stepsOrientation: 1,
				<cfif not local.readOnly>
				onStepChanging: sf_validateSubmitSWODStepChange,
				</cfif>
				onFinishing: sf_saveSubmitSWODProgram,
				onInit: function(){
					if(sf_readonly) $('##submitSWODProgramWizard a[href="##finish"]').addClass('d-none');
				},
				labels: {
					next: sf_readonly ? "Next": "Continue to Next Step",
					finish: "#local.submitButtonText#",
				}
			});
			mca_setupScrollable($('##frmSubmitSWODProgram'));
			mca_setupDatePickerField('sf_dateOrigPublished');
			mca_setupDatePickerRangeFields('sf_dateCatalogStart','sf_dateCatalogEnd');
			mca_setupCalendarIcons('frmSubmitSWODProgram');
			mca_setupSelect2();

			$('##sf_addLearningObjectives').change(function () {
				$('.sf_divLearningObjectives').toggleClass('d-none',!this.checked);
			});
			$('##sf_offerQA').change(function () {
				$('.sf_divOfferQA').toggleClass('d-none',!this.checked);
			});
			$('##sf_includeLinks').change(function () {
				$('.sf_divExternalLinks').toggleClass('d-none',!this.checked);
			});
			$('##sf_offerEval').change(function () {
				$('.sf_divOfferEvaluation').toggleClass('d-none',!this.checked);
			});
			$('##sf_offerExam').change(function () {
				$('.sf_divOfferExam').toggleClass('d-none',!this.checked);
			});
			$('##sf_allowCatalog').change(function () {
				$('.sf_divAllowCatalog').toggleClass('d-none',!this.checked);
			});
			$('##sf_isPriceBasedOnActual').change(function () {
				$('.fs_ratePermissionGroupsSection').toggleClass('d-none',!this.checked);
			});
			$('##sf_allowSyndication').change(function () {
				$('.sf_divAllowSyndication').toggleClass('d-none',!this.checked);
			});
			$('##sf_createOwnPricingToOptIns').change(function () {
				$('.sf_divDefaultPricingToOptIns').toggleClass('d-none',this.checked);
			});
			$('##sf_addNewProgramImg').change(function () {
				$('.sf_divFeaturedImage').toggleClass('d-none',!this.checked);
			});
			$('##sf_offerCECredit').change(function () {
				$('.sf_divCECredit').toggleClass('d-none',!this.checked);
				<cfif local.submissionID eq 0>
					if(this.checked) sf_autoSelectCreditsCheck();
				</cfif>
			});

			<cfif local.submissionID gt 0>
				$('##sf_offerQA').trigger("change");
				$('##sf_allowCatalog').trigger("change");
				$('##sf_allowSyndication').trigger("change");
				$('##sf_addNewProgramImg').trigger("change");
				$('##sf_createOwnPricingToOptIns').trigger("change");
				$('##sf_offerEval').trigger("change");
				$('##sf_offerExam').trigger("change");

				sf_getProgramObjectives();
				sf_getSWAuthors('Speaker');
				sf_getSWAuthors('Moderator');
				sf_getExternalLinks();
				sf_getProgramRates();
				sf_getSyndicatedPrices();
				sf_getCECredits();
				sf_getUploadedFiles();
			</cfif>

			sf_getSWEvaluations();

			if(CKEDITOR.instances['sf_seminarDescEditor'] != null)
				CKEDITOR.instances['sf_seminarDescEditor'].destroy();

			<cfif local.submissionID eq 0>
				var uploadSettings = JSON.parse('#local.fileUploadSettingsJSON#');
				sf_initCustomEvaluationUploader(uploadSettings);
				sf_initCustomExamUploader(uploadSettings);
				sf_initProgramMaterialFilesUploader(uploadSettings);
				sf_initFeaturedImageUploader(uploadSettings);
			</cfif>

			<cfif local.readOnly>
				$('##submitSWODProgramWizard.wizard > div.content').find('input:not([type=hidden]),select,textarea,button').prop('disabled',true);
			</cfif>
		});
	</script>
	<style type="text/css">
		##frmSubmitSWODProgram .vertical {border-top-left-radius: 0.65rem;}
		##frmSubmitSWODProgram .vertical .steps {width:90px;border-top-left-radius: 0.65rem;}
		##frmSubmitSWODProgram .vertical .content, .vertical .actions {width:calc(100% - 90px);}
		##frmSubmitSWODProgram .vertical .content {height:calc(100% - 69px);overflow-y:auto;min-height: 25em;}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.submitSWODJS)#">

<cfoutput>
<div class="mt-2 mb-3">
	<a href="##" onclick="return cancelSubmitSWODProgramForm(#local.submissionID#);">
		<i class="fa-solid fa-chevrons-left"></i> Return to <cfif local.submissionID gt 0>SeminarWeb On Demand Submissions<cfelse>#arguments.event.getValue('mc_siteInfo.swodBrand')#</cfif>
	</a>
</div>
<h5><cfif local.submissionID gt 0>Review Program Submission<cfelse>Submit a Program</cfif></h5>
<div class="submitSWODProgramFormContainer">
	<form name="frmSubmitSWODProgram" id="frmSubmitSWODProgram" class="h-100">
		<input type="hidden" name="sf_seminarDesc" id="sf_seminarDesc" value="">
		<div id="submitSWODProgramWizard" class="h-100">
			<!--- Step 1 --->
			<h3 class="font-weight-bold mb-3">Seminar Basics</h3>
			<section>
				<div class="form-group">
					<div class="form-label-group">
						<input type="text" name="sf_seminarName" id="sf_seminarName" value="#replace(local.qrySubmissionDetail.seminarName,chr(34),'&quot;','ALL')#" class="form-control" maxlength="250">
						<label for="sf_seminarName">Seminar Title</label>
					</div>
				</div>
				<div class="form-group">
					<div class="form-label-group">
						<input type="text" name="sf_seminarSubTitle" id="sf_seminarSubTitle" value="#replace(local.qrySubmissionDetail.seminarSubTitle,chr(34),'&quot;','ALL')#" class="form-control" maxlength="250">
						<label for="sf_seminarSubTitle">Seminar Sub Title (optional)</label>
					</div>
				</div>
				<div class="row form-group my-3">
					<div class="col-12">
						#application.objWebEditor.embed(objname="sf_seminarDescEditor", objValue="#local.qrySubmissionDetail.seminarDesc#", tools="ContentEditor", autoEnable=1, fieldLabel="Description", supportsBootstrap=true, ImageUpload=false, ImageBrowser=false)#
					</div>
				</div>
				<div class="form-group">
					<div class="form-label-group">
						<div class="input-group dateFieldHolder">
							<input type="text" name="sf_dateOrigPublished" id="sf_dateOrigPublished" value="#dateFormat(local.qrySubmissionDetail.dateOrigPublished,'m/d/yyyy')#" class="form-control dateControl" autocomplete="off">
							<div class="input-group-append">
								<span class="input-group-text cursor-pointer calendar-button" data-target="sf_dateOrigPublished"><i class="fa-solid fa-calendar"></i></span>
								<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('sf_dateOrigPublished');"><i class="fa-solid fa-circle-xmark"></i></a></span>
							</div>
							<label for="sf_dateOrigPublished">Date Originally Published</label>
						</div>
					</div>
				</div>
				<div class="form-group">
					<div class="form-label-group">
						<div class="input-group">
							<input type="text" name="sf_seminarLength" id="sf_seminarLength" value="#local.qrySubmissionDetail.seminarLength#" class="form-control" maxlength="4">
							<div class="input-group-append align-items-center">
								<span class="ml-1">minutes</span>
							</div>
							<label for="sf_seminarLength">Est. Time to Complete (in minutes)</label>
						</div>
					</div>
				</div>
				<div class="form-group mb-2">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_addLearningObjectives" id="sf_addLearningObjectives" value="1" class="custom-control-input">
						<label class="custom-control-label" for="sf_addLearningObjectives">I'd like to specify learning objectives for this program.</label>
					</div>
				</div>
				<div class="form-group sf_divLearningObjectives d-none">
					<div class="mt-3">
						<button type="button" id="sf_btnAddObjective" class="btn btn-link btn-sm pl-0" onclick="sf_addProgramObjective();">
							<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
							<span class="btn-wrapper--label">Add New Objective</span>
						</button>
					</div>
					<table class="table table-sm table-borderless table-hover table-striped mb-2" id="sf_tblObjectives">
						<thead>
							<tr><th width="40"></th><th colspan="3" class="p-2">Objectives</th></tr>
						</thead>
						<tbody class="sf_tbodyLearningObjectives">
						</tbody>
						<tbody class="sf_tbodyLearningObjectives_noRecords">
							<tr><td class="p-2" colspan="4">No entries.</td></tr>
						</tbody>
					</table>
				</div>
			</section>
			<!--- Step 2 --->
			<h3 class="font-weight-bold mb-3">Program Speakers</h3>
			<section>
				<div class="mt-2">
					<button type="button" id="sf_btnAddSpeaker" class="btn btn-link btn-sm pl-0" onclick="sf_addSWAuthor('Speaker');">
						<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
						<span class="btn-wrapper--label">Choose Speakers for this Program</span>
					</button>
				</div>
				<table class="table table-sm table-borderless table-hover table-striped mb-2" id="sf_tblSpeakers">
					<thead>
						<tr><th width="40"></th><th colspan="3" class="p-2">Speaker</th></tr>
					</thead>
					<tbody class="sf_tbodySpeakers">
					</tbody>
					<tbody class="sf_tbodySpeakers_noRecords">
						<tr><td class="p-2" colspan="4">None specified.</td></tr>
					</tbody>
				</table>
			</section>
			<!--- Step 3 --->
			<h3 class="font-weight-bold mb-3">Program Settings</h3>
			<section>
				<div class="form-group">
					<div class="form-label-group">
						<textarea id="sf_introMessageText" name="sf_introMessageText" class="form-control" rows="3">#local.qrySubmissionDetail.introMessageText#</textarea>
						<label for="sf_introMessageText">Program Introductory Message (optional)</label>
						<div class="text-dark">The program introductory message will be shown in the player above the course outline.</div>
					</div>
				</div>
				<div class="form-group">
					<div class="form-label-group">
						<textarea id="sf_endOfSeminarText" name="sf_endOfSeminarText" class="form-control" rows="3">#local.qrySubmissionDetail.endOfSeminarText#</textarea>
						<label for="sf_endOfSeminarText">Program Completion Message (optional)</label>
						<div class="text-dark">The program completion message will be shown to the registrant once the program is completed and a certificate is awarded.</div>
					</div>
				</div>
				<div class="form-group mb-2">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_blankOnInactivity" id="sf_blankOnInactivity" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.blankOnInactivity)> checked</cfif>>
						<label class="custom-control-label" for="sf_blankOnInactivity"><b>Inactivity:</b> Pause the program if a member clicks out of the program.</label>
					</div>
				</div>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_offerQA" id="sf_offerQA" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.offerQA)> checked</cfif>>
						<label class="custom-control-label" for="sf_offerQA"><b>Q&A Tab:</b> Offer the Q&A tab in the program player.</label>
					</div>
					<div class="form-group sf_divOfferQA d-none pl-5">
						<div class="alert alert-info my-2">Moderators will receive Q&A questions from members.</div>
						<div class="mt-2">
							<button type="button" id="sf_btnAddModerator" class="btn btn-link btn-sm pl-0" onclick="sf_addSWAuthor('Moderator');">
								<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
								<span class="btn-wrapper--label">Add Moderator</span>
							</button>
						</div>
						<table class="table table-sm table-borderless table-hover table-striped mb-2" id="sf_tblModerators">
							<thead>
								<tr><th width="40"></th><th colspan="3" class="p-2">Moderator</th></tr>
							</thead>
							<tbody class="sf_tbodyModerators">
							</tbody>
							<tbody class="sf_tbodyModerators_noRecords">
								<tr><td class="p-2" colspan="4">None specified.</td></tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_offerCertificate" id="sf_offerCertificate" value="1" class="custom-control-input"<cfif local.submissionID eq 0 or val(local.qrySubmissionDetail.offerCertificate)> checked</cfif>>
						<label class="custom-control-label" for="sf_offerCertificate"><b>Certificates:</b> Offer certificates for this program.</label>
					</div>
				</div>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_includeLinks" id="sf_includeLinks" value="1" class="custom-control-input">
						<label class="custom-control-label" for="sf_includeLinks"><b>Other Resources Tab:</b> Offer links to external sites on the Other Resources tab in the program player.</label>
					</div>
					<div class="form-group sf_divExternalLinks d-none pl-5">
						<div class="mt-2">
							<button type="button" id="sf_btnAddLink" class="btn btn-link btn-sm pl-0" onclick="sf_editLink('x');">
								<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
								<span class="btn-wrapper--label">Define New Link</span>
							</button>
						</div>
						<div id="sf_editLinkFormContainer" class="d-none">
						</div>
						<table class="table table-sm table-borderless table-hover table-striped mb-2" id="sf_tblExternalLinks">
							<thead>
								<tr><th width="40"></th><th colspan="3" class="p-2">Link</th></tr>
							</thead>
							<tbody class="sf_tbodyExternalLinks">
							</tbody>
							<tbody class="sf_tbodyExternalLinks_noRecords">
								<tr><td class="p-2" colspan="4">None defined.</td></tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_offerEval" id="sf_offerEval" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.offerEval)> checked</cfif>>
						<label class="custom-control-label" for="sf_offerEval"><b>Evaluation:</b> Offer an evaluation for this program.</label>
					</div>
					<div class="form-group sf_divOfferEvaluation d-none pl-5">
						<div class="card card-box mt-2">
							<div class="card-header bg-light py-2">
								<div class="card-header--title font-weight-bold font-size-sm">Choose Custom Evaluations for this Program</div>
							</div>
							<div class="card-body p-3" style="max-height:300px;overflow-y:auto;">
								<table class="table table-sm table-borderless table-hover table-striped" id="sf_tblEvaluations">
									<tbody class="sf_tbodyEvaluations"></tbody>
									<tbody class="sf_tbodyEvaluations_noRecords">
										<tr><td class="p-2" colspan="2">No custom evaluations are currently available. To request a custom evaluation for this program, upload a sample evaluation below.</td></tr>
									</tbody>
								</table>
							</div>
						</div>
						<cfif local.submissionID eq 0>
							<div class="font-weight-bold mt-3 mb-2">OR Upload a Custom Evaluation</div>
							<div class="d-flex align-items-center mb-2">
								<button type="button" name="btnSelectCustomEvaluationFile" id="btnSelectCustomEvaluationFile" class="btn btn-sm btn-secondary">
									<i class="fa-solid fa-file"></i> <span class="ml-2">Choose File</span>
								</button>
								<div class="ml-2 text-dark font-size-sm">Supported file types: #replace(local.customEvalFilesExtList,",",", ","all")#</div>
							</div>
							<div id="selectedCustomEvaluationFileName"></div>
						<cfelse>
							<div id="sf_uploadedFiles_customEval" class="sf_uploadedFiles mt-3"></div>
						</cfif>
					</div>
				</div>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_offerExam" id="sf_offerExam" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.offerExam)> checked</cfif>>
						<label class="custom-control-label" for="sf_offerExam"><b>Exam:</b> Offer an exam for this program.</label>
					</div>
					<div class="form-group sf_divOfferExam d-none pl-5 mt-2">
						<cfif local.submissionID eq 0>
							<div class="font-weight-bold mb-2">Upload Custom Exam</div>
							<div class="d-flex align-items-center mb-2">
								<button type="button" name="btnSelectCustomExamFile" id="btnSelectCustomExamFile" class="btn btn-sm btn-secondary">
									<i class="fa-solid fa-file"></i> <span class="ml-2">Choose File</span>
								</button>
								<div class="ml-2 text-dark font-size-sm">Supported file types: #replace(local.customExamFilesExtList,",",", ","all")#</div>
							</div>
							<div id="selectedCustomExamFileName"></div>
						<cfelse>
							<div id="sf_uploadedFiles_customExam" class="sf_uploadedFiles"></div>
						</cfif>
					</div>
				</div>
			</section>
			<!--- Step 4 --->
			<h3 class="font-weight-bold mb-3">Program Audio, Video, and Downloadable Files</h3>
			<section>
				<cfif local.submissionID eq 0>
					<div>You can upload audio or video files for the program as well as downloadable papers or other materials.</div>
					<div id="sf_programMaterialFilesUploader">
						<p>Your browser does not have HTML5 support.</p>
					</div>
					<div class="alert alert-info mx-2 mb-0">
						<strong><i class="fa-regular fa-circle-info"></i> &nbsp; Important</strong>
						<br/>
						You cannot upload a file larger than 2GB. If your files are too large, submit them <a target="_blank" href="https://spaces.hightail.com/uplink/MemberCentral">here</a> and skip this step.
						<br/>
						Supported file types: #replace(local.downloadableFilesExtList,",",", ","all")#.
					</div>
				<cfelse>
					<div id="sf_uploadedFiles_materials" class="sf_uploadedFiles"></div>
				</cfif>
			</section>
			<!--- Step 5 --->
			<h3 class="font-weight-bold mb-3">Catalog Details</h3>
			<section>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_allowCatalog" id="sf_allowCatalog" value="1" class="custom-control-input"<cfif local.submissionID eq 0 or len(local.qrySubmissionDetail.dateCatalogStart)> checked</cfif>>
						<label class="custom-control-label" for="sf_allowCatalog">I want to sell this program in the catalog.</label>
					</div>
				</div>
				<div class="form-group sf_divAllowCatalog mt-3 ml-5">
					<div class="form-row">
						<div class="col">
							<div class="form-label-group mb-0">
								<div class="input-group dateFieldHolder">
									<input type="text" name="sf_dateCatalogStart" id="sf_dateCatalogStart" value="#dateformat(local.qrySubmissionDetail.dateCatalogStart,"m/d/yyy")#" class="form-control dateControl">
									<div class="input-group-append">
										<span class="input-group-text cursor-pointer calendar-button" data-target="sf_dateCatalogStart"><i class="fa-solid fa-calendar"></i></span>
										<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('sf_dateCatalogStart');"><i class="fa-solid fa-circle-xmark"></i></a></span>
									</div>
									<label for="sf_dateCatalogStart">Start Date</label>
								</div>
							</div>
						</div>
						<div class="col">
							<div class="form-label-group mb-0">
								<div class="input-group dateFieldHolder">
									<input type="text" name="sf_dateCatalogEnd" id="sf_dateCatalogEnd" value="#dateformat(local.qrySubmissionDetail.dateCatalogEnd,"m/d/yyy")#" class="form-control dateControl">
									<div class="input-group-append">
										<span class="input-group-text cursor-pointer calendar-button" data-target="sf_dateCatalogEnd"><i class="fa-solid fa-calendar"></i></span>
										<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('sf_dateCatalogEnd');"><i class="fa-solid fa-circle-xmark"></i></a></span>
									</div>
									<label for="sf_dateCatalogEnd">End Date</label>
								</div>
							</div>
						</div>
						<div class="col-auto align-self-center">12:00AM CT</div>
					</div>
					<div class="custom-control custom-switch mt-3">
						<input type="checkbox" name="sf_isPriceBasedOnActual" id="sf_isPriceBasedOnActual" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.isPriceBasedOnActual)> checked</cfif>>
						<label class="custom-control-label" for="sf_isPriceBasedOnActual">Registrant must qualify for rate based on their group membership.</label>
					</div>
					<div class="sf_divProgramRates mt-3">
						<button type="button" id="sf_btnAddRate" class="btn btn-link btn-sm pl-0" onclick="sf_editRate('x');">
							<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
							<span class="btn-wrapper--label">Add Rate</span>
						</button>
						<button type="button" class="btn btn-link btn-sm pl-0" id="btnCopyRateFromProgram" onclick="copyRatesFromSWProgramPrompt('swsubmission');return false;">
							<span class="btn-wrapper--icon"><i class="fa-regular fa-copy"></i></span>
							<span class="btn-wrapper--label">Copy Rates from Other Program</span>
						</button>
						<div id="sf_editRateFormContainer" class="d-none">
						</div>
						<table class="table table-sm table-borderless table-hover table-striped mb-2" id="sf_tblProgramRates">
							<thead>
								<tr><th width="40"></th><th class="p-2">Rate Name</th><th class="p-2" colspan="3">Rate</th></tr>
							</thead>
							<tbody class="sf_tbodyProgramRates">
							</tbody>
							<tbody class="sf_tbodyProgramRates_noRecords">
								<tr><td class="p-2" colspan="5">No entries.</td></tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_allowSyndication" id="sf_allowSyndication" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.allowSyndication)> checked</cfif>>
						<label class="custom-control-label" for="sf_allowSyndication">I want to syndicate this program to other partner associations.</label>
					</div>
				</div>
				<div class="form-group sf_divAllowSyndication d-none mt-2 ml-5">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_createOwnPricingToOptIns" id="sf_createOwnPricingToOptIns" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.createOwnPricingToOptIns)> checked</cfif>>
						<label class="custom-control-label" for="sf_createOwnPricingToOptIns">I want to allow partner associations to create their own pricing.</label>
					</div>
					<div class="sf_divDefaultPricingToOptIns mt-2">
						<input type="hidden" name="sf_syndXML" id="sf_syndXML" value="">
						<button type="button" id="sf_btnAddSyndicatedPrice" class="btn btn-link btn-sm pl-0" onclick="sf_addSyndicatedPrice();">
							<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
							<span class="btn-wrapper--label">Add Syndicated Rate</span>
						</button>
						<div id="sf_editRateFormContainer" class="d-none">
						</div>
						<table class="table table-sm table-borderless table-hover table-striped mb-2" id="sf_tblSyndicatedPrices">
							<thead>
								<tr><th width="40"></th><th class="p-2">Rate Name</th><th class="p-2" colspan="3">Rate</th></tr>
							</thead>
							<tbody class="sf_tbodySyndicatedPrices">
							</tbody>
							<tbody class="sf_tbodySyndicatedPrices_noRecords">
								<tr><td class="p-2" colspan="5">No entries.</td></tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_isFeaturedProgram" id="sf_isFeaturedProgram" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.isFeatured)> checked</cfif>>
						<label class="custom-control-label" for="sf_isFeaturedProgram">Feature this program in the catalog.</label>
					</div>
				</div>
				<div class="form-group mb-2 mt-4 <cfif local.submissionID gt 0>d-none</cfif>">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_addNewProgramImg" id="sf_addNewProgramImg" value="1" class="custom-control-input"<cfif val(local.qrySubmissionDetail.isFeatured)> checked</cfif>>
						<label class="custom-control-label" for="sf_addNewProgramImg">Add a new program image.</label>
					</div>
				</div>
				<cfif local.submissionID eq 0>
					<div class="form-group sf_divFeaturedImage ml-5 mt-2 d-none">
							<div class="font-weight-bold mb-2">Upload Featured Image</div>
							<div class="d-flex align-items-center mb-2">
								<button type="button" name="sf_btnSelectFeaturedImage" id="sf_btnSelectFeaturedImage" class="btn btn-sm btn-secondary">
									<i class="fa-solid fa-file"></i> <span class="ml-2">Choose File</span>
								</button>
								<div class="ml-2 text-dark font-size-sm">Supported file types: #replace(local.ftdImageFilesExtList,",",", ","all")#</div>
							</div>
							<div id="sf_selectedFeaturedImageFileName"></div>
					</div>
				<cfelse>
					<div class="form-group sf_divFeaturedImage mt-4 d-none">
						<div id="sf_uploadedFiles_ftdImage" class="sf_uploadedFiles"></div>
					</div>
				</cfif>
				<div class="form-group mb-2 mt-4">
					<h6 class="font-weight-bold">Choose Subject Areas for this Program:<h6>
					<cfif arguments.event.getValue('mc_adminToolInfo.myRights.manageSWSettingsAll') is 1 or arguments.event.getValue('mc_adminToolInfo.myRights.manageSWSettingsOwn') is 1>
						<button type="button" id="sf_btnAddRate" class="btn btn-link btn-sm pl-0" onclick="sf_showSubjectAreaForm();">
							<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
							<span class="btn-wrapper--label">Add New Subject Area</span>
						</button>
						<div id="sf_divNewSubjectForm" class="d-none">
							<form name="sf_frmCategoryDetails" id="sf_frmCategoryDetails">
								<div class="card card-box my-2">
									<div class="card-header bg-light">
										<div class="card-header--title font-weight-bold font-size-sm">Create New Subject Area</div>
									</div>
									<div class="card-body p-3">
										<div class="form-group row no-gutters mt-2 d-flex align-items-center">
											<div class="col">
												<div class="form-label-group mb-0">
													<input type="text" name="sf_newCategoryName"  id="sf_newCategoryName" value="" class="form-control" maxlength="75"/>
													<label for="sf_newCategoryName">New Subject Area *</label>
												</div>
											</div>
											<div class="col-auto pl-2">
												<button type="button" name="sf_btnAddNewCategory" id="sf_btnAddNewCategory" class="btn btn-sm btn-primary" onclick="sf_addNewSubjectArea();">Add</button>
												<button type="button" class="btn btn-sm btn-secondary" onclick="sf_cancelSubjectAreaForm();">Cancel</button>
											</div>
										</div>
									</div>
								</div>
							</form>
						</div>
					</cfif>
					<div class="form-label-group mt-2">
						<select id="sf_categoryID" name="sf_categoryID" class="form-control form-control-sm" multiple data-toggle="custom-select2">
							<cfloop query="local.qryCategories">
								<option value="#local.qryCategories.categoryID#"<cfif listFindNoCase(local.qrySubmissionDetail.categoryIDs, local.qryCategories.categoryID)> selected</cfif>>#local.qryCategories.categoryName#</option>
							</cfloop>
						</select>
						<label for="sf_categoryID">Subject Areas</label>
					</div>
				</div>
			</section>
			<!--- Step 6 --->
			<h3 class="font-weight-bold mb-3">CE Credit</h3>
			<section>
				<div class="form-group mb-2 mt-4">
					<div class="custom-control custom-switch">
						<input type="checkbox" name="sf_offerCECredit" id="sf_offerCECredit" value="1" class="custom-control-input">
						<label class="custom-control-label" for="sf_offerCECredit">Offer credit for this program.</label>
					</div>
				</div>
				<div class="form-group sf_divCECredit d-none mt-3 ml-5">
					<div class="row no-gutters align-items-center mb-2" id="sf_addCreditSelectionContainer">
						<div class="col">
							<div class="form-label-group mb-0">
								<select id="sf_CSALinkID_x" name="sf_CSALinkID_x" class="form-control">
									<cfloop query="local.qrySponsorsAndAuthorities">
										<option value="#local.qrySponsorsAndAuthorities.CSALinkID#">#local.qrySponsorsAndAuthorities.jurisdiction# - #local.qrySponsorsAndAuthorities.authorityName# (via #local.qrySponsorsAndAuthorities.sponsorName#)</option>
									</cfloop>
								</select>
								<label for="sf_CSALinkID_x">Choose Sponsor/Authority</label>
							</div>
						</div>
						<div class="col-auto pl-2">
							<button type="button" id="sf_btnAddCECredit" class="btn btn-primary btn-sm" onclick="sf_editCECredit('x');">
								<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
								<span class="btn-wrapper--label">Add Credit</span>
							</button>
						</div>
					</div>
					<div id="sf_editCECreditFormContainer" class="mb-3 d-none"></div>
					<table class="table table-sm table-borderless table-hover table-striped mb-2" id="sf_tblCECredits">
						<thead>
							<tr><th width="40"></th><th class="p-2">Authority/Sponsor</th><th class="p-2">Settings</th><th class="p-2" colspan="3">Credits</th></tr>
						</thead>
						<tbody class="sf_tbodyCECredits">
						</tbody>
						<tbody class="sf_tbodyCECredits_noRecords">
							<tr><td class="p-2" colspan="5">No entries.</td></tr>
						</tbody>
					</table>
				</div>
			</section>
			<!--- Step 7 --->
			<h3 class="font-weight-bold mb-3">Final Notes</h3>
			<section>
				<div id="divFinalNotesSection" class="mt-3">
					<label for="sf_additionalInfo">Is there additional information SeminarWeb should know about this program?</label>
					<textarea id="sf_additionalInfo" name="sf_additionalInfo" class="form-control form-control-sm" rows="4">#local.qrySubmissionDetail.additionalInfo#</textarea>
					<cfif not local.readOnly>
						<div class="mt-2 alert d-flex align-items-center pl-2 align-content-center alert-info alert-dismissible fade show " role="alert">
							<span class="font-size-lg d-block d-40 text-center">
								<i class="fa-regular fa-circle-info"></i>
							</span>
							<span>
								Click the <strong>#local.submitButtonText#</strong> button below to <cfif local.submissionID gt 0>convert this submission into a seminar<cfelse>save the data</cfif>.
							</span>
						</div>
					</cfif>
				</div>
				<div id="divSubmitProgramFormArea" class="d-none"></div>
				<div id="divSubmitProgramLoading" class="d-none">
					<div class="text-center">
						<br/>
						<i class="fa-light fa-circle-notch fa-spin fa-3x"></i>
						<br/><br/>
						<b>Please wait while we validate and save the details.</b>
						<br/>
					</div>
				</div>
			</section>
			<div id="err_submitprogram" class="alert alert-danger mb-1 mt-3 d-none"></div>
		</div>
	</form>
</div>
<script id="sf_programObjective_rows" type="text/x-handlebars-template">
	{{##each arrobjectives}}
		<tr id="sf_trObj_{{autoid}}">
			<input type="hidden" name="sf_learnObjectiveID" id="sf_learnObjectiveID_{{autoid}}" value="{{autoid}}">
			<td class="text-right pr-2 indexVal text-grey"></td>
			<td class="objective py-2">
				<textarea name="sf_learnObjective_{{autoid}}" id="sf_learnObjective_{{autoid}}" rows="2" class="form-control form-control-sm sf_learnObjective">{{objective}}</textarea>
			</td>
			<td width="25"><i class="fa-solid fa-circle-minus fa-lg cursor-pointer sf-grid-action" onclick="sf_removeObjective({{autoid}})" title="Remove Objective from this Program"></i></td>
		</tr>
	{{/each}}
</script>
<script id="sf_speaker_add" type="text/html">
	<form name="frmAddSWSpeaker" id="frmAddSWSpeaker">
		<div id="err_addswspeaker" class="alert alert-danger mb-2 d-none"></div>
		<div class="form-label-group">
			<select name="sf_SWSpeaker" id="sf_SWSpeaker" class="custom-select"></select>
			<label for="sf_SWSpeaker">Choose Speaker</label>
		</div>
	</form>
</script>
<script id="sf_speaker_rows" type="text/x-handlebars-template">
	{{##each arrauthors}}
		<tr id="trAuthor_{{authorid}}" class="trAuthor">
			<input type="hidden" name="sf_speaker_authorID" id="sf_speaker_authorID_{{authorid}}" value="{{authorid}}">
			<td class="text-right pr-2 indexVal text-grey"></td>
			<td class="py-2 authorname">{{authorname}}</td>
			<td width="25"><i class="fa-solid fa-pencil fa-lg cursor-pointer sf-grid-action" onclick="sf_editSpeaker({{authorid}})" title="Edit this Speaker"></i></td>
			<td width="25"><i class="fa-solid fa-circle-minus fa-lg cursor-pointer sf-grid-action" onclick="sf_removeSpeaker({{authorid}})" title="Remove Speaker from this Program"></i></td>
		</tr>
	{{/each}}
</script>
<script id="sf_moderator_add" type="text/html">
	<form name="frmAddSWModerator" id="frmAddSWModerator">
		<div id="err_addswmoderator" class="alert alert-danger mb-2 d-none"></div>
		<div class="form-label-group">
			<select name="sf_SWModerator" id="sf_SWModerator" class="custom-select"></select>
			<label for="sf_SWModerator">Choose Moderator</label>
		</div>
	</form>
</script>
<script id="sf_moderator_rows" type="text/x-handlebars-template">
	{{##each arrauthors}}
		<tr id="trAuthor_{{authorid}}" class="trAuthor">
			<input type="hidden" name="sf_moderator_authorID" id="sf_moderator_authorID_{{authorid}}" value="{{authorid}}">
			<td class="text-right pr-2 indexVal text-grey"></td>
			<td class="py-2 authorname">{{authorname}}</td>
			<td width="25"><i class="fa-solid fa-pencil fa-lg cursor-pointer sf-grid-action" onclick="sf_editModerator({{authorid}})" title="Edit this Moderator"></i></td>
			<td width="25"><i class="fa-solid fa-circle-minus fa-lg cursor-pointer sf-grid-action" onclick="sf_removeModerator({{authorid}})" title="Remove Moderator from this Program"></i></td>
		</tr>
	{{/each}}
</script>
<script id="sf_externalLink_add" type="text/x-handlebars-template">
	<div class="card card-box my-2">
		<div class="card-header bg-light py-2">
			<div class="card-header--title font-weight-bold font-size-sm">{{##compare autoid '==' "x"}}Add{{/compare}}{{##compare autoid '!=' "x"}}Update{{/compare}} Link</div>
		</div>
		<div class="card-body py-3">
			<div id="sf_err_link" class="alert alert-danger mb-2 d-none"></div>
			<div class="externalLink pb-2">
				<div class="form-label-group">
					<input type="text" name="sf_linkURL" id="sf_linkURL" value="{{linkurl}}" class="form-control" maxlength="250">
					<label for="sf_linkURL">URL</label>
				</div>
				<div class="form-label-group">
					<input type="text" name="sf_linkName" id="sf_linkName" value="{{linkname}}" class="form-control" maxlength="250">
					<label for="sf_linkName">Clickable Text</label>
				</div>
			</div>
			<div class="mt-2">
				<button type="button" id="btnSaveLink" class="btn btn-sm btn-secondary" onclick="sf_saveLink('{{autoid}}');">
					<span class="btn-wrapper--icon"><i class="fa fa-floppy-disk fa-lg"></i></span>
					<span class="btn-wrapper--label">Save</span>
				</button>
				<button type="button" id="btnCancelLink" class="btn btn-sm btn-secondary" onclick="sf_cancelLink('{{autoid}}');">Cancel</button>
			</div>
		</div>
	</div>
</script>
<script id="sf_externalLink_rows" type="text/x-handlebars-template">
	{{##each arrlinks}}
		<tr id="sf_trLink_{{autoid}}">
			<input type="hidden" name="sf_linkID" id="sf_linkID_{{autoid}}" value="{{autoid}}">
			<input type="hidden" name="sf_linkURL_{{autoid}}" id="sf_linkURL_{{autoid}}" value="{{linkurl}}">
			<input type="hidden" name="sf_linkName_{{autoid}}" id="sf_linkName_{{autoid}}" value="{{linkname}}">
			<td class="text-right pr-2 indexVal text-grey"></td>
			<td class="externalLink py-2 pl-3">
				{{linkname}}
				<div class="text-dim small"><a href="{{linkurl}}" target="_blank">{{linkurl}}</a></div>
			</td>
			<td width="25"><i class="fa-solid fa-pencil fa-lg cursor-pointer sf-grid-action" onclick="sf_editLink({{autoid}})" title="Edit"></i></td>
			<td width="25"><i class="fa-solid fa-circle-minus fa-lg cursor-pointer sf-grid-action" onclick="sf_removeLink({{autoid}})" title="Remove"></i></td>
		</tr>
	{{/each}}
</script>
<script id="sf_evaluation_rows" type="text/x-handlebars-template">
	{{##compare arrorgforms.length '>' 0}}
		{{##each arrorgforms}}
			<tr id="trEvaluation_{{formid}}" class="trEvaluation">
				<td width="40" class="text-center">
					<input type="checkbox" name="sf_formID" id="sf_formID_{{formid}}" value="{{formid}}"{{##if selected}} checked{{/if}}>
				</td>
				<td><label for="sf_formID_{{formid}}" class="my-1">{{formtitle}}</label></td>
			</tr>
		{{/each}}
	{{/compare}}
</script>
<script id="sf_programRate_add" type="text/x-handlebars-template">
	<div class="card card-box mt-2">
		<div class="card-header bg-light py-2">
			<div class="card-header--title font-weight-bold font-size-sm">{{##compare autoid '==' "x"}}Define{{/compare}}{{##compare autoid '!=' "x"}}Update{{/compare}} Rate</div>
		</div>
		<div class="card-body py-3">
			<div id="sf_err_rate" class="alert alert-danger mb-2 d-none"></div>
			<div class="row no-gutters">
				<div class="col-6">
					<div class="form-label-group">
						<input type="text" name="sf_rateName" id="sf_rateName" value="{{ratename}}" class="form-control" maxlength="250">
						<label for="sf_rateName">Name of Rate</label>
					</div>
				</div>
				<div class="col-6 pl-2">
					<div class="input-group flex-nowrap">
						<div class="input-group-prepend">
							<span class="input-group-text">$</span>
						</div>
						<div class="form-label-group flex-grow-1 mb-0">
							<input type="text" name="sf_rate" id="sf_rate" value="{{rate}}" class="form-control" onblur="if (this.value.length > 0) this.value=formatCurrency(this.value);" autocomplete="off">
							<label for="sf_rate">Rate</label>
						</div>
					</div>
				</div>
			</div>
			<cfloop list="rategroups,deniedrategroups" item="local.groupMode">
				<cfset local.dropdownID = (local.groupMode == "rategroups" ? "sf_rateGroupID" : "sf_deniedRateGroupID")>
				<cfset local.dropdownLabel = (local.groupMode == "rategroups" ? "Choose Group(s) that Qualify for Rate" : "Choose Group(s) that are Denied Rate")>
				<div class="form-group fs_ratePermissionGroupsSection d-none">
					<div class="form-label-group">
						<select id="#local.dropdownID#" name="#local.dropdownID#" class="form-control form-control-sm" multiple data-toggle="custom-select2">
							<optgroup label="System Groups">
							<cfloop query="local.qryGroupsSYS">
								<cfset local.tmpOptName = local.qryGroupsSYS.thePathExpanded>
								<cfif len(local.qryGroupsSYS.groupDesc)>
									<cfset local.tmpOptName = local.tmpOptName & " (#local.qryGroupsSYS.groupDesc#)">
								</cfif>
								<option value="#local.qryGroupsSYS.groupID#">#left(local.tmpOptName,100)# <cfif len(local.tmpOptName) gt 100>...</cfif>&nbsp;</option>
							</cfloop>
							</optgroup>
							<optgroup label="Top 10 Most Frequently Used Groups">
							<cfloop query="local.qryFrequentGroups">
								<cfset local.tmpOptName = local.qryFrequentGroups.thePathExpanded>
								<cfif len(local.qryFrequentGroups.groupDesc)>
									<cfset local.tmpOptName = local.tmpOptName & " (#local.qryFrequentGroups.groupDesc#)">
								</cfif>
								<option value="#local.qryFrequentGroups.groupID#">#left(local.tmpOptName,100)# <cfif len(local.tmpOptName) gt 100>...</cfif>&nbsp;</option>
							</cfloop>
							</optgroup>
							<optgroup label="All of Your Groups">
							<cfloop query="local.qryGroupsNonSYS">
								<cfset local.tmpOptName = local.qryGroupsNonSYS.thePathExpanded>
								<cfif len(local.qryGroupsNonSYS.groupDesc)>
									<cfset local.tmpOptName = local.tmpOptName & " (#local.qryGroupsNonSYS.groupDesc#)">
								</cfif>
								<option value="#local.qryGroupsNonSYS.groupID#">#left(local.tmpOptName,100)# <cfif len(local.tmpOptName) gt 100>...</cfif>&nbsp;</option>
							</cfloop>
							</optgroup>
						</select>
						<label for="#local.dropdownID#">#local.dropdownLabel#</label>
					</div>
				</div>
			</cfloop>

			<div class="mt-2">
				<button type="button" id="btnSaveRate" class="btn btn-sm btn-secondary" onclick="sf_saveRate('{{autoid}}');">
					<span class="btn-wrapper--icon"><i class="fa fa-floppy-disk fa-lg"></i></span>
					<span class="btn-wrapper--label">Save</span>
				</button>
				<button type="button" id="btnCancelRate" class="btn btn-sm btn-secondary" onclick="sf_cancelRate('{{autoid}}');">Cancel</button>
			</div>
		</div>
	</div>
</script>
<script id="sf_programRate_rows" type="text/x-handlebars-template">
	{{##each arrrates}}
		<tr id="sf_trProgramRate_{{autoid}}">
			<input type="hidden" name="sf_rateID" id="sf_rateID_{{autoid}}" value="{{autoid}}">
			<input type="hidden" name="sf_rateName_{{autoid}}" id="sf_rateName_{{autoid}}" value="{{ratename}}">
			<input type="hidden" name="sf_rate_{{autoid}}" id="sf_rate_{{autoid}}" value="{{rate}}">
			<input type="hidden" name="sf_rateGroupIDs_{{autoid}}" id="sf_rateGroupIDs_{{autoid}}" value="{{groupids}}">
			<input type="hidden" name="sf_deniedRateGroupIDs_{{autoid}}" id="sf_deniedRateGroupIDs_{{autoid}}" value="{{deniedgroupids}}">
			<td class="text-right pr-2 indexVal text-grey align-top"></td>
			<td class="py-2 pl-3 align-top">
				{{ratename}}
				{{##compare arrgroupnames.length '!=' 0}}
					<div class="mt-1 pl-2 font-size-sm fs_ratePermissionGroupsSection">
						{{##each arrgroupnames}}
							<i class="fa-solid fa-users fa-sm"></i> {{this}}</br/>
						{{/each}}
					</div>
				{{/compare}}
			</td>
			<td class="py-2 pl-3 align-top">{{rate}}</td>
			<td width="25" class="align-top"><i class="fa-solid fa-pencil fa-lg cursor-pointer sf-grid-action" onclick="sf_editRate({{autoid}})" title="Edit"></i></td>
			<td width="25" class="align-top"><i class="fa-solid fa-circle-minus fa-lg cursor-pointer sf-grid-action" onclick="sf_removeRate({{autoid}})" title="Remove"></i></td>
		</tr>
	{{/each}}
</script>
<script id="sf_syndicatedPrice_rows" type="text/x-handlebars-template">
	{{##each arrprices}}
		<tr id="sf_trSyndicatedPrice_{{autoid}}">
			<input type="hidden" name="sf_syndPriceID" id="sf_syndPriceID_{{autoid}}" value="{{autoid}}">
			<td class="text-right pr-2 indexVal text-grey"></td>
			<td class="py-1">
				<input type="text" name="sf_syndGroup_{{autoid}}" id="sf_syndGroup_{{autoid}}" value="{{pricecategory}}" class="form-control form-control-sm">
			</td>
			<td class="py-1">
				<input type="text" name="sf_syndPrice_{{autoid}}" id="sf_syndPrice_{{autoid}}" value="{{price}}" class="form-control form-control-sm" onblur="if (this.value.length > 0) this.value=formatCurrency(this.value);">
			</td>
			<td width="25"><i class="fa-solid fa-circle-minus fa-lg cursor-pointer sf-grid-action" onclick="sf_removeSyndicatedPrice({{autoid}})" title="Remove"></i></td>
		</tr>
	{{/each}}
</script>
<script id="sf_CECredit_add" type="text/x-handlebars-template">
	<div class="card card-box my-2">
		<div class="card-header bg-light py-2">
			<div class="card-header--title font-weight-bold font-size-sm">
				{{authoritycode}} - {{authorityname}}
				<div class="text-grey ml-2">{{sponsorname}}</div>
			</div>
			<div>
				<button type="button" id="btnSaveCredit" class="btn btn-sm btn-secondary" onclick="sf_saveCECredit('{{autoid}}');">
					<span class="btn-wrapper--icon"><i class="fa fa-floppy-disk fa-lg"></i></span>
					<span class="btn-wrapper--label">{{##compare autoid '==' "x"}}Add{{/compare}}{{##compare autoid '!=' "x"}}Save{{/compare}}</span>
				</button>
				<button type="button" id="btnCancelCredit" class="btn btn-sm btn-secondary" onclick="sf_cancelCECredit('{{autoid}}');">Cancel</button>
			</div>
		</div>
		<div class="card-body py-3">
			<div id="sf_err_credit" class="alert alert-danger mb-2 d-none"></div>
			<input type="hidden" name="sf_CECredit_actionMode" id="sf_CECredit_actionMode" value="{{##compare autoid '==' "x"}}Add{{/compare}}{{##compare autoid '!=' "x"}}Update{{/compare}}">
			<input type="hidden" name="sf_CSALinkID" id="sf_CSALinkID" value="{{csalinkid}}">
			<input type="hidden" name="sf_CECredit_authorityCode" id="sf_CECredit_authorityCode" value="{{authoritycode}}">
			<input type="hidden" name="sf_CECredit_authorityName" id="sf_CECredit_authorityName" value="{{authorityname}}">
			<input type="hidden" name="sf_CECredit_sponsorName" id="sf_CECredit_sponsorName" value="{{sponsorname}}">
			<div class="form-group row">
				<div class="col-md-6">
					<div class="form-group mb-2">
						<label for="sf_creditStatusID">Status:</label>
						<select name="sf_creditStatusID" id="sf_creditStatusID" class="form-control form-control-sm">
							<cfloop query="local.qryCreditStatuses">
								<option value="#local.qryCreditStatuses.statusID#" {{##compare autoid '==' "x"}}{{##compare '#local.qryCreditStatuses.status#' '==' 'Not Submitted'}} selected{{/compare}}{{/compare}}{{##compare autoid '!=' "x"}}{{##compare statusid '==' #local.qryCreditStatuses.statusID#}} selected{{/compare}}{{/compare}}>#local.qryCreditStatuses.status#</option>
							</cfloop>
						</select>
					</div>
					<label for="sf_creditOfferedStartDate">Credit Offered between:</label>
					<div class="form-group row no-gutters">
						<div class="col-6 form-group pr-1">
							<div class="input-group input-group-sm">
								<input type="text" name="sf_creditOfferedStartDate" id="sf_creditOfferedStartDate" value="{{offeredstartdate}}" class="form-control form-control-sm dateControl" placeholder="Offered from">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="sf_creditOfferedStartDate"><i class="fa-solid fa-calendar"></i></span>
								</div>
							</div>
						</div>
						<div class="col-6 form-group pl-1">
							<div class="input-group input-group-sm">
								<input type="text" name="sf_creditOfferedEndDate" id="sf_creditOfferedEndDate" value="{{offeredenddate}}" class="form-control form-control-sm dateControl" placeholder="Offered to">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="sf_creditOfferedEndDate"><i class="fa-solid fa-calendar"></i></span>
								</div>
							</div>
						</div>
					</div>
					<div class="form-group row mb-3 mt-2">
						<div class="col-12 form-group">
							<label for="sf_creditCompleteByDate">In order to earn credit, this program must be completed by:</label>
						</div>
						<div class="col-6 form-group pr-1">
							<div class="input-group input-group-sm">
								<input type="text" name="sf_creditCompleteByDate" id="sf_creditCompleteByDate" value="{{completebydate}}" class="form-control form-control-sm dateControl" placeholder="Complete by">
								<div class="input-group-append">
									<span class="input-group-text cursor-pointer calendar-button" data-target="sf_creditCompleteByDate"><i class="fa-solid fa-calendar"></i></span>
								</div>
							</div>
						</div>
					</div>
					<div class="form-group my-4">
						<div class="custom-control custom-switch">
							<input type="checkbox" name="sf_isCreditRequired" id="sf_isCreditRequired" value="1" class="custom-control-input"{{##compare iscreditrequired '==' 1}} checked{{/compare}}>
							<label class="custom-control-label" for="sf_isCreditRequired">Registrants are required to select this credit when registering.</label>
						</div>
					</div>
					<div class="form-group mt-2 d-flex align-items-center">
						<label for="sf_creditApprovalNum" class="text-nowrap mr-2 mb-0">Accrediting Authority Approval Num:</label>
						<input type="text" name="sf_creditApprovalNum" id="sf_creditApprovalNum" value="{{creditapprovalnum}}" class="form-control form-control-sm" maxlength="80">
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label>Credits offered:</label>
						<div>
							{{##each arrcredittypes}}
								<div class="mb-1">
									<input type="text" name="sf_xcredit_{{fieldname}}" id="sf_xcredit_{{fieldname}}" value="{{creditvalue}}" class="form-control form-control-sm sf_credVal d-inline" style="width:70px;" data-fieldname="{{fieldname}}" data-displayname="{{displayname}}">
									<label for="sf_xcredit_{{fieldname}}" class="d-inline-block">{{displayname}}</label>
								</div>
							{{/each}}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</script>
<script id="sf_CECredit_rows" type="text/x-handlebars-template">
	{{##each arrcecredits}}
		<tr id="sf_trCECredit_{{autoid}}">
			<input type="hidden" name="sf_CECreditID" value="{{autoid}}">
			<input type="hidden" name="sf_CSALinkID_{{autoid}}" id="sf_CSALinkID_{{autoid}}" value="{{csalinkid}}">
			<input type="hidden" name="sf_creditStatusID_{{autoid}}" id="sf_creditStatusID_{{autoid}}" value="{{statusid}}">
			<input type="hidden" name="sf_creditOfferedStartDate_{{autoid}}" id="sf_creditOfferedStartDate_{{autoid}}" value="{{offeredstartdate}}">
			<input type="hidden" name="sf_creditOfferedEndDate_{{autoid}}" id="sf_creditOfferedEndDate_{{autoid}}" value="{{offeredenddate}}">
			<input type="hidden" name="sf_creditCompleteByDate_{{autoid}}" id="sf_creditCompleteByDate_{{autoid}}" value="{{completebydate}}">
			<input type="hidden" name="sf_isCreditRequired_{{autoid}}" id="sf_isCreditRequired_{{autoid}}" value="{{iscreditrequired}}">
			<input type="hidden" name="sf_creditApprovalNum_{{autoid}}" id="sf_creditApprovalNum_{{autoid}}" value="{{creditapprovalnum}}">
			{{##each arrcreditvalues}}
				<input type="hidden" name="sf_xcredit_{{../autoid}}_{{fieldname}}" id="sf_xcredit_{{../autoid}}_{{fieldname}}" value="{{value}}" class="sf_credVal{{../autoid}}">
			{{/each}}

			<td class="text-right pr-2 indexVal text-grey"></td>
			<td class="py-2 pl-3">
				<span class="font-weight-bold">{{authoritycode}} - {{authorityname}}</span>
				<div class="text-grey font-weight-bold ml-2">{{sponsorname}}</div>
			</td>
			<td class="py-2 pl-3">
				<div class="font-weight-bold ">{{statusname}}</div>
				{{##compare creditapprovalnum.length '>' 0}}
					<div>Approval Num: {{creditapprovalnum}}</div>
				{{/compare}}
				<div>
					{{##compare offeredstartdate.length '||' offeredenddate.length}}
						Offered {{##compare offeredstartdate.length '>' 0}}from {{offeredstartdate}} {{/compare}}{{##compare offeredenddate.length '>' 0}}to {{offeredenddate}}{{/compare}}<br/>
					{{/compare}}
					{{##compare completebydate.length '>' 0}}Complete by {{completebydate}}{{/compare}}
				</div>
			</td>
			<td class="py-2 pl-3">
				<div class="d-block d-md-none font-weight-bold mb-1">Credits:</div>
				{{##each arrcreditvalues}}
					<div class="mb-1">
						<span>{{value}}</span>
						<span>{{displayname}}</span>
					</div>
				{{/each}}
				{{##compare arrcreditvalues.length '==' 0}}
					<div class="font-weight-bold text-danger">Not defined</div>
				{{/compare}}
			</td>
			<td width="25"><i class="fa-solid fa-pencil fa-lg cursor-pointer sf-grid-action" onclick="sf_editCECredit({{autoid}})" title="Edit"></i></td>
			<td width="25"><i class="fa-solid fa-circle-minus fa-lg cursor-pointer sf-grid-action" onclick="sf_removeCECredit({{autoid}})" title="Remove"></i></td>
		</tr>
	{{/each}}
</script>
<script id="sf_fileuploads" type="text/x-handlebars-template">
	{{##compare arruploads.length '>' 0}}
		<div class="font-weight-bold mb-2">{{header}}</div>
		{{##each arruploads}}
			<a href="{{s3url}}" target="_blank" class="btn btn-link pl-0 pb-0 downloadLink"><i class="fa-solid fa-download"></i> {{##if ../showfilename}}{{filename}}{{else}}{{../linktext}}{{/if}}</a>
			{{##compare ../arruploads.length '>' 1}}<br/>{{/compare}}
		{{/each}}
		<cfif not local.hasProgramFeaturedImageConfig>
			{{##compare type '==' 'ftdimage'}}
				<div class="alert alert-info mt-2">
					Program Featured Image Configuration is not set for the publishing site. So this image will need to be uploaded manually once seminar is created and image configuration is set.
				</div>
			{{/compare}}
		</cfif>
	{{/compare}}
	{{##compare arruploads.length '==' 0}}
		<div class="alert alert-info">{{nofiletext}}.</div>
	{{/compare}}
</script>
<script id="sf_alertsubmitsuccess" type="text/html">
	<div class="alert alert-success d-flex align-items-center align-content-center my-2 pl-2" role="alert">
		<span class="font-size-lg d-block d-40 text-center">
			<i class="fa-regular fa-circle-check"></i>
		</span>
		<span>
			The program has been submitted successfully! A member of our team will follow up on your submission within 2 business days.
			{{errormessages}}
		</span>
	</div>
	<div class="alert alert-danger my-2 pl-2" role="alert">
		<div class="d-flex align-items-center align-content-center">
			<span class="font-size-lg d-block d-40 text-center">
				<i class="fa-regular fa-circle-info"></i>
			</span>
			<span>To submit additional files for this program, click here: <a href="https://spaces.hightail.com/uplink/membercentral" target="_blank">spaces.hightail.com/uplink/membercentral</a></span>
		</div>
		<div class="d-flex align-items-center align-content-center">
			<span class="font-size-lg d-block d-40 text-center">
				<i class="fa-regular fa-hand-point-right"></i>
			</span>
			<span><strong>IMPORTANT:</strong> Include program name in the subject line to ensure files are added to the correct program.</span>
		</div>
	</div>
</script>
</cfoutput>